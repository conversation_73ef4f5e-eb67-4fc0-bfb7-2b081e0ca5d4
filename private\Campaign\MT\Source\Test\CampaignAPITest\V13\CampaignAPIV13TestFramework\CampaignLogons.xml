﻿<?xml version="1.0" encoding="utf-8"?>
<EnvironmentList>
  <Environment Name="LocalApps">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://localhost:10874/CampaignManagementService13.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://by2adck585:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://localhost:10874/BulkService13.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://BY2ADCK162:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://localhost:10870/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://localhost:10874/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\BY2ADCK162\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\BY2ADCK162\d$\Advertiser\Campaign\API\APIV8\Log</APILogFolders>
    <CustomerList>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True">
            <UserName>wODe5Edvv</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4311735</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True" YahooUser="True">
            <UserName>WNhCllxfwRbHS1gF4goi</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4214832</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>    
      <!-- Yahoopush flow -->
      <!--  user id = 1495974 -->
      <Customer Id="9071907" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Type="ApiUser" Default="true">
            <UserName>9130678ea48dd20d</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1412999</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in Pace-->
      <Customer Id="4150092" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>9190d4087fc46e4f</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1931884</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ADCL024;Initial Catalog=Adcenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ADCL006;Initial Catalog=AdCenter_Campaign_P98;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCL023;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="appsdevdev">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://appsdevdev:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://appsdevdev:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://appsdevdev:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://BY2ADCK162:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://clientcentermt.redmond.corp.microsoft.com:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://appsdevdev:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\appsdevdev\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\appsdevdev\d$\Advertiser\Campaign\API\APIV8\Log</APILogFolders>
    <CustomerList>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True">
            <UserName>wODe5Edvv</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4311735</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True" YahooUser="True">
            <UserName>WNhCllxfwRbHS1gF4goi</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4214832</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="9071907" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Type="ApiUser" Default="true">
            <UserName>9130678ea48dd20d</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1412999</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in Pace-->
      <Customer Id="4150092" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>9190d4087fc46e4f</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1931884</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ADCL024;Initial Catalog=Adcenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ADCL006;Initial Catalog=AdCenter_Campaign_P98;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCL023;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-AKS">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2_campaignapi_aks.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c2_campaignapi_aks.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ASICMPSQL6\BY2ASICMPSQL6;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCF085\BY2SI2K8EDTSQL2;Initial Catalog=AdCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- Yahoopush flow -->
      <!--  user id = 2110652 -->
      <Customer Id="5079691" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>ae36ad25d4633149</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1687519</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in SI-->
      <Customer Id="5079623" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>a8ed306639c986ca</UserName>
            <PassWord></PassWord>
            <AccessKey>R5S2IIQ2D05O</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1686682</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Default="true">
            <UserName>048b4c4b05264f15b530</UserName>
            <PassWord></PassWord>
            <AccessKey>260J54N1</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1622399</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ASICMPSQL6\BY2ASICMPSQL6;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCF085\BY2SI2K8EDTSQL2;Initial Catalog=AdCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-LDC2-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <!--<CampaignManagementUrl>https://api.si.yahoo.adcenterapi.microsoft.com/Api/Advertiser/v13beta/CampaignManagement/CampaignManagementService.svc</CampaignManagementUrl>-->
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://si.clientcenter.microsoft-int.com:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://by2apse059:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- Yahoopush flow -->
      <!--  user id = 2110652 -->
      <Customer Id="5079691" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>ae36ad25d4633149</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1687519</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in SI-->
      <Customer Id="5079623" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>a8ed306639c986ca</UserName>
            <PassWord></PassWord>
            <AccessKey>R5S2IIQ2D05O</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1686682</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Default="true">
            <UserName>048b4c4b05264f15b530</UserName>
            <PassWord></PassWord>
            <AccessKey>260J54N1</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1622399</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ASICMPSQL6\BY2ASICMPSQL6;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCF085\BY2SI2K8EDTSQL2;Initial Catalog=AdCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-LDC1-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <!--<CampaignManagementUrl>https://api.si.yahoo.adcenterapi.microsoft.com/Api/Advertiser/v13beta/CampaignManagement/CampaignManagementService.svc</CampaignManagementUrl>-->
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- Yahoopush flow -->
      <!--  user id = 2110652 -->
      <Customer Id="5079691" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>ae36ad25d4633149</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1687519</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in SI-->
      <Customer Id="5079623" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>a8ed306639c986ca</UserName>
            <PassWord></PassWord>
            <AccessKey>R5S2IIQ2D05O</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1686682</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Default="true">
            <UserName>048b4c4b05264f15b530</UserName>
            <PassWord></PassWord>
            <AccessKey>260J54N1</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1622399</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ASICMPSQL6\BY2ASICMPSQL6;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCF085\BY2SI2K8EDTSQL2;Initial Catalog=AdCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-Sandbox">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaign.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://bulk.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://sandbox.api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://si.clientcenter.microsoft-int.com:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://by2apse059:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <!--Sandbox customer USERID:3037411-->
      <Customer Id="********" Default="true" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="true" Type="ApiUser" Default="true">
            <UserName>8e86dfb6200c4116a0e1</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2748412</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--Sandbox customer USERID:3037411-->
      <Customer Id="********" Default="true" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="true" Type="ApiUser" Default="true">
            <UserName>7AJc0mcaq9ZeAPFK</UserName>
            <PassWord></PassWord>
            <AccessKey>92JVA11Y9</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1556333</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCF085\BY2SI2K8EDTSQL2;Initial Catalog=AdCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="app-services-ci-labman">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <AdIntelligenceServiceUrl>http://{localhost}:8080/Api/Advertiser/v8/CampaignManagement/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CCMTEndPoint>https://{localhost}.redmond.corp.microsoft.com:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://{localhost}:10096/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MDSEndPoint>net.tcp://{localhost}:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=localhost;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=localhost;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source={localhost};Initial Catalog=customerdb;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source={localhost};Initial Catalog=Adcenter_Campaign_p1;Integrated Security=True</CampaignDB>
      <MTDB>Data Source={localhost};Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <!-- Used by CI -->
  <Environment Name="Local">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://apps-ci-box02.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://apps-ci-box02.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://apps-ci-box02.redmond.corp.microsoft.com/CampaignApi/BulkService.svc</BulkServiceUrl>
    <AdIntelligenceServiceUrl>http://localhost:34011/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm04:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <!-- Todo: Modify CI script to https. CI script replaces the protocol and port.  -->
    <CCMTEndPoint>net.tcp://apps-ci-box02.redmond.corp.microsoft.com:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL18;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPMTZDSQL18;Initial Catalog=Adcenter_Campaign_p6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL18;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="DevDev.v13">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <!--<CampaignManagementUrl>http://advertisertest2.redmond.corp.microsoft.com:809/Api/Advertiser/May-v13/CampaignManagementService9.svc</CampaignManagementUrl>-->
    <!--<CampaignManagementUrl>http://advertisertest2.redmond.corp.microsoft.com:809/Api/Advertiser/Mayv13/CampaignManagementService9.svc</CampaignManagementUrl>-->
    <CampaignManagementUrl>http://advertisertest2.redmond.corp.microsoft.com:809/Api/Advertiser/Mayv13t1/CampaignManagementService9.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <!--<BulkServiceUrl>http://advertisertest2.redmond.corp.microsoft.com:809/Api/Advertiser/May-v13/BulkService.svc</BulkServiceUrl>-->
    <BulkServiceUrl>http://advertisertest2.redmond.corp.microsoft.com:809/Api/Advertiser/Mayv13t1/BulkService9.svc</BulkServiceUrl>
    <CCMTEndPoint>net.tcp://SAAPPDEVHYP040:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL18;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPMTZDSQL18;Initial Catalog=Adcenter_Campaign_p6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL18;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Prod">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <!-- for new data center-->
    <!--<CampaignManagementUrl>https://main.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>-->
    <CampaignManagementUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <!-- for new data center-->
    <CampaignManagementRestUrl>https://main.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <!--<CampaignManagementRestUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>-->
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://cpmtzdwb04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://CH1ADCPAPIU08:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264388" TrustRating="0" Type="Premium" Default="true" Partition="5" PilotEnabled="True" KeywordMatchTypeEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Prod-Beta">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://bcp.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://cpmtzdwb04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Prod-BCP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://cpmtzdwb04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Prod-Offline">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://BY2ADCAPI01/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://CH1ADCAPI08/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://CH1ADCAPI08/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://CH1ADCAPI08/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://cpmtzdwb04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://CH1ADCK211:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264390" TrustRating="2" Type="Premium" Default="true" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>     
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
    <Offline-Servers>
      <!--Increase servercount and add neew servers as needed-->
      <ServerCount>10</ServerCount>
      <Server1 Name="BY2ADCAPI21"></Server1>
      <Server2 Name="BY2ADCAPI22"></Server2>
      <Server3 Name="BY2ADCAPI23"></Server3>
      <Server4 Name="BY2ADCAPI24"></Server4>
      <Server5 Name="BY2ADCAPI25"></Server5>
      <Server6 Name="BY2ADCAPI26"></Server6>
      <Server7 Name="BY2ADCAPI27"></Server7>
      <Server8 Name="BY2ADCAPI28"></Server8>
      <Server9 Name="BY2ADCAPI29"></Server9>
      <Server10 Name="BY2ADCAPI30"></Server10>
      <Server11 Name="BY2ADCAPI30"></Server11>
    </Offline-Servers>
  </Environment>
  <Environment Name="pace3_LDC2">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://by2adck585:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://by2adck585:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://by2adck585:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <!--<AdIntelligenceServiceUrl>http://BY2ADCK590:8080/Api/Advertiser/CampaignManagement/V13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>-->
    <MDSEndPoint>net.tcp://BY2ADCK162:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://BY2ADCVM332.phx.gbl:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://by2adck585:10094/CampaignMT/v6/CampaignService.svc </CMMTEndPoint>
    <!-- comma delimited folders without dates. If there is a central folder location then add the paths until the server name, include all 3 central folders -->
    <MTLogFolders>\\BY2ADCK162\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\BY2ADCK162\d$\Advertiser\Campaign\API\APIV8\Log</APILogFolders>
    <CustomerList>     
      <!--add a customer here to bypas KWMT issue on customer 7079836 bug 860061 -->
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True">
            <UserName>wODe5Edvv</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4311735</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="98" Default="true" PilotEnabled="True" Merchant="DirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" AdultRoleEnabled="True" YahooUser="True">
            <UserName>WNhCllxfwRbHS1gF4goi</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>4214832</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>     
      <!--  user id = 1495974 -->
      <Customer Id="9071907" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="True" Type="ApiUser" Default="true">
            <UserName>9130678ea48dd20d</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1412999</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in Pace-->
      <Customer Id="4150092" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>9190d4087fc46e4f</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1931884</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ADCL024;Initial Catalog=Adcenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ADCL006;Initial Catalog=AdCenter_Campaign_P98;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2ADCL023;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Pace-LDC1">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://BY2ADCK580:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://BY2ADCK580:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://BY2ADCK580:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <AdIntelligenceServiceUrl>http://BY2ADCK580:8080/Api/Advertiser/CampaignManagement/V13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://BY2ADCK162:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://BY2ADCVM332:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://BY2ADCK580:10094/CampaignMT/v6/CampaignService.svc </CMMTEndPoint>
    <!-- comma delimited folders without dates. If there is a central folder location then add the paths until the server name, include all 3 central folders -->
    <MTLogFolders>\\BY2ADCK162\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\BY2ADCK162\d$\Advertiser\Campaign\API\APIV8\Log</APILogFolders>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
        <Customer Id="9071907" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>9130678ea48dd20d</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1412999</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ADCL001;Initial Catalog=Adcenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ADCL002;Initial Catalog=AdCenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CO2APSC116V2;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Wallace">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://BY2ADCVM316.phx.gbl:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://by2apsg045.phx.gbl:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://BY2ADCVM313:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://BY2ADCVM316.phx.gbl:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <CustomerList>
      <!-- Next five are for Bulk -->
      <Customer Id="79839" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>cctest</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>110829</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="5074003" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>cctest</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1310009</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="350031" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>cctest</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>345938</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="5038059" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>cctest</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1119384</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="43359" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>cctest</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>786850</AccountID>
          </Account>
        </AccountList>
      </Customer>     
      <Customer Id="655116" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>APIPerf_P4_1</UserName>
            <PassWord></PassWord>
            <AccessKey>77Z75K2TJ</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>369726</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="1827" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" Default="true">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>API_P4_5</UserName>
            <PassWord></PassWord>
            <AccessKey>0A69KD8U</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>33956</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ003;initial catalog=Adcenter_Campaign_P1;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="105" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Default="true">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>API_P4_2</UserName>
            <PassWord></PassWord>
            <AccessKey>3DA8UDZP7</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>242998</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ003;initial catalog=Adcenter_Campaign_P1;Integrated Security=True</CampaignDB>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="14247" TrustRating="0" Type="Premium" Default="true" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>API_P4_3</UserName>
            <PassWord></PassWord>
            <AccessKey>ESB34N28</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="False">
            <AccountID>858133</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;initial catalog=Adcenter_Campaign_P5;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="27711" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>API_P4_4</UserName>
            <PassWord></PassWord>
            <AccessKey>R56D79I18LV</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>143622</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2MTZSSQLOFS01;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2MTZSSQLo01;Initial Catalog=adCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="PerfLab-Gromit">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://by2adcl139.phx.gbl:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://by2apsg045.phx.gbl:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://by2mtzc876.phx.gbl:807/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CustomerList>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>N2cqiC3GILWVrIxFxd9d</UserName>
            <PassWord></PassWord>
            <AccessKey>A55RWRF77A2P</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1347325</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>ZyU6R8Qn</UserName>
            <PassWord></PassWord>
            <AccessKey>09DF56168</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1353846</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>Nt2heuGIOhzBrjh</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1353847</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>3pxGytZ4L</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true">
            <AccountID>1353848</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2MTZSSQLOFS01;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2MTZSSQLo01;Initial Catalog=adCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="OneBox">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://apps-ci-box11.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>http://apps-ci-box11.redmond.corp.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://apps-ci-box11.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://apps-ci-box11:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://apps-ci-box11:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
    
      <Customer Id="5079691" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="true" Type="ApiUser" Default="true">
            <UserName>ae36ad25d4633149</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1687519</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in SI-->
      <Customer Id="5079623" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>a8ed306639c986ca</UserName>
            <PassWord></PassWord>
            <AccessKey>R5S2IIQ2D05O</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1686682</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="YahooUser" Default="true">
            <UserName>048b4c4b05264f15b530</UserName>
            <PassWord></PassWord>
            <AccessKey>260J54N1</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1622399</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" YahooUser="True" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2MTZSSQLo01;Initial Catalog=adCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="Bay-ProductionTest">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CampaignApiCertificateSubjectName>adcenterapi.microsoft.com</CampaignApiCertificateSubjectName>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Default="true" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True" Default="true">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
    
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
    <Offline-Servers>
      <!--Increase servercount and add neew servers as needed-->
      <ServerCount>10</ServerCount>
      <Server1 Name="BY2ADCAPI01"></Server1>
      <Server2 Name="BY2ADCAPI02"></Server2>
      <Server3 Name="BY2ADCAPI03"></Server3>
      <Server4 Name="BY2ADCAPI04"></Server4>
      <Server5 Name="BY2ADCAPI05"></Server5>
      <Server6 Name="BY2ADCAPI16"></Server6>
      <Server7 Name="BY2ADCAPI17"></Server7>
      <Server8 Name="BY2ADCMAFAPI04"></Server8>
      <Server9 Name="BY2ADCMAFAPI05"></Server9>
      <Server10 Name="BY2ADCMAFAPI06"></Server10>
      <Server11 Name="BY2ADCMAFAPI06"></Server11>
    </Offline-Servers>
  </Environment>
  <Environment Name="CHI-ProductionTest">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CampaignApiCertificateSubjectName>adcenterapi.microsoft.com</CampaignApiCertificateSubjectName>
    <CustomerList>
      <Customer Id="162434496" TrustRating="2" Type="Premium" Partition="1" Default="true" EnableOAuth="True" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true" EnableOAuth="True">
            <UserName><EMAIL></UserName>
            <PassWord></PassWord>
            <AccessKey>118U1G479H385647</AccessKey>
            <TokenName>CampaignBulkApiAutoBVT</TokenName>
            <RefreshToken>MCQ7HDsJYLgrl4xstc9yg9YtzPzRdx1S71Dr2Zl3*9e7oozTOV!0!9*mnkF3lBm0BwdrMVJq0CPyub3d5eDvVxTbX8AY6x4*UWJF7*hJXrVAj1Z*totwyyTv88Cxm08cCqhd0VXEMrmmEzrrQFuK*7OBEHdBnJ7*OU6clxIkpVH5ZLZtfaz3*TexQ9WTz6S6*HEQfZmTnOfKN4qJGU3xQIypKIp6DbpwWb9vQPogpX7lJ7wHy95qTHxheSvGJAlqSAkKs5l6WGQW2mr5KAKTor!grvRh0BrDDYlRIKbdIdgtspHiGzB6QHfnnYHGBDzIXa81lNKSQlHSF5q9VXrNkyt7IzOgMqHaTdQn5!UVcep3B</RefreshToken>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>*********</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
    <Offline-Servers>
      <!--Increase servercount and add neew servers as needed-->
      <ServerCount>10</ServerCount>
      <Server1 Name="BY2ADCAPI01"></Server1>
      <Server2 Name="BY2ADCAPI02"></Server2>
      <Server3 Name="BY2ADCAPI03"></Server3>
      <Server4 Name="BY2ADCAPI04"></Server4>
      <Server5 Name="BY2ADCAPI05"></Server5>
      <Server6 Name="BY2ADCAPI16"></Server6>
      <Server7 Name="BY2ADCAPI17"></Server7>
      <Server8 Name="BY2ADCMAFAPI04"></Server8>
      <Server9 Name="BY2ADCMAFAPI05"></Server9>
      <Server10 Name="BY2ADCMAFAPI06"></Server10>
      <Server11 Name="BY2ADCMAFAPI06"></Server11>
    </Offline-Servers>
  </Environment>
  <Environment Name="BAY-Online">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="EAPCH01LDC1-ProductionTest-Vip">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
     <Customer Id="162434496" TrustRating="2" Type="Premium" Partition="1" Default="true" EnableOAuth="True" PilotEnabled="True" Merchant="SimpleDirectMerchant"> 
       <UserList>
          <User Status="Active" Type="ApiUser" Default="true" EnableOAuth="True">
            <UserName><EMAIL></UserName>
            <PassWord></PassWord>
            <AccessKey>118U1G479H385647</AccessKey>
            <TokenName>CampaignBulkApiAutoBVT</TokenName>
            <RefreshToken>MCQ7HDsJYLgrl4xstc9yg9YtzPzRdx1S71Dr2Zl3*9e7oozTOV!0!9*mnkF3lBm0BwdrMVJq0CPyub3d5eDvVxTbX8AY6x4*UWJF7*hJXrVAj1Z*totwyyTv88Cxm08cCqhd0VXEMrmmEzrrQFuK*7OBEHdBnJ7*OU6clxIkpVH5ZLZtfaz3*TexQ9WTz6S6*HEQfZmTnOfKN4qJGU3xQIypKIp6DbpwWb9vQPogpX7lJ7wHy95qTHxheSvGJAlqSAkKs5l6WGQW2mr5KAKTor!grvRh0BrDDYlRIKbdIdgtspHiGzB6QHfnnYHGBDzIXa81lNKSQlHSF5q9VXrNkyt7IzOgMqHaTdQn5!UVcep3B</RefreshToken>
          </User>
       </UserList>
       <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>*********</AccountID>
          </Account>
       </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="EAPCH01LDC2-ProductionTest-Vip">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
     <Customer Id="162434496" TrustRating="2" Type="Premium" Partition="1" Default="true" EnableOAuth="True" PilotEnabled="True" Merchant="SimpleDirectMerchant"> 
       <UserList>
          <User Status="Active" Type="ApiUser" Default="true" EnableOAuth="True">
            <UserName><EMAIL></UserName>
            <PassWord></PassWord>
            <AccessKey>118U1G479H385647</AccessKey>
            <TokenName>CampaignBulkApiAutoBVT</TokenName>
            <RefreshToken>MCQ7HDsJYLgrl4xstc9yg9YtzPzRdx1S71Dr2Zl3*9e7oozTOV!0!9*mnkF3lBm0BwdrMVJq0CPyub3d5eDvVxTbX8AY6x4*UWJF7*hJXrVAj1Z*totwyyTv88Cxm08cCqhd0VXEMrmmEzrrQFuK*7OBEHdBnJ7*OU6clxIkpVH5ZLZtfaz3*TexQ9WTz6S6*HEQfZmTnOfKN4qJGU3xQIypKIp6DbpwWb9vQPogpX7lJ7wHy95qTHxheSvGJAlqSAkKs5l6WGQW2mr5KAKTor!grvRh0BrDDYlRIKbdIdgtspHiGzB6QHfnnYHGBDzIXa81lNKSQlHSF5q9VXrNkyt7IzOgMqHaTdQn5!UVcep3B</RefreshToken>
          </User>
       </UserList>
       <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>*********</AccountID>
          </Account>
       </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="EAPMW1LDC1-ProductionTest-Vip">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
     <Customer Id="162434496" TrustRating="2" Type="Premium" Partition="1" Default="true" EnableOAuth="True" PilotEnabled="True" Merchant="SimpleDirectMerchant"> 
       <UserList>
          <User Status="Active" Type="ApiUser" Default="true" EnableOAuth="True">
            <UserName><EMAIL></UserName>
            <PassWord></PassWord>
            <AccessKey>118U1G479H385647</AccessKey>
            <TokenName>CampaignBulkApiAutoBVT</TokenName>
            <RefreshToken>MCQ7HDsJYLgrl4xstc9yg9YtzPzRdx1S71Dr2Zl3*9e7oozTOV!0!9*mnkF3lBm0BwdrMVJq0CPyub3d5eDvVxTbX8AY6x4*UWJF7*hJXrVAj1Z*totwyyTv88Cxm08cCqhd0VXEMrmmEzrrQFuK*7OBEHdBnJ7*OU6clxIkpVH5ZLZtfaz3*TexQ9WTz6S6*HEQfZmTnOfKN4qJGU3xQIypKIp6DbpwWb9vQPogpX7lJ7wHy95qTHxheSvGJAlqSAkKs5l6WGQW2mr5KAKTor!grvRh0BrDDYlRIKbdIdgtspHiGzB6QHfnnYHGBDzIXa81lNKSQlHSF5q9VXrNkyt7IzOgMqHaTdQn5!UVcep3B</RefreshToken>
          </User>
       </UserList>
       <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>*********</AccountID>
          </Account>
       </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="EAPMW1LDC2-ProductionTest-Vip">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://campaignbeta.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
     <Customer Id="162434496" TrustRating="2" Type="Premium" Partition="1" Default="true" EnableOAuth="True" PilotEnabled="True" Merchant="SimpleDirectMerchant"> 
       <UserList>
          <User Status="Active" Type="ApiUser" Default="true" EnableOAuth="True">
            <UserName><EMAIL></UserName>
            <PassWord></PassWord>
            <AccessKey>118U1G479H385647</AccessKey>
            <TokenName>CampaignBulkApiAutoBVT</TokenName>
            <RefreshToken>MCQ7HDsJYLgrl4xstc9yg9YtzPzRdx1S71Dr2Zl3*9e7oozTOV!0!9*mnkF3lBm0BwdrMVJq0CPyub3d5eDvVxTbX8AY6x4*UWJF7*hJXrVAj1Z*totwyyTv88Cxm08cCqhd0VXEMrmmEzrrQFuK*7OBEHdBnJ7*OU6clxIkpVH5ZLZtfaz3*TexQ9WTz6S6*HEQfZmTnOfKN4qJGU3xQIypKIp6DbpwWb9vQPogpX7lJ7wHy95qTHxheSvGJAlqSAkKs5l6WGQW2mr5KAKTor!grvRh0BrDDYlRIKbdIdgtspHiGzB6QHfnnYHGBDzIXa81lNKSQlHSF5q9VXrNkyt7IzOgMqHaTdQn5!UVcep3B</RefreshToken>
          </User>
       </UserList>
       <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>*********</AccountID>
          </Account>
       </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="BAY-LDC2-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2.By2.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://c2.By2.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://c2.By2.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="CHI-LDC1-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c1.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://c1.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://c1.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="CHI-LDC2-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://c2.ch1.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://c2_ch1_adcenter_campaign_mt.phx:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="CHI-Online">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://bcp.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://bcp.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://bcp.adcenterapi.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertiserapitest_02</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>2383058</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>advertisersrctest20</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1822322</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" Default="true" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase />
    <Offline-Server />
  </Environment>
  <Environment Name="OneBox-DailyRegression">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://apps-ci-box04.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc</CampaignManagementUrl>
    <CampaignManagementRestUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://apps-ci-box04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://apps-ci-box04:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <MTLogFolders>\\CO2APSC110V1\d$\CampaignMTLog</MTLogFolders>
    <APILogFolders>\\CO2APSC110V1\d$\Advertiser\Campaign\API\APIV7\Log</APILogFolders>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="5079691" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" YahooUser="true" Type="ApiUser" Default="true">
            <UserName>ae36ad25d4633149</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1687519</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!--new user devtest2 got messed up-->
      <Customer Id="7079836" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>2e6a97799dd8486</UserName>
            <PassWord></PassWord>
            <AccessKey>1L9L5BYH550</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1429598</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <!-- KWMT Customer in SI-->
      <Customer Id="5079623" TrustRating="1" Type="Premium" Partition="6" PilotEnabled="True" KeywordMatchTypeEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>a8ed306639c986ca</UserName>
            <PassWord></PassWord>
            <AccessKey>R5S2IIQ2D05O</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1686682</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="********" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="YahooUser" Default="true">
            <UserName>048b4c4b05264f15b530</UserName>
            <PassWord></PassWord>
            <AccessKey>260J54N1</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>1622399</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" YahooUser="True" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=BY2ACSICTM\BY2ACSICTMSQL;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=BY2ACSICMP2\BY2ACSICMPSQL2;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=BY2MTZSSQLo01;Initial Catalog=adCenter_MT;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="nalink">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>http://localhost:48471/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>http://localhost:48471/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>http://localhost:48471/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>http://localhost:48471/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm04:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>net.tcp://cpmtzdwb04:3080/clientcenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://cptrarptf12:12000/CampaignMTDevDev6/Service.svc</CMMTEndPoint>
    <CustomerList>
      <!-- trust rating 0 -->
      <Customer Id="264388" TrustRating="0" Type="Premium" Partition="5" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest1api</UserName>
            <PassWord></PassWord>
            <AccessKey>0BA328O0</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <AccountID>256473</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="7" PilotEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="True" Merchant="SimpleDirectMerchant" AdultEnabled="True">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="True">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="SMB" Default="true">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Agency" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ApiUser" Default="true" AdultRoleEnabled="False">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264390" TrustRating="2" Type="Premium" LoadAccountsFromDB="False" Partition="3" PilotEnabled="True">
        <UserList>
          <User Type="ThirdPartyDevToken">
            <UserName>devtest3api</UserName>
            <PassWord></PassWord>
            <AccessKey>Q3E5UX45</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="False">
            <!--<AccountID>125457</AccountID>-->
            <AccountID>256492</AccountID>
          </Account>
        </AccountList>
        <CampaignDB>Data Source=CPAPSZ004;initial catalog=Adcenter_Campaign_P3;Integrated Security=True</CampaignDB>
      </Customer>
      <Customer Id="264391" TrustRating="3" Type="Premium" Partition="1" PilotEnabled="False" Merchant="DirectMerchant" AdultEnabled="False">
        <UserList>
          <User Status="Active" Type="ApiUser" AdultRoleEnabled="False">
            <UserName>devtest4api</UserName>
            <PassWord></PassWord>
            <AccessKey>********</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" CashBackEnabled="True">
            <AccountID>256493</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL18;Initial Catalog=adCenter_Customer;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPMTZDSQL18;Initial Catalog=Adcenter_Campaign_p6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL18;Initial Catalog=AdCenter_MT_Scrub;Integrated Security=True</MTDB>
      <!--DB Access Parameters-->
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-AutoBVTTest">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <AdIntelligenceServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <CampaignManagementRestUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://##ServerName##/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CampaignApiCertificateSubjectName>api.si.bingads.microsoft.com</CampaignApiCertificateSubjectName>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-Online">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://api.si.adcenter.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://si.clientcenter.microsoft-int.com:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://by2apse059:10094/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-LDC1-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://c1_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
  <Environment Name="SI-LDC2-VIP">
    <CampaignEndPoint>BasicHttpBinding_ICampaignManagementService</CampaignEndPoint>
    <CampaignManagementUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc</CampaignManagementUrl>
    <BulkServiceEndPoint>BasicHttpBinding_IBulkService</BulkServiceEndPoint>
    <BulkServiceUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc</BulkServiceUrl>
    <CampaignManagementRestUrl>https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementServiceRest.svc</CampaignManagementRestUrl>
    <AdIntelligenceServiceUrl>https://c2_api.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/AdIntelligenceService.svc</AdIntelligenceServiceUrl>
    <MDSEndPoint>net.tcp://btredvm05:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService</MDSEndPoint>
    <CCMTEndPoint>https://siccmt.trafficmanager.net:3089/ClientCenter/mt</CCMTEndPoint>
    <CMMTEndPoint>http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc</CMMTEndPoint>
    <CustomerList>
      <Customer Id="264389" TrustRating="1" Type="Premium" Partition="6" Default="true" PilotEnabled="True" Merchant="DirectMerchant">
        <UserList>
          <User Status="Active" Type="ApiUser" Default="true">
            <UserName>devtest2api</UserName>
            <PassWord></PassWord>
            <AccessKey>78218Z7KQ6V4J</AccessKey>
          </User>
        </UserList>
        <AccountList>
          <Account CurrencyCode="USD" Status="Active" Paused="False" Default="true" CashBackEnabled="True">
            <AccountID>256490</AccountID>
          </Account>
        </AccountList>
      </Customer>
    </CustomerList>
    <DataBase>
      <CustomerDB>Data Source=CPMTZDSQL08;Initial Catalog=Adcenter_CUstomer_INT;Integrated Security=True</CustomerDB>
      <CampaignDB>Data Source=CPAPSZ005;Initial Catalog=Adcenter_Campaign_P6;Integrated Security=True</CampaignDB>
      <MTDB>Data Source=CPMTZDSQL16;Initial Catalog=adCenter_MT_INT;Integrated Security=True</MTDB>
      <DBConnectionTimeout>60</DBConnectionTimeout>
      <DBCommandTimeout>600</DBCommandTimeout>
      <SecondaryDBCommandTimeOut>600</SecondaryDBCommandTimeOut>
      <DBConnectionRetries>1</DBConnectionRetries>
    </DataBase>
  </Environment>
</EnvironmentList>