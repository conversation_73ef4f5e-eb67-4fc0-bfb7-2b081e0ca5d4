﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="CampaignManagementFunctional.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <appSettings file="DoNotCheckin.config">
    <add key="AdCenterEnvironment" value="##TestEnvironmentName##" />
    <add key="UseConfigCustomerAccountUser" value="true" />
    <add key="UseMsaInProd" value="true" />
    <add key="DefaultCustomerPartition" value="" />
    <add key="UseConfigEnvironmentValues" value="true" />
    <add key="OfflineValidations" value="##OfflineValidation##" />
    <add key="FindLogsForFailedTests" value="true" />
    <add key="APIVersion" value="V13" />
    <add key="NewDefaultCustomersPoolSize" value="1" />
    <add key="ReplaceBulkDownloadURL" value="true" />
    <add key="PrintLogsInConsole" value="true" />
    <add key="WaitForCCMTCacheUpdateSec" value="15" />
    <add key="UseShardGroupHistogramForCampaignDb" value="true" />
    <add key="ClientSettingsProvider.ServiceUri" value="" />
    <add key="FileDownloadRetryCount" value="5" />
    <add key="FileDownloadRetryWaitInMilliSeconds" value="500" />
    <add key="CustomerCacheFolderName" value="Default" />
	<add key="Clientcenter.UserSecurityClient.IsTestEnvironment" value="true"/>
  </appSettings>
  <applicationSettings>
    <CampaignManagementFunctional.Properties.Settings>
      <setting name="CampaignManagementFunctional_CampaignManagementService_AdCenterCampaignManagementService" serializeAs="String">
        <value>http://localhost:10874/CampaignManagementService.svc</value>
      </setting>
    </CampaignManagementFunctional.Properties.Settings>
  </applicationSettings>
</configuration>
