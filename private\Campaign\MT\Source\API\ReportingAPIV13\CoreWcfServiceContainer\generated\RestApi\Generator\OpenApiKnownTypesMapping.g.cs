﻿namespace HostedService;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json.Serialization;
using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization.Metadata;

public static partial class RestApiGeneration
{
    public partial class OpenApiKnownTypesMapping
    {
        public static Dictionary<Type, Dictionary<Type, string>> TypesMap = new Dictionary<Type, Dictionary<Type, string>>
        {
            // Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request entities
            { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ReportRequest), new Dictionary<Type, string> {                
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AccountPerformanceReportRequest), "AccountPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.CampaignPerformanceReportRequest), "CampaignPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdDynamicTextPerformanceReportRequest), "AdDynamicTextPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdGroupPerformanceReportRequest), "AdGroupPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdPerformanceReportRequest), "AdPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.KeywordPerformanceReportRequest), "KeywordPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.DestinationUrlPerformanceReportRequest), "DestinationUrlPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.BudgetSummaryReportRequest), "BudgetSummaryReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AgeGenderAudienceReportRequest), "AgeGenderAudienceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProfessionalDemographicsAudienceReportRequest), "ProfessionalDemographicsAudienceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.UserLocationPerformanceReportRequest), "UserLocationPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.PublisherUsagePerformanceReportRequest), "PublisherUsagePerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.SearchQueryPerformanceReportRequest), "SearchQueryPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ConversionPerformanceReportRequest), "ConversionPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.GoalsAndFunnelsReportRequest), "GoalsAndFunnelsReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.NegativeKeywordConflictReportRequest), "NegativeKeywordConflictReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.SearchCampaignChangeHistoryReportRequest), "SearchCampaignChangeHistoryReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdExtensionByAdReportRequest), "AdExtensionByAdReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdExtensionByKeywordReportRequest), "AdExtensionByKeywordReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AudiencePerformanceReportRequest), "AudiencePerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AdExtensionDetailReportRequest), "AdExtensionDetailReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ShareOfVoiceReportRequest), "ShareOfVoiceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductDimensionPerformanceReportRequest), "ProductDimensionPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductPartitionPerformanceReportRequest), "ProductPartitionPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductPartitionUnitPerformanceReportRequest), "ProductPartitionUnitPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductSearchQueryPerformanceReportRequest), "ProductSearchQueryPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductMatchCountReportRequest), "ProductMatchCountReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.ProductNegativeKeywordConflictReportRequest), "ProductNegativeKeywordConflictReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.CallDetailReportRequest), "CallDetailReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.GeographicPerformanceReportRequest), "GeographicPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.DSASearchQueryPerformanceReportRequest), "DSASearchQueryPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.DSAAutoTargetPerformanceReportRequest), "DSAAutoTargetPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.DSACategoryPerformanceReportRequest), "DSACategoryPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.HotelDimensionPerformanceReportRequest), "HotelDimensionPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.HotelGroupPerformanceReportRequest), "HotelGroupPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AssetGroupPerformanceReportRequest), "AssetGroupPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.SearchInsightPerformanceReportRequest), "SearchInsightPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AssetPerformanceReportRequest), "AssetPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.CategoryInsightsReportRequest), "CategoryInsightsReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.CategoryClickCoverageReportRequest), "CategoryClickCoverageReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.CombinationPerformanceReportRequest), "CombinationPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.AppsPerformanceReportRequest), "AppsPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.FeedItemPerformanceReportRequest), "FeedItemPerformanceReportRequest" },
                { typeof(Microsoft.AdCenter.Advertiser.Reporting.Api.DataContracts.Request.TravelQueryInsightReportRequest), "TravelQueryInsightReportRequest" } }
            },

       };
    }
}