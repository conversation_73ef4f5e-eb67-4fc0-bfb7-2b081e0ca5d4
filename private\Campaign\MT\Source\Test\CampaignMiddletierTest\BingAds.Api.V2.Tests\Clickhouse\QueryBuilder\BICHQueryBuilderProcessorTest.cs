﻿using CampaignMiddleTierTest.Framework;
using ClickHouse.Client.ADO;
using Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse;
using Microsoft.AdCenter.Shared.MT.Logging;
using Microsoft.AdCenter.Shared.MT;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Text;
using BIClickhouse = Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse;
using Microsoft.AdCenter.Common.UniversalConfiguration;
using Microsoft.Advertising.ServiceLocation;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json;

namespace Microsoft.Advertising.Advertiser.Api.V2.Clickhouse.QueryBuilder
{
    [TestClass]
    public class BICHQueryBuilderProcessorTest 
    {
        private static ILogShared logger = new BasicLogger();

        public static Dictionary<ClickhouseQueryType, ReportColumns> BIClickhouseReportColumns = new Dictionary<ClickhouseQueryType, ReportColumns>();


        [ClassInitialize]
        public static void InitReportColumns(TestContext testContext)
        {
            var settings = new JsonSerializerSettings
            {
                Converters = new List<JsonConverter> { new StringEnumConverter() }
            };

            BIClickhouseReportColumns = JsonConvert.DeserializeObject<Dictionary<ClickhouseQueryType, ReportColumns>>(File.ReadAllText(@"Clickhouse\ColumnsForQueries.json"), settings);

            //QueryBuilder requires AppConfigRegistry
            //https://msasg.visualstudio.com/Bing_Ads/_git/AdsAppsMT?path=/private/Common/Clickhouse/ClickhouseQueryBuilder/Common/ClickhouseQueryBuilderConfiguration.cs&version=GBmaster&line=19&lineEnd=19&lineStartColumn=1&lineEndColumn=79&lineStyle=plain&_a=contents

            var container = ((UnityServiceLocator)ServiceLocator.Current).Container;
            ConfigManagerInitialization.SetUpConfigurationManager(
                logger,
                container,
                "app.config",
                null,
                null
                );
            string compileEngine =  "Clickhouse";
            ClickhouseQueryBuilderConfiguration.DynamicConfig.OverwriteConfig("ClickhouseQueryBuilderCompileEngine", compileEngine);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Prc_AccountSummary_UI_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { *********, 2338104 } },
                { "RelationshipId", new List<long> { } },
                { "AgencyId", new List<long> { } },
                { "Dates", new List<DateTime> { new DateTime(2023, 5, 1), new DateTime(2023, 5, 31) } },
                { "NumOfWeeks", 0 },
                { "QualifiedRowCnt", 0 },
                { "ActualReturnRowCnt", 178 },
                { "TimeZoneId", new List<long> { } },
                { "Options", "MultiAccountOverview,ReturnIfNoData"},
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountSummary_ui].Dimensions },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountSummary_ui].Measures },
                { "TrackingId", "5bf93995-4dea-405f-a7a6-0bb6022b6c46" },
                { "ResultsTable", "" },
                { "CallType", "" }
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_AccountSummary_ui, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Prc_AccountSummary_uiV2_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
          {
              { "AccountId", new List<long> { *********, *********, ********* } },
              { "RelationshipId", new List<long> { } },
              { "AgencyId", new List<long> { } },
              { "MediumId", new List<int> {255,1 } },
              { "Dates", new List<DateTime> { new DateTime(2025, 2, 25), new DateTime(2025, 2, 27) } },
              { "NumOfWeeks", 0 },
              { "QualifiedRowCnt", 0 },
              { "ActualReturnRowCnt", 178 },
              { "TimeZoneId", new List<long> { } },
              { "Options", "UCM,ReturnIfNoData"},
               { "RegionId", new List<string>(){"EE"}},
              { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountSummary_ui].Dimensions },
              { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountSummary_ui].Measures },
              { "TrackingId", "5bf93995-4dea-405f-a7a6-0bb6022b6c46" },
              { "ResultsTable", "" },
              { "CallType", "" }
          };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_AccountSummaryV2_ui, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Prc_AccountImpressionShare_ui_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { *********, 5247285 } },
                { "RelationshipId", new List<long> { } },
                { "Dates", new List<DateTime> { new DateTime(2023, 9, 1), new DateTime(2023, 9, 16) } },
                { "QualifiedRowCnt", 0 },
                { "ActualReturnRowCnt", 0 },
                { "Options", "UCM,ReturnIfNoData"},
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountImpressionShare_ui].Dimensions },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.prc_AccountImpressionShare_ui].Measures },
                { "TrackingId", "3d28b606-acdd-41c5-9cbd-9b68b65c0437" },
                { "ResultsTable", "" }
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_AccountImpressionShare_ui, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Rpt_AccountActivity_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "TrackingId", "083139be-c138-40c8-acb4-d7e96112218b" },
                { "UserId", ******** },
                { "RequestedReturnRowCnt", 0 },
                { "Options", "RDS,ReturnIfNoData"},
                { "AccountId", new List<long> { ********* } },
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.rpt_AccountActivity].Dimensions },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.rpt_AccountActivity].Measures },
                { "Grain", "GregorianDate" },
                { "Dates", new List<DateTime> { new DateTime(2023, 08, 30), new DateTime(2023, 08, 30) } },
                { "OrderBy", "AccountName Asc,AccountNumber Asc,AccountId Asc,GregorianDate Asc,CurrencyCode Asc,MediumName Asc,DeviceTypeName Asc,NetworkId Asc,AccountStatusName Asc" }
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_AccountActivity, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Rpt_MSXAccountUsageSummary_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "TrackingId", "DDB12F13-CAFC-42C5-BC69-286EA5858743" },
                { "UserId", ********* },
                { "RequestedReturnRowCnt", 0 },
                { "ActualReturnRowCnt", 62 },
                { "QualifiedRowCnt", 62 },
                { "Options", "RDS,ReturnIfNoData"},
                { "AccountId", new List<long> { *********, *********, *********, *********, *********, *********, *********, *********, *********, ********* } },
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.rpt_MSXAccountUsageSummary].Dimensions },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.rpt_MSXAccountUsageSummary].Measures },
                { "Grain", "GregorianDate" },
                { "Dates", new List<DateTime> { new DateTime(2023, 12, 1), new DateTime(2023, 12, 1) } }
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_MSXAccountUsageSummary, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Rpt_GetAccountInfo_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "TrackingId", "25CD3DB0-28EE-42A2-BA32-B388EFB8A7F2" },
                { "SessionId", Guid.NewGuid() },
                { "RequestId", Guid.NewGuid() },
                { "AccountIds", new List<long> { *********, *********, 2509186, 1161026 } },
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_GetAccountInfo, queryProperties);
            Assert.IsTrue(res);
        }


        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void rpt_ProductDimensionSummaryV2_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long>
                { *********,*********,*********,*********,********
                }},
                { "UserId", new List<long> { ********* } },
                { "Grain", "GregorianDate"},
                { "ActualReturnRowCnt", 1 },
                { "QualifiedRowCnt", 49348 },
                { "RequestedReturnRowCnt", 0 },
                { "StartDate", new DateTime(2024, 2, 1) },
                { "EndDate", new DateTime(2024, 2, 29) },
                { "Columns", new List<string>//Measures used in recent 30 days
                {
                    "AccountName", "CampaignName", "KeywordOrderName", "MerchantProductId", "Title", "AssetGroupName"
                }},
                { "Measures", new List<string>//Measures used in recent 30 days
                {   "Impressions", "Clicks", "TotalCost", "TotalSearchAbsTopPosition",
                    "TotalShoppingAbsTopPosition", "SearchUniqueImpressionCnt", "ShoppingUniqueImpressionCnt","BenchmarkBid_Numerator"
                }},
                { "Options", "RDS,ReturnIfNoData" },
                { "TrackingId", "1F7D963B-80CF-41B0-951C-76F40FABDDF3" },
                { "CampaignStatusId", "1"},
                { "AccountStatusId", "1"},
                { "AdGroupStatusId", "1"},
                { "AdStatusId", "1"},
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_ProductDimensionSummaryV2, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void rpt_KeywordActivitySummary_clickhouse_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { 1666659 } },
                { "Dates", new List<DateTime> { new DateTime(2023, 10, 22), new DateTime(2023, 10, 22) } },
                { "Columns", new List<string>//Measures used in recent 30 days
                {
                    "AccountName", "CampaignName", "Keyword"
                }},
                { "Measures", new List<string>//Measures used in recent 30 days
                {   "Impressions", "Clicks", "TotalCost"
                }},
               { "TrackingId", "066ED686-4687-4BC1-BB84-3F4FBDD57074" },
                { "CampaignStatusId", "1"},
                { "AdGroupStatusId", "1"},
                { "KeywordStatusId", "1"},
            };
            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_KeywordActivitySummary, queryProperties);
            Assert.IsTrue(res);

        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Prc_CampaignSummary_ui_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { ********* } },
                { "CampaignId", new List<long> { } },
                { "RelationshipId", new List<long> { } },
                { "DeviceTypeId", new List<long> { } },
                { "AgencyId", new List<long> { } },
                { "Dates", new List<DateTime> { new DateTime(2023, 8, 6), new DateTime(2023, 9, 4) } },
                { "NumOfWeeks", 0 },
                { "QualifiedRowCnt", 0 },
                { "ActualReturnRowCnt", 200 },
                { "TimeZoneId", new List<long> { } },
                { "CurrencyId", new List<long> { } },
                { "MediumId", new List<long> { } },
                { "RegionId", new List<long> { } },
                { "Options", "ReturnIfNoData"},
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.prc_CampaignSummary_ui].Dimensions },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.prc_CampaignSummary_ui].Measures },
                { "TrackingId", "3e538458-e4c2-412d-aee0-dea9cc9f360f" },
                { "ResultsTable", "" },
                { "CallType", "" },
                { "OnlyShoppingData", 0 }
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_CampaignSummary_ui, queryProperties);
            Assert.IsTrue(res);
        }


        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Prc_CampaignExperimentSummary_uiCookiebased_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { ********* } },
                { "CustomerId", new List<long> { ********* } },
                { "CampaignId", new List<long> { ********* } },
                { "Dates", new List<DateTime> { new DateTime(2023, 11, 1), new DateTime(2023, 11, 15) } },
                { "QualifiedRowCnt", 0 },
                { "ActualReturnRowCnt", 178 },
                { "Options", "ReturnIfNoData"},
                { "Columns", BIClickhouseReportColumns[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].Dimensions.Where( c=> !c.Equals("CustomerId")).ToList() },
                { "Measures", BIClickhouseReportColumns[ClickhouseQueryType.prc_CampaignExperimentSummary_ui].Measures},
                { "TrackingId", "41960DA5-13DF-4DD9-8123-DA976661AF4A" },
                { "ResultsTable", "" },
                { "IsCookieBased", true}
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_CampaignExperimentSummary_ui, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Rpt_CampaignInfo_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountIds", new List<long> { ********* } },
                { "Dates", new List<DateTime> { new DateTime(2023, 11, 1), new DateTime(2023, 11, 15) } },
                { "SessionId", "41960DA5-13DF-4DD9-8123-DA976661AF4A" },
                { "RequestId", "41960DA5-13DF-4DD9-8123-DA976661AF4A" },
                { "TrackingId", "41960DA5-13DF-4DD9-8123-DA976661AF4A" },
                { "JoinCampaignsWithData", true}
            };

            var res = ExecuteClickhouseQuery(ClickhouseQueryType.rpt_GetCampaignInfo, queryProperties);
            Assert.IsTrue(res);
        }

        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void Uni_ContentPerformanceSummary_CIOnly()
        {
            var queryProperties = new Dictionary<string, dynamic>
            {
                { "AccountId", new List<long> { ********* } },
                { "Dates", new List<DateTime> { new DateTime(2023, 10, 27), new DateTime(2023, 10, 27) } },
                { "ActualReturnRowCnt", 13 },
                { "Columns", new List<string>
                {
                    "AccountName",
                    "AccountNumber",
                    "AccountId",
                    "CampaignName",
                    "KeywordOrderName",
                    "LanguageName",
                    "BiddedMatchTypeDesc",
                    "DeliveredMatchTypeDesc",
                    "DeviceOSName",
                    "AccountStatusName",
                    "CampaignStatusName",
                    "AdGroupStatusName",
                    "HourOfDay",
                    "MonthStartDate",
                    "PublisherURL",
                    "TargetTypeId",
                    "TargetValueId",
                    "ContentId",
                }},
                { "Measures", new List<string>
                {
                    "Impressions",
                    "Clicks",
                    "TotalCost",
                    "Conversions",
                    "AdvertiserReportedRevenue",
                    "ConversionEnabledClicks",
                    "ConversionEnabledTotalCost",
                    "Assists",
                    "FullAdvertiserReportedRevenue",
                    "TotalConversions",
                    "FullConversions",
                    "FullViewConversions",
                    "TotalSearchAbsTopPosition",
                    "TotalShoppingAbsTopPosition",
                    "SearchUniqueImpressionCnt",
                    "ShoppingUniqueImpressionCnt",
                    "TotalSearchTopPosition",
                    "ConversionsQualified",
                    "AllConversionsQualified",
                    "FullViewAdvertiserReportedRevenue"
                }},
                { "TrackingId", Guid.NewGuid().ToString() }
            };
            var res = ExecuteClickhouseQuery(ClickhouseQueryType.Uni_ContentPerformanceSummary, queryProperties);
            Assert.IsTrue(res);
        }


        [TestMethod]
        [Priority(0)]
        [TestCategory("CIOnly")]
        public void prc_GetSearchQuerySummaryLite_CIOnly()
        {
            int expectRowCount = 0;

            var queryProperties = new Dictionary<string, dynamic>
            {
                { "TrackingId", "f8d72548-7109-48e0-a265-c5cf20fde529" },
                { "Options", "ReturnIfNoData"},
                { "AccountId", new List<long> { ********* } },
                { "Columns", new List<string>()
                {
                    "SearchTerm",
                    "KeywordOrderId",
                    "CampaignId",
                    "MatchTypeId",
                    "KeywordOrderItemId",
                    "IsOther",
                    "SubMatchTypeId",
                }},
                { "Measures", new List<string>()
                {
                    "TotalCost",
                }
                },
                { "OrderBy","TopImpressionRate"}, // Restore the OrderBy line
                { "Dates", new List<DateTime> { new DateTime(2023, 10, 1), new DateTime(2023, 10, 31) } },

            };
            var res = ExecuteClickhouseQuery(ClickhouseQueryType.prc_GetSearchQuerySummaryLite, queryProperties);
            Assert.IsTrue(res);
        }

        private bool ExecuteClickhouseQuery(ClickhouseQueryType queryType, Dictionary<string, dynamic> queryProperties)
        {
            var columns = queryProperties.TryGetValue("Columns", out var p1s) ? string.Join(',', p1s) : string.Empty;
            var measures = queryProperties.TryGetValue("Measures", out var p2s) ? string.Join(',', p2s) : string.Empty;
            try
            {
                var clickhouseAdGroupDBComponents = TestSetting.Environment.CampaignService.AdvBiCHAdgroupDB.DbComponents;
                foreach (DatabaseComponent component in clickhouseAdGroupDBComponents)
                {
                    if(component != null)
                    {
                        var partitionId = component.PartitionId;
                        var allDb = new List<DatabaseComponent> { TestSetting.Environment.CampaignService.AdvBiCHAdgroupDB.GetDatabaseComponent(partitionId) };

                        foreach (var dbc in allDb)
                        {
                            if (dbc == null) continue;
                            using var conn = new ClickHouseConnection(dbc.ConnectionString);
                            var actualRowCount = 0;
                            conn.Open();
                            var queryBuilder = new BIClickhouse.ClickhouseQueryBuilder(logger);
                            var request = new ClickhouseQueryRequest(queryType, queryProperties,
                                new TestConnectionProvider(conn.ConnectionString));
                            request.QueryId = Guid.NewGuid();
                            var response = queryBuilder.QueryAsync(request, logger).Result;
                            return response.Status == QueryStatus.Success && response.DataReader != null;
                        }
                    }
                }
                return false;
            }
            catch (Exception e)
            {
                logger.LogError($"Columns:{columns},Measures:{measures},  Error: {e}");
                throw;
            }
        }
    }


    public class ReportColumns
    {
        public List<string> Dimensions { get; set; }
        public List<string> Measures { get; set; }
        public ReportColumns(List<string> dimensions, List<string> measures)
        {
            Dimensions = dimensions;
            Measures = measures;
        }
    }
}
