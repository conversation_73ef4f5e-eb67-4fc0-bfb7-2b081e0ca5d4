<?xml version="1.0" encoding="UTF-8"?>
<configuration>
<configSections>
</configSections>
<appSettings>
<add key="OutOfServiceFilePath" value="d:\data\CampaignMiddleTier\OutOfService.txt" />
<add key="OutOfServiceFilePathLinux" value="/app/OutOfService" />
<add key="DistributedQueryServiceSSLCertName" value="api-sandbox-bingads-microsoft-com" />
<add key="IsCacheEnabled" value="true" />
<add key="IsLocalFanoutEnabled" value="true" />
<add key="AccountShardedDbTypes" value="AzureChangeHistoryUI, AdvbiAzureAccount2, AuditHistoryReport, AdvBiCHAccount" />
<!--add comma-separated types as sharding gets enabled for them-->
<!-- Note, when adding new DB types below, make sure also put its credential (userid/pwd) into KeyVault for SI/Prod and add its KeyVaultName in this config, see 'CampaignDBKeyVaultName' as an exmaple. Better to test it in LDC3 before check-in as local/CI does not require keyvault -->
<add key="ShardedDbTypes" value="" />
<add key="DBTypesLoadedFromMetadataService" value="" />
<!-- A report preview call timeout is 120 seconds, set the DBCommandTimeout to the same value -->
<add key="DBCommandTimeout" value="120" />
<add key="SystemMemorySizeCheckPoint1" value="50" />
<add key="SystemMemorySizeCheckPoint2" value="70" />
<add key="MemoryRowLimitBeforeCheckPoint1" value="5000" />
<add key="MemoryRowLimitBeforeCheckPoint2" value="1000" />
<add key="MemoryRowLimitAfterCheckPoint2" value="0" />
<add key="MaxRowsInMemoryPerIDataReader" value="400000" />
<add key="FolderToBufferData" value="C:\DistributedQueryServiceDataBuffer" />
<add key="FolderToBufferDataLinux" value="/app/DistributedQueryServiceDataBuffer" />
<add key="SortTempFolderPath" value="C:\DistributedQueryServiceDataSort" />
<add key="SortTempFolderPathLinux" value="/app/DistributedQueryServiceDataSort" />
<!-- Clickhouse migration-->
<add key="ClickhouseAzureStorageAccountConnectionString" value="KeyVaultSecret:ClickhouseAzureStorageAccountConnectionStringKey" />
<add key="AdvBi2ChAdgroupShardgroupMapping" value="1:1" />
<add key="ReportDataServiceDataValidationFiles" value="C:\ReportDataServiceDataValidationFiles" />
<add key="ReportDataServiceDataValidationFilesLinux" value="/app/ReportDataServiceDataValidationFiles" />
<add key="ClickhouseLoadBalancerPolicy" value="Default" />
<add key="ClickhouseEnv" value="CI" />
<add key="LoadBIStatisticsFromCache" value="false" />
<add key="ClickhouseQueryBIDataExistenceInCTE" value="true" />
<!-- From CampaignMT Implementation App.Config -->
<add key="ListenOnMultipleAddresses" value="true" />
<add key="ListenOnAlternatePorts" value="" />
<add key="TerminateOnDynamicConfigLoadFailure" value="true" />
<add key="AppIdOverrideForCampaignApiV13" value="API-AdCenterCampaignManagementMiddleTierAPIv13" />
<add key="KeyVaultUseManagedIdentity" value="false" />
<add key="KeyVaultRunAs" value="RunAs=Developer; DeveloperTool=AzureCli" />
<add key="CampaignMTManageIdentityId" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
<add key="CampaignMTClientCertName" value="bingads-campaignmt-clientcert-pme" />
<add key="ODataNetCorePort" value="8080" />
<add key="CampaignServiceSSLCertThumbprint" value="3875e1058353709343e250bd41cbd25bfe2c8b6e" />
<add key="CampaignServiceSSLPort" value="443" />
<add key="CampaignServiceSSLCertName" value="api-sandbox-bingads-microsoft-com" />
<add key="IsOneCertEnabled" value="false" />
<add key="LogConfigurationPath" value=".." />
<add key="LogConfigurationFile" value="LogConfiguration.xml" />
<add key="LogSchemaFile" value="ClientConfiguration.xsd" />
<add key="ServiceHostDnsName" value="" />
<add key="StaticDataCacheRefreshIntervalInSeconds" value="600" />
<add key="CacheExpiryDurationInSeconds" value="7200" />
<add key="SecurityTicketExpirationBufferInSeconds" value="60" />
<add key="NumRetriesForCCMTCommunicationException" value="2" />
<add key="ConfigServiceEnabled" value="true" />
<add key="BlacklistEnabled" value="true" />
<add key="ThrottleEnabled" value="True" />
<add key="ThrottleEnforced" value="false" />
<add key="ThrottleSyncPeriodInSeconds" value="10" />
<add key="ThrottleLimitPerPeriod" value="300" />
<add key="ThrottleLimitForSandbox" value="300" />
<add key="ThrottleLimitForBulkDownload" value="300" />
<add key="ThrottleLimitForPollStatus" value="300" />
<add key="ThrottleLimitForBulkUpload" value="300" />
<add key="ThrottleLimitForUploadEntityRecords" value="300" />
<add key="ThrottleTimePeriodInSeconds" value="60" />
<add key="ThrottleCacheTimeToLiveInSeconds" value="60" />
<add key="SandboxEnabled" value="false" />
<add key="SandboxHostPrefix" value="sandbox" />
<add key="DemandClassEnabled" value="false" />
<add key="TipHostPrefix" value="tip" />
<add key="ClientcenterBaseaddress" value="net.tcp://cpmtzdwb04:3080/clientcenter/mt" />
<add key="Clientcenter.UPN" value="" />
<add key="Clientcenter.ConnectionBufferSize" value="262144" />
<add key="Clientcenter.UseUserSecurityService" value="false" />
<add key="ClientCenter_UseRaceTasksWithErrorHandling" value="true" />
<!-- Recreate CC security ticket time in minutes. 0 Means turned off and do not call CCMT.RecreateToken() -->
<add key="RecreateSecurityTicketTimeInMinutes" value="15" />
<add key="ConnectionDrainEnabled" value="false" />
<add key="ConnectionDrainMonitorFilePath" value="" />
<!-- ConnectionDrainTaskInterval in milliseconds -->
<add key="ConnectionDrainTaskInterval" value="60000" />
<!-- Enable close connection per nth call -->
<add key="CloseConnectionOnNthCallEnabled" value="false" />
<!-- Close connection after below number of calls -->
<add key="CloseConnectionRequestCount" value="1000" />
<add key="CampaignSecretsKeyVaultName" value="CampaignSecretsKVSI" />
<add key="OptimizedLocationCacheLoad" value="true" />
<add key="ConditionalPeriodicCacheRefreshEnabled" value="false" />
<add key="BlobVersionCheckIntervalInMinutes" value="10" />
<add key="LocationCacheRefreshIntervalInMinutes" value="10" />
<add key="QueryDistributorMaxVirtualPageSize" value="10000" />
<add key="QueryDistributorVirtualPageSizeFactor" value="50" />
<add key="EnableUniversalConfig" value="false" />
<add key="EnableUniversalConfigMonitor" value="false" />
<add key="EnableLoadFromUniversalConfigFile" value="true" />
<add key="UniversalConfigBlobConnectionString" value="KeyVaultSecret:UniversalConfigBlobConnectionString" />
<add key="UniversalConfigServiceBusConnectionString" value="KeyVaultSecret:UniversalConfigServiceBusConnectionString" />
<add key="ServiceBusConnectionString" value="KeyVaultSecret:ServiceBusConnectionString" />
<add key="CampaignHealthProbeServiceBusTopicName" value="CampaignHealthProbeServiceBusTopic" />
<add key="CampaignHealthProbeServiceBusTopicMessageLifespan" value="00:03:00" />
<add key="CampaignHealthProbeServiceBusSubscriberLockDuration" value="00:05:00" />
<add key="CampaignHealthProbeServiceBusSubscriberMaxMessageAge" value="00:02:00" />
<add key="CampaignHealthProbeServiceBusSubscriberDeleteOnIdleTime" value="00:05:00" />
<add key="LoadBalancerStateCacheTooOldThreshold" value="00:02:00" />
<add key="ServiceStopWhenInitFail" value="false" />
<!-- Long running task flag settings -->
<add key="ActiveTaskThresholdForLongRunning" value="100" />
<add key="SetLongRunningTaskFlag" value="true" />
<!-- Kusto Logger Settings -->
<add key="KustoLoggerEnabled" value="false" />
<add key="KustoUrl" value="https://ingest-bingadsppe.kusto.windows.net:443" />
<add key="KustoDatabase" value="BingAdsTracing" />
<add key="KustoTableBase" value="adsapps" />
<!-- Note: the application id and key below is for use by the Bing Ads Apps Campaign platform team only. -->
<add key="KustoApplicationId" value="0fb88dd0-e942-4c39-9aa5-8326638948f9" />
<add key="KustoApplicationCertName" value="bingads-campaignmt-clientcert-pme" />
<add key="KustoManagedIdentityEnabled" value="false" />
<add key="KustoCategoryExcludeFilter" value="" />
<add key="KustoIncludeVerbose" value="false" />
<add key="KustoTablesByLogCategory" value="true" />
<add key="KustoConnectionLoggerEnabled" value="false" />
<!-- Client center token cache settings -->
<add key="UsePrincipalForTokenCacheKey" value="true" />
<!-- Thread pool overrides -->
<add key="MinThreadPoolThreads" value="1000" />
<add key="MaxThreadPoolThreads" value="" />
<add key="MinCompletionPortThreads" value="500" />
<add key="MaxCompletionPortThreads" value="1024" />
<!-- ServicePointManager DefaultConnectionLimit -->
<add key="ServicePointManagerDefaultConnectionLimit" value="100" />
<!-- Encryption -->
<add key="CredentialsFilePath" value="\\BingDrops\AppsServiceCIShare\CI_DataFiles\PPSCredentialShare\QueueProcessorCrededntials_encrypted.xml" />
<add key="EncryptionCertificatePath" value="NA" />
<add key="EncryptionCertificateThumbprint" value="NA" />
<add key="EncryptedKey" value="KeyVaultSecret:EncryptedKey" />
<add key="EncryptedIV" value="WzdkItA7ZfmBrM81urPsiEHGBiZ/U5Pq61FjmzihR6zY7wcOlbSDvRy+9Ha+bsaGxLbvg68mKIofPF9SmJt5/BxGlgDEBxvaIbH9FL/ZLRorBV7guc7qq8MxxpbztSdTpce9nwClG0n+JjxPFBn2xR8vEsTa0hxw39CmwIWqT30=" />
<!-- Encryption -->
<add key="FileCacheUseCompression" value="true" />
<add key="FileCacheUseCopy" value="true" />
<add key="FileCacheBufferSize" value="1048576" />
<add key="AsyncCacheWrite" value="true" />
<add key="DomainDataRefreshIntervalInSeconds" value="300" />
<add key="DisableDomainDataRefresh" value="false" />
<add key="DisableDomainDataConsistencyCheck" value="false" />
<add key="DisableMetadataRefresh" value="false" />
<add key="MetadataRefreshIntervalInSeconds" value="30" />
<add key="MetadataVersion" value="1.00" />
<add key="Metadata_Server" value="appsdevdev" />
<add key="Metadata_DB" value="AdCenter_MetadataDB" />
<add key="Metadata_Server_Secondary" value="appsdevdev" />
<add key="Metadata_DB_Secondary" value="AdCenter_MetadataDB" />
<add key="VirtualEarthServiceUrlRest" value="https://dev.virtualearth.net/REST/v1/Locations/" />
<add key="VirtualEarthTimeZoneServiceUrlRest" value="https://dev.virtualearth.net/REST/v1/TimeZone/" />
<add key="VirtualEarthCommonServiceUrl" value="https://common.virtualearth.net/find-30/common.asmx" />
<add key="VirtualEarthKey" value="KeyVaultSecret:VirtualEarthKey" />
<!-- According to VE team, SLA is 6 seconds -->
<add key="VirtualEarthPingTimeoutSeconds" value="6" />
<add key="GenerateLocationsXMLOnStartup" value="0" />
<add key="PriceEstimationPartition" value="PriceEstimation0,0,127;PriceEstimation1,128,255" />
<add key="BTEPriceEstimationPartition" value="AdvertiserEngagement_NKW,0,1;AdvertiserEngagement_NKW,2,3;AdvertiserEngagement_NKW,4,5;AdvertiserEngagement_NKW,6,7;AdvertiserEngagement_NKW,8,9;AdvertiserEngagement_NKW,10,11;AdvertiserEngagement_NKW,12,13;AdvertiserEngagement_NKW,14,15" />
<add key="AdcenterMobileUrlSuffix" value=".beta5.api.idss.msn.com/mobileweb" />
<!-- This value setting key can be set to modify the hostname in the WSDL generated by IIS -->
<!-- Same as ServiceHostName but for API V9 -->
<add key="ServiceHostDnsNameBingAds" value="" />
<!-- Same as ServiceHostName but for Campaign API V10 -->
<add key="ServiceHostDnsNameBingAdsCampaign" value="" />
<!-- Same as ServiceHostName but for Bulk API V10 -->
<add key="ServiceHostDnsNameBingAdsBulk" value="" />
<add key="UserMapStoreUrl" value="http://appsdevdev:1090/AdvertiserMocks/UserMapMockService.svc/" />
<add key="UserMapStoreTimeoutSeconds" value="300" />
<add key="AudienceInteligenceServiceUrl" value="net.tcp://aidev1:3001/v1/AudienceIntelligenceService/MetadataService/PublicService" />
<add key="StandardSegmentsCacheValidityInSeconds" value="300" />
<add key="CryptographyServiceUrl" value="https://appsdevdev.redmond.corp.microsoft.com:3089/clientcenter/mt/cryptography" />
<!-- API Throttle limit flag -->
<add key="ThrottleEnableGlobalCache" value="false" />
<add key="ThrottleEnableLimitForSpecificCustomers" value="true" />
<!-- API Global Throttle Settings -->
<add key="ThrottleRedisCacheEntryPoint" value="cmmt-eus-si-redis-throttle.redis.cache.windows.net" />
<add key="ThrottleRedisCacheSecretKey" value="cmmt-eus-si-redis-throttle" />
<add key="ThrottleRedisCachePort" value="6380" />
<add key="ThrottleEnvironmentIdentifier" value="Default" />
<add key="ThrottleRedisCacheConnectTimeoutInMS" value="1000" />
<add key="ThrottleRedisCacheSyncTimeoutInMS" value="1000" />
<!-- Keyword grid-related flag -->
<add key="AggregatorKeywordNetworkOptimizationThreshold" value="250000" />
<add key="ReportMTPingTimeoutSeconds" value="3" />
<add key="InternalWCFServicePingTimeoutSeconds" value="3" />
<add key="AggregatorCacheReadThrottleSize" value="80" />
<add key="AggregatorCacheWriteThrottleSize" value="100" />
<add key="MinimumClicksForLowBudgetStatus" value="10" />
<add key="ImportFileShare" value="\\advertisertest2\filecache" />
<!-- Tier settings -->
<add key="DBConnectionTimeout" value="10" />
<add key="ResourceExpiryTime" value="300" />
<add key="RetryFailedDBOperation" value="4" />
<add key="MonthlyBudgetMinimum" value="5" />
<add key="BudgetPercent" value="1" />
<!-- number of minutes to refresh the customer feature cache -->
<add key="RefreshCustomerFeatureCacheMinutes" value="5" />
<!-- comma separated values from enum Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.DistributionChannel -->
<add key="LiveMarketDistributionChannel" value="UnitedStates,CanadaEN,CanadaFR" />
<!-- comma separated MarketForAssetAds values from enum  Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.PublisherCountry -->
<add key="MarketsForAssetAds" value="US,Canada,UK,France,Ireland" />
<!-- comma separated MarketForRichSearchAds values from enum  Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.PublisherCountry -->
<add key="MarketsForRichSearchAds" value="US,Canada,UK,France,Ireland,Germany" />
<!-- comma separated values from enum  Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Entities.PublisherCountry -->
<add key="MarketsWithRequiredProvinceCode" value=" US,Canada,Italy,Spain,Brazil,India,Australia,China" />
<add key="AdGroupMaxEndDate" value="1/1/2050 23:59:59" />
<!-- true if Read-Only mode is in effect, false if system runs under normal operation -->
<add key="ReadOnlyEnabled" value="false" />
<add key="NegativeKeywordExpansionEntityLimit" value="25" />
<add key="NegativeKeywordEntityLimitForBulkDownload" value="5" />
<add key="StructuredNegativeKeywordEntityLimit" value="1" />
<add key="StructuredNegativeKeywordLimitForBulkUpload" value="10000" />
<add key="StructuredNegativeKeywordAddDeleteBatchLimit" value="1000" />
<!--Number of targets to process in a batch-->
<add key="MaxPreferencesReadBatchSize" value="2000" />
<add key="TargetsBatchLimit" value="50" />
<add key="CreateTargetGroupBatchLimit" value="100" />
<add key="CreateTargetBatchThreadLimit" value="2" />
<add key="MaxNegativeKeywordsCount" value="20000" />
<add key="MaxRowsReturnedFromImportDB" value="50000" />
<add key="ImportDoEditorialValidationsDuringInitialFileUpload" value="true" />
<add key="ImportDoNormalizationDuringInitialFileUpload" value="false" />
<add key="ValidateCamAssets" value="true" />
<add key="SmallBatchLimit" value="20" />
<add key="TaskTimeoutInMinutes" value="30" />
<add key="RetryWaitTimeInMinutes" value="1" />
<add key="BLImportCountPerTask" value="50" />
<add key="MaximumNumberOfGeocodeThreads" value="10" />
<add key="MultiAccountBulkUploadMaxFileSizeInBytes" value="*********" />
<add key="BulkUploadTaskTimeoutInMinutes" value="30" />
<add key="BulkUploadRetryWaitTimeInMinutes" value="1" />
<add key="BulkUploadUrl" value="http://localhost:10094/File/UploadBulkFile" />
<add key="BulkUploadDirectUrl" value="http://localhost:10094/File/UploadBulkFile" />
<add key="BulkUploadThrottleRequestsLimit" value="100" />
<add key="BulkUploadThrottleTimeIntervalInMinutes" value="10" />
<add key="BulkUploadRunRequestsInLowPriorityThreads" value="false" />
<add key="BulkUploadUseSqlShortTermStorage" value="false" />
<add key="BulkUploadMaxRowsInMemory" value="40000" />
<add key="BulkUploadMaxThreads" value="2" />
<add key="BulkUploadMaxKeywordEditorialThreads" value="2" />
<add key="BulkUploadMaxAdEditorialThreads" value="2" />
<add key="BulkUploadMaxWriteThreads" value="3" />
<add key="BulkUploadMaxThreadsForAdgroups" value="10" />
<add key="BulkUploadBackoffThreshold" value="80" />
<add key="BulkUploadBackoffDurationInMs" value="5000" />
<add key="BulkUploadMaxRowCountAllowed" value="4000000" />
<add key="BulkUploadTopXKeywords" value="10" />
<add key="BulkUploadTopXAds" value="10" />
<add key="BulkEditTaskParamsVersion" value="2" />
<add key="MaxKeywordEditorialBatchSize" value="1000" />
<add key="MaxAdEditorialBatchSize" value="1000" />
<add key="AlwaysUseAccountLevelKeywordEO" value="true" />
<add key="AdGroupSyncServiceDownloadLimit" value="200000" />
<add key="EnforceMonthlySpendCheckForCampaignBudgetUpdate" value="true" />
<add key="EnableFraudCheck" value="false" />
<add key="MaxKeywordsToFraudCheck" value="10000" />
<add key="FraudMTTimeoutInMilliseconds" value="0" />
<add key="AssetSchemasCacheValidDurationInSeconds" value="600" />
<add key="AllowModifyingAdGroupDefaultBidsWhenOptimizerOn" value="false" />
<add key="AllowModifyingAdGroupMediumWhenOptimizerOn" value="false" />
<add key="LogUnmappedEditorialErrors" value="false" />
<add key="NKWConflictsReadyFromBI" value="false" />
<add key="MaxTextAdTextLength" value="71" />
<add key="UserIdsUsedForYahooMigration" value="" />
<add key="DomainDataDownloadBaseUrl" value="http://dummy" />
<add key="EnableDatabaseCallCounting" value="true" />
<add key="MaxDatabasePoolSize" value="500" />
<add key="MaxEntriesForImportPreferencesCache" value="1000" />
<add key="BulkEntryThresholdInPercentage" value="30" />
<add key="IsTaskPostponedFeatureEnabled" value="true" />
<add key="MaxNumberOfNegativeSiteUrls" value="2500" />
<add key="MaxNumberOfIPExclusions" value="3000" />
<add key="RunBulkRequestsInLowPriorityThreads" value="false" />
<add key="QualityScoreFeatureEnabledForCampaignAndAdGroup" value="true" />
<!--ClientCenter dll config cache refresh time in minutes -->
<add key="UserSecurity.ResourceGroupsCacheExpireAfterMinutes" value="60" />
<add key="MaxNumberOfEntitiesForNegKeywordsAdd" value="1000" />
<add key="MaxNumberOfSearchTermsFromReporting" value="25000" />
<add key="EnableDbTrackingIdLogging" value="true" />
<add key="BingMerchantCenterApplicationId" value="A59218844EBFD8F318DD52C4AB6A267A7C93D47E" />
<add key="BingMerchantCenterTimeoutSeconds" value="3" />
<add key="BingMerchantCenterDestinationSchema" value="BdiGeneric.VerifyFiltersResponseData[1.1]" />
<!-- SI URL is - http://apiqsgint.playmsn.com:83/bdigeneric/xmlxml.aspx-->
<add key="BingMerchantCenterBDIURL" value="http://apiqsgint.playmsn.com:83/bdigeneric/xmlxml.aspx" />
<add key="BingMerchantCenterBDIVersion" value="1.0" />
<add key="BingMerchantCenterFlightHeader" value="xap=SOSPartners" />
<add key="BingMerchantCenterVersionHeader" value="1.0" />
<add key="BingMerchantCenterHeadersEnabled" value="false" />
<add key="BingMerchantCenterBDITimeoutSeconds" value="3" />
<add key="RefreshAccountCacheMinutes" value="5" />
<add key="EnableAccountCaching" value="true" />
<!--ProbeSettings-->
<add key="ShadowingMonitorEnabled" value="false" />
<add key="ShadowingSignalFilePath" value="D:\Data\ShadowRequest\ShadowRequest.txt" />
<add key="ShadowMonitorTaskIntervalInMilliseconds" value="5000" />
<add key="RefreshAccountLightCacheMinutes" value="60" />
<add key="EnableAccountLightCaching" value="true" />
<add key="AdGroupCallTrackingEligibilityBiDateRangeType" value="5" />
<add key="AdGroupCallTrackingEligibilityMinClicks" value="10" />
<add key="AdGroupCallTrackingEligibilityMinSpend" value="10" />
<add key="EnableCustomerAuthorization" value="true" />
<add key="EnableCustomerAuthorizationCache" value="true" />
<add key="CustomerAuthorizationCacheExpiryInMinutes" value="5" />
<add key="EnableClick2CallQueue" value="true" />
<add key="Click2CallQueuePauseDelayInHours" value="0" />
<add key="MaxBulkEntitiesFetchedFromDatabase" value="1000" />
<add key="UseNewOrderItemIndexProcs" value="true" />
<add key="DevTokenCustomerMapping" value="995V9F8Q382:Y-DTSAccess2,9FK9IJW4:API_EFrontier,QOTKGA69:API_Kenshoo,137KMG15PFX:API_DARTSRCH_GOOG,1PN9N6E4L9883CYC:API_Nextag,8N9X5R3L:API_adlucent,X1J9O01JH:API_SearchIgnite,KZ48H7FQ16C2:API_Marin,1L9L5BYH550:Desktop,142M1D2770K:intelliad_API" />
<add key="ReturnErrorForDeprecatedMethods" value="false" />
<!--Cloud table storage configurations-->
<add key="CloudTableMaxPreProcessedBatchNumber" value="30" />
<add key="CloudTableMaxBacklogedBatchNumber" value="8" />
<add key="CloudTableMaxBatchSize" value="100" />
<add key="CloudTableConnectionString" value="KeyVaultSecret:CloudTableConnectionString" />
<add key="CloudTableRetryDeltaBackOffSeconds" value="1" />
<add key="CloudTableMaxRetryCount" value="3" />
<!--BulkUpload SQL storage configurations-->
<add key="BulkImportSqlBatchSize" value="5000" />
<add key="ApiV9RequestsEnabled" value="true" />
<add key="BulkV9RequestsEnabled" value="true" />
<add key="SharedPartition.InitialAssociationLoadInSeconds" value="30" />
<!--format is [entity count]:[timeout in ms], [entity count]:[timeout in ms]-->
<add key="ReportingTimeoutConfiguration" value="500000:240000,***********:300000" />
<add key="ReportingTimeoutConfigurationForBiOnlyDownload" value="500000:240000,***********:300000" />
<add key="KeywordDownloadConfiguration" value="NonHierarchyOrderItemBatchSize=100000,NonHierarchyOrderItemDownloadThreadCount=3,KeywordOrderItemQueueSize=3,OutputWriterQueueSize=3,ToStringConversionBatchSize=50000" />
<add key="ProductPartitionDownloadConfiguration" value="NonHierarchyProductPartitionBatchSize=50000,ProductPartitionQueueSize=3" />
<add key="MatchAccountIdInHeaderAndRequest" value="true" />
<add key="EnableCampaignQSRetrieval" value="true" />
<add key="EnableCampaignBTERetrieval" value="true" />
<add key="AdExtensionsBIEnabled" value="true" />
<add key="CampaignFraudStatusEnabled" value="true" />
<add key="AccountSummaryReportGridEntityEnabled" value="true" />
<add key="AdExtensionSummaryBatchSize" value="100" />
<add key="BulkParentEntityBatchSizePrediction" value="true" />
<add key="BulkParentEntityBatchSizePredictionMaxBatchSize" value="1000" />
<add key="AudienceGroupMaxBatchSize" value="100" />
<add key="BlackListedKeywordCacheExpiryIntervalInSeconds" value="3600" />
<add key="RegisterDynamicFile" value="true" />
<add key="DynamicConfigRefreshIntervalMillis" value="300000" />
<add key="SharedEntityAssociationsMaxLimitPerBatch" value="10000" />
<add key="NegativeKeywordMaxPerList" value="5000" />
<add key="NegativeKeywordMaxPerAccountNegativeKeywordList" value="1000" />
<add key="AggregatorNegativeKeywordGridMaxEntities" value="100000" />
<add key="AggregatorNegativeKeywordListAssociationGridMaxEntities" value="100000" />
<!-- set InitialProgressUpdateIntervalInMs to zero to disable updating reporting queue with progress -->
<add key="BulkDownloadProgressTrackingConfiguration" value="Campaign=6,CampaignSummary=1,CampaignEntity=1,AdGroup=20,AdGroupSummary=1,AdGroupEntity=1,Ad=20,AdSummary=1,AdEntity=1,Keyword=50,KeywordSummary=1,KeywordEntity=1,AdExtensionSummaryAndEntity=4;InitialProgressUpdateIntervalInMs=5000,NumberOfInitialProgressUpdates=3,LongRunningProgressUpdateIntervalInMs=15000,HeartbeatIntervalInMinutes=5;MinProgressInPercent=1,MaxProgressBeforeSuccessInPercent=98" />
<add key="BatchSizeForAudienceReadUsingMap" value="100000" />
<add key="MinBatchSizeForAudienceReadUsingMap" value="50000" />
<add key="MaxParallelDbCallforMappedRead" value="6" />
<!-- Make sure you add the entities in ConfiValues.cs or the entities will not get unhidden-->
<add key="ShowHiddenWsdlEntries" value="false" />
<add key="ShowHiddenWsdlEntries_DynamicSearchAd" value="false" />
<add key="ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding" value="true" />
<add key="ShowHiddenWsdlEntries_TestFunctionality" value="false" />
<add key="ShowHiddenWsdlEntries_TestConfiguration" value="false" />
<add key="ShowHiddenWsdlEntries_LinkedInForSearchBscDsa" value="false" />
<add key="ShowHiddenWsdlEntries_Experiment" value="true" />
<add key="ShowHiddenWsdlEntries_ResponsiveSearchAd" value="true" />
<add key="ShowHiddenWsdlEntries_EnhancedResponsiveAd" value="false" />
<add key="ShowHiddenWsdlEntries_CampaignLevelAudienceTargeting" value="false" />
<add key="ShowHiddenWsdlEntries_FinalUrlSuffix" value="false" />
<add key="ShowHiddenWsdlEntries_ImageAdExtensionsV2" value="false" />
<add key="ShowHiddenWsdlEntries_SearchCompanies" value="true" />
<add key="ShowHiddenWsdlEntries_AdScheduleTimeZoneSetting" value="true" />
<add key="ShowHiddenWsdlEntries_ApiUploadEntityRecords" value="false" />
<add key="ShowHiddenWsdlEntries_SponsorProductAdsV2" value="false" />
<add key="ShowHiddenWsdlEntries_ImportApi" value="true" />
<add key="ShowHiddenWsdlEntries_ThirdPartyConversions" value="true" />
<add key="ShowHiddenWsdlEntries_HouseholdIncomeTargeting" value="false" />
<add key="ShowHiddenBulkColumns" value="false" />
<add key="CacheTokenKey" value="KeyVaultSecret:CacheTokenKey" />
<add key="CacheTokenHmac" value="763089A5C8C27279DCFCBE753D3B3B24110C3D00AD681645D6D1F9B9D193ECC5" />
<add key="FileCacheTTLInMinutes" value="60" />
<add key="MemoryCacheTTLInMinutes" value="60" />
<add key="RedisCacheTTLInMinutes" value="60" />
<add key="RedisCacheAvailable" value="true" />
<!-- Off, Pilot, GA -->
<add key="BulkDownloadParallelConfiguration" value="AdsConcurrentTasks = 3; AdgroupsConcurrentTasks = 3; TargetsConcurrentTasks = 3" />
<add key="GetAdGroupCriterionsAdGroupMaxThreads" value="10" />
<add key="GetAdGroupCriterionsAdGroupBatchSize" value="1000" />
<add key="GetAdGroupCriterionsAdGroupEnabled" value="true" />
<add key="FlexLocationHierarchyBitMap" value="1" />
<add key="LogIncomingOrOutgoingRequests" value="false" />
<add key="ClientSettingsProvider.ServiceUri" value="" />
<add key="MdsServiceUrl" value="https://aismdsmt-si.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" />
<add key="MdsServiceHealthProbeUrl" value="http://aismdsmt-si.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" />
<add key="DataCenterName" value="115" />
<add key="LDC" value="LDC0" />
<!-- GSync -->
<add key="GSyncDevToken" value="KeyVaultSecret:GSyncDevToken" />
<add key="GSyncCosmosUploadFolder" value="D:\CampaignMT\ImportUpload" />
<add key="ScheduledImportCampaignIdsUploadFolder" value="D:\CampaignMT\CampaignIdsToUpload" />
<add key="UnifiedMarketIntelligenceDataFolder" value="D:\CampaignMT\MIUnifiedData" />
<add key="MarketIntelligenceCosmosUploadFolder" value="D:\CampaignMT\MIUpLoad" />
<add key="MarketIntelligenceAzureReportTaskStatusTableName" value="MarketIntelligenceReportTaskStatus" />
<add key="MarketIntelligenceAzureEntitySchemaTableName" value="MarketIntelligenceEntitySchema" />
<!-- Sql Deadlock Retry Policy settings-->
<!-- Note - The following values are curerntly being used for Optimized Import flow but can be used while calling Campaign DB SP also.-->
<!-- Accepted values : Default, Linear and Exponential-->
<add key="SqlDeadlockRetryPolicyType" value="Linear" />
<add key="SqlDeadlockRetryIntervalInSeconds" value="2" />
<!-- AppAdExtension URL validation settings -->
<add key="AppAdExtensionAsyncUrlValidationMaxThreads" value="5" />
<add key="AppAdExtensionAsyncUrlValidationTimeoutInMilliseconds" value="10000" />
<add key="AppAdExtensionAutoGoalCreationEnabled" value="false" />
<add key="ShowHiddenWsdlEntries_DataTableAdExtension" value="false" />
<!-- Azure Blob Storage Specific Settings-->
<add key="AzureBlobConnectionString" value="KeyVaultSecret:AzureBlobConnectionString" />
<add key="CampaignDataStorageConnectionString" value="KVSecret:campaigndbdonotdropsi" />
<add key="TextAssetStorageAccountName" value="campaigndbdonotdropsi" />
<add key="CampaignDataStorageAccountName" value="campaigndbdonotdropsi" />
<add key="TipTileConnectionString" value="KeyVaultSecret:TipTileConnectionString" />
<add key="ImportRecommendationsSAConnString" value="KeyVaultSecret:ImportRecommendationsSAConnString" />
<add key="ImportRecommendationStorageAccountName" value="importrecommendationspme" />
<add key="ImportAzureStorageAccountName" value="importstoragesi" />
<add key="ImportPMEAzureStorageAccountName" value="importstoragesipme" />
<add key="ImportAdsAppsStorageAccountName" value="bingadsappsstoragesi" />
<add key="ImportMIExperimentKustoUrl" value="https://bingadsppe.kusto.windows.net" />
<add key="ImportMIExperimentKustoDatabase" value="BingAdsImportSvc" />
<add key="ImportAzureStorageConnectionString" value="KeyVaultSecret:ImportAzureStorageConnectionString" />
<add key="ImportPMEAzureStorageConnectionString" value="KeyVaultSecret:ImportPMEAzureStorageConnectionString" />
<add key="ImportAdsAppsStorageConnectionString" value="KeyVaultSecret:bingadsappsstorageConnectionString" />
<add key="DefaultDeltaBackOff" value="1" />
<add key="DefaultMaxRetryCount" value="3" />
<add key="DefaultMaxExecutionTimeInSecondsForUploading" value="3600" />
<add key="DefaultSingleBlobUploadThresholdInBytes" value="********" />
<add key="BlobAccessExpiryTimeInSeconds" value="120" />
<add key="BulkFileBlobAccessExpiryTimeInSeconds" value="1200" />
<add key="ImportExportErrorBlobAccessExpiryTimeInSeconds" value="86400" />
<add key="ImportBulkUploadBlobAccessExpiryTimeInSeconds" value="1200" />
<add key="MaxDegreeOfParallelismForUploading" value="1" />
<add key="ShouldCacheCreatedContainers" value="false" />
<add key="BlobSharedAccessStartTimeInSeconds" value="-300" />
<add key="UndoBulkUploadContainerName" value="undobulkupsertuploadedfiles" />
<add key="MultiAccountBulkUploadContainerName" value="multiaccountuploadedfiles" />
<add key="MultiAccountBulkUploadStagingFolderPath" value="D:\CampaignMT\MultiAccountBulkUpload" />
<add key="MultiAccountUploadInputFileContainerName" value="multiaccountuploadedfiles" />
<add key="MultiAccountUploadAllRecordsResultFileContainerName" value="multiacccountresultfileswithcompletedata" />
<add key="MultiAccountUploadErrorOnlyResultFileContainerName" value="multiaccountresultfileswitherrorsonly" />
<add key="MultiAccountDownloadResultFileContainerName" value="multiaccountdownloadfiles" />
<add key="OfflineConversionsUploadPath" value="D:\CampaignMT\OfflineConversionsUpload" />
<add key="OfflineConversionsInputFileContainerName" value="offlineconvuploadedfiles" />
<add key="OfflineConversionsAllRecordsResultFileContainerName" value="offlineconvallresultfiles" />
<add key="OfflineConversionsErrorOnlyResultFileContainerName" value="offlineconverrorresultfiles" />
<add key="OfflineConversionReportContainerName" value="offlineconvreportfiles" />
<add key="OfflineConversionFullReportContainerName" value="offlineconvfullreport" />
<add key="OfflineConversionUploadMaxFileSizeInBytes" value="*********" />
<add key="OfflineConversionUploadMaxRowCountAllowed" value="100000" />
<add key="CustomerMatchInputFileContainerName" value="customermatchuploadedfiles" />
<add key="CustomerMatchResultFileContainerName" value="customermatchresultfiles" />
<add key="CustomerMatchUploadMaxRowCountAllowed" value="4000000" />
<add key="ImportStagingFolderPath" value="D:\dbs\el\aamt\CampaignMT\AzureStagingForImport" />
<add key="ImportToBulkFiles" value="D:\CampaignMT\ImportToBulkFiles" />
<add key="ImportAdWordsIdLocalFilePathForAdDeletion" value="D:\CampaignMT\ImportAdWordsIdLocalFilePathForAdDeletion" />
<add key="ImportAdWordsTokensKeyVaultCertName" value="secretsencryption-si-2024-1" />
<add key="ImportFileContainerName" value="importfiles" />
<add key="ImportPilotFileContainerName" value="importpilotfiles" />
<add key="ImportParsingErrorFileContainerName" value="importparsingerrorfiles" />
<add key="ImportExportFileContainerName" value="importexportfiles" />
<add key="ImportBulkUploadFileContainerName" value="importbulkuploadfiles" />
<add key="ImportBulkUploadResultFileContainerName" value="importbulkuploadresultfiles" />
<add key="GSyncEntityHashContainerName" value="gsyncentityhashfiles" />
<add key="MIFileContainerName" value="mifiles" />
<add key="MIFBFileContainerName" value="mifbfiles" />
<add key="BulkDownloadResultFileContainerName" value="bulkdownloadresultfiles" />
<add key="BulkUploadResultFileContainerName" value="bulkuploadresultfiles" />
<add key="GMBImportContainerName" value="gmbimportfiles" />
<add key="GTMImportContainerName" value="gtmentitydata" />
<add key="GTMImportCacheContainerName" value="gtmcache" />
<add key="BingPlacesImportContainerName" value="bingplacesimportfiles" />
<add key="FacebookImportRawFileContainerName" value="facebookimportrawfiles" />
<add key="BulkUploadDownloadStagingFolder" value="D:\CampaignMT\BulkUploadDownload" />
<add key="GSyncV2StagingFolder" value="D:\CampaignMT\GSyncV2StagingFolder" />
<add key="GMBStagingFolder" value="D:\CampaignMT\GMBStagingFolder" />
<add key="MIStagingFolder" value="D:\CampaignMT\MIStagingFolder" />
<add key="FeedUploadServiceContainerName" value="feeduploadservice" />
<add key="FeedUploadResultFileContainerName" value="feeduploadresult" />
<add key="PpsReconciliationReportInputFileContainerName" value="ppsreconciliationreportinputfiles" />
<add key="PpsReconciliationReportPreviewedFileContainerName" value="ppsreconciliationreportpreviewedfiles" />
<add key="PpsReconciliationReportCommittedFileContainerName" value="ppsreconciliationreportcommittedfiles " />
<add key="PpsReconciliationReportProcessedFileContainerName" value="ppsreconciliationreportprocessedfiles" />
<!-- UserPreference Settings -->
<add key="UXDataItemContainer" value="BingAds.Campaign.UserPreferences" />
<!-- Metadata Service settings-->
<!--
      This MetadataServiceEnabled value can be true,false or machine name list separated by ','.
      Machine name is the format without domain name.
      For example 'EAP010040204101,EAP010040204109' is a valid machine list
    -->
<add key="MetadataServiceEnabled" value="true" />
<add key="MetadataServiceEndpoint" value="http://appsdevdev:818" />
<add key="ChecksumDBTypes" value="CampaignDB,CampaignAdGroupShard" />
<!--add comma-separated types as sharding gets enabled for them-->
<!-- Note, when adding new DB types below, make sure also put its credential (userid/pwd) into KeyVault for SI/Prod and add its KeyVaultName in this config, see 'CampaignDBKeyVaultName' as an exmaple. Better to test it in LDC3 before check-in as local/CI does not require keyvault -->
<add key="DbTypesUsingFlattenedShardGroupMap" value="" />
<add key="DefaultNativeBidAdjustment" value="0" />
<add key="AggregatorInternalUrlTemplate" value="http://{0}:10094/AggregatorService.svc" />
<!-- Metric monitoring client settings. -->
<add key="MetricRecordingTarget" value="EventHub" />
<add key="MetricClientEventHubConnectionString" value="KeyVaultSecret:EventHubConnectionString" />
<add key="MetricClientEventHubName" value="anomalydetectiontest" />
<add key="MetricClientEventHubEndpoint" value="sb://anomalydetectionsi.servicebus.windows.net" />
<add key="MetricClientEventHubManagedIentityEnabled" value="false" />
<add key="IsEAPEnv" value="false" />
<!-- ContainerAccessExpiryTimeInSeconds as 15 minutes -->
<add key="ContainerAccessExpiryTimeInSeconds" value="900" />
<add key="ShowHiddenWsdlEntries_ActionAdExtension_V13" value="true" />
<add key="ClientCenter.UserSecurityClient.IsTestEnvironment" value="true" />
<add key="ExtraConnectionStringSettings" value="" />
<add key="LogConnectionDetailsForSqlExceptions" value="false" />
<add key="DownloadedFileLocation" value="D:\FileUpload\UploadedFileLocation\" />
<add key="EnableFileStreamStorage" value="true" />
<add key="ClientcenterBillingBaseaddress" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/ClientCenter/billing" />
<!-- DO NOT set override to true in prod - should only be true in CI and SI -->
<add key="EnableDynamicConfigOverride" value="false" />
<add key="DontWaitForCustomerCacheOnInit" value="true" />
<add key="EnableAzureBlobStorageBasedAccountParentCustomerIdLoad" value="true" />
<add key="AccountIDCustomerIDCacheRefreshInterval" value="00:10:00" />
<add key="BackgroundTaskEtwHeartbeatDelayInSeconds" value="30" />
<!-- Keyvault for DB credentials -->
<add key="MetadataKeyVaultEnabled" value="false" />
<add key="MetadataDBKeyVaultName" value="CampaignSecretsKVCI" />
<!-- Keyvault for DB credentials: Campaign -->
<add key="CampaignDBKeyVaultName" value="" />
<add key="CampaignStaticKeyVaultName" value="" />
<add key="CampaignAdGroupShardKeyVaultName" value="" />
<add key="CampaignNegativeKeywordShardKeyVaultName" value="" />
<add key="CampaignSharedLibraryShardKeyVaultName" value="" />
<add key="NegativeKeywordCatalogShardKeyVaultName" value="" />
<add key="VerticalDBKeyVaultName" value="" />
<add key="VerticalDBReadOnlyKeyVaultName" value="" />
<add key="CampaignEntityLibraryShardKeyVaultName" value="" />
<!-- Keyvault for DB credentials: BI -->
<add key="AdvBiAzureAccountKeyVaultName" value="" />
<add key="AzureAuditHistoryKeyVaultName" value="" />
<add key="AuditHistoryReportKeyVaultName" value="" />
<add key="AdvBiAzureAdgroupKeyVaultName" value="" />
<add key="AdvBiAccountMT_1KeyVaultName" value="" />
<add key="AdvBiAccountMT_2KeyVaultName" value="" />
<add key="AdvertiserBIBSCKeyVaultName" value="" />
<add key="AdvBIBSCShardedKeyVaultName" value="" />
<add key="AdvBiCHAccountKeyVaultName" value="" />
<add key="AdvBiCHAdgroupKeyVaultName" value="" />
<add key="OrderManagementSystemDBKeyVaultName" value="" />
<add key="MerchantCenterOfferDBKeyVaultName" value="" />
<add key="AdvbiAdgroupDVSKeyVaultName" value="" />
<add key="AdvbiAzureAccount2KeyVaultName" value="" />
<add key="BIElasticQueryKeyVaultName" value="" />
<add key="AdvBIAdgroupDimKeyVaultName" value="" />
<add key="AzureChangeHistoryUIKeyVaultName" value="" />
<add key="AzureChangeHistorySAKeyVaultName" value="" />
<!-- Keyvault for DB credentials: SA -->
<add key="CampaignBulkDBv2KeyVaultName" value="" />
<!-- Keyvault for DB credentials: ClientCenter -->
<add key="AppsConsolidatedInAzureKeyVaultName" value="" />
<!-- Keyvault for DB credentials: Billing -->
<add key="BillingDBPartitionedKeyVaultName" value="" />
<!-- Keyvault for DB credentials: PubBi -->
<add key="PubBIDbKeyName" value="pubbici" />
<add key="PubBIDbKeyVaultName" value="BIDataMartsKVMTSI" />
<!-- Profile data -->
<add key="ProfileDataRefreshIntervalInSeconds" value="3600" />
<add key="DisableProfileDataRefresh" value="false" />
<add key="EnableProfileDataInitializationForLinkedInCampaign" value="false" />
<!--OdataEndpointAddress-->
<add key="OdataEndpointAddress" value="http://localhost:10877/Advertiser/V2" />
<add key="IsDataServiceForReportingEnabled" value="false" />
<add key="ExperimentStagingFolder" value="D:\CampaignMT\Experiment" />
<!-- Metric Client for Parition Support -->
<add key="MetricClientEnabledForPartitionSupport" value="false" />
<!-- Feed upload service task type name -->
<add key="FeedUploadTaskTypeName" value="FeedUpload" />
<add key="MaxDurationToCacheBigTokenInMinutes" value="5" />
<!-- Multi-Channel Ads (MCA) Settings -->
<add key="MultiChannelAdsSecretsKeyVaultAppId" value="2f27e396-146c-458c-a1e0-08adabdbc766" />
<add key="MultiChannelAdsSecretsKeyVaultName" value="MCAVaultTest" />
<add key="McaEnvironment" value="CI" />
<add key="MultiChannelAdsKeyVaultName" value="" />
<add key="MultiChannelAdsSystemDBKeyVaultName" value="" />
<add key="McaBlobAccessExpiryTimeInSeconds" value="1200" />
<add key="MultiChannelAdsServiceBusConnectionString" value="KeyVaultSecret:MultiChannelAdsServiceBusConnectionString" />
<add key="MultiChannelAdsServiceBusSharedQueues" value="shared_p0,shared_p1,shared_p2" />
<!-- Smart Page Blob Storage Settings -->
<add key="SmartPageContainerName" value="$web" />
<add key="SmartPageDeltaBackOff" value="1" />
<add key="SmartPageMaxRetryCount" value="3" />
<add key="SmartPageMaxExecutionTimeInSecondsForUploading" value="3600" />
<add key="SmartPageSingleBlobUploadThresholdInBytes" value="********" />
<add key="SmartPageBlobAccessExpiryTimeInSeconds" value="120" />
<add key="SmartPageMaxDegreeOfParallelismForUploading" value="1" />
<add key="SmartPageBlobStorageConnectionString" value="KeyVaultSecret:SmartPageBlobStorageConnectionString" />
<add key="TaskEngineUrl" value="http://localhost:3877/ExecutionManagement" />
<add key="TaskEngine_sendTimeout" value="00:02:00" />
<add key="TaskEngine_openTimeout" value="00:01:00" />
<add key="TaskEngine_closeTimeout" value="00:01:00" />
<add key="CcmtHttpsImpersonationEndpointUrl" value="" />
<add key="CcmtHttpsImpersonation_ClientCertificateThumbprint" value="‎62c7b45a863f31602df6b805004c88018f83a989" />
<add key="CcmtHttpsImpersonationTokenCertificateName" value="" />
<add key="EditorialServiceUrl" value="http://localhost:1901/" />
<add key="EditorialShortTimeout_sendTimeout" value="00:00:01" />
<add key="EditorialShortTimeout_receiveTimeout" value="00:00:01" />
<add key="EditorialShortTimeout_openTimeout" value="00:00:01" />
<add key="EditorialShortTimeout_closeTimeout" value="00:00:01" />
<add key="Editorial_sendTimeout" value="00:00:05" />
<add key="Editorial_receiveTimeout" value="00:00:05" />
<add key="Editorial_openTimeout" value="00:00:05" />
<add key="Editorial_closeTimeout" value="00:00:05" />
<add key="EditorialAppeal_sendTimeout" value="00:01:00" />
<add key="EditorialAppeal_receiveTimeout" value="00:01:00" />
<add key="EditorialAppeal_openTimeout" value="00:01:00" />
<add key="EditorialAppeal_closeTimeout" value="00:01:00" />
<add key="AdInsight_sendTimeout" value="00:01:00" />
<add key="AdInsight_receiveTimeout" value="00:01:00" />
<add key="AdInsight_openTimeout" value="00:01:00" />
<add key="AdInsight_closeTimeout" value="00:01:00" />
<add key="AdInsightMtServiceUrl" value="http://localhost:10875/AdIntelligenceMTMockService.svc" />
<add key="AdInsightMt_sendTimeout" value="00:00:02" />
<add key="AdInsightMt_receiveTimeout" value="00:00:02" />
<add key="AdInsightMt_openTimeout" value="00:00:02" />
<add key="AdInsightMt_closeTimeout" value="00:00:02" />
<add key="FraudServiceUrl" value="net.tcp://appsdevdev:4326/Int/FraudInt.svc" />
<add key="Fraud_sendTimeout" value="00:00:30" />
<add key="Fraud_receiveTimeout" value="00:00:30" />
<add key="Fraud_openTimeout" value="00:00:20" />
<add key="Fraud_closeTimeout" value="00:00:05" />
<add key="Fraud_disableSecurity" value="false" />
<add key="ReportingMTUrlv2" value="net.tcp://appsdevdev:806/ReportingAggregatorMockService.svc" />
<add key="ReportingMt_sendTimeout" value="00:00:30" />
<add key="ReportingMt_receiveTimeout" value="00:00:30" />
<add key="ReportingMt_openTimeout" value="00:00:30" />
<add key="ReportingMt_closeTimeout" value="00:00:30" />
<add key="ReportingAggregatorUrl" value="http://localhost/AdvertiserMocks/ReportingAggregatorMockService.svc" />
<add key="ReportingAggregator_sendTimeout" value="00:00:30" />
<add key="ReportingAggregator_receiveTimeout" value="00:00:30" />
<add key="ReportingAggregator_openTimeout" value="00:00:30" />
<add key="ReportingAggregator_closeTimeout" value="00:00:30" />
<add key="Microsoft.ServiceBus.ConnectionString" value="Endpoint=sb://[your namespace].servicebus.windows.net;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=[your secret]" />
<add key="ConfigurationFilePath" value="." />
<add key="CampaignManagement_Environment_TestEntry" value="Test" />
<!--- Middletier URLs -->
<!--This value setting key can be set to modify the hostname in the WSDL generated by IIS-->
<!--Enforce having customer or account Id.-->
<add key="EnforceCustomerOrAccountIdInHeader" value="true" />
<!--If negative keyword expansion is enabled, specify the batch limit of entities-->
<!--Timeout for downloading of image data by CDN URL using WebClient-->
<add key="ImageDataDownloadTimeoutInSeconds" value="30" />
<!-- Editorial MT Service Urls-->
<add key="AdInsightServiceEndpoint" value="http://editorialmttest:802/EditorialMT/AdInsightService.svc" />
<add key="CampaignManagement_Tier_TestEntry" value="Test" />
<add key="DependencyXMLFile" value="Dependency.xml" />
<!--Flag to disable WCF channel pool for ClientCenter MT-->
<add key="DisableClientCenterMTChannelPooling" value="false" />
<!--Flag to disable WCF channel pool for CAM MT-->
<add key="DisableCamMTChannelPooling" value="false" />
<!-- use this to specify if request.ToString needs to be logged. Should be set to false in producton -->
<add key="LogRequestInfo" value="true" />
<!-- Quota Usage Percentage above which the value would be picked from DB -->
<add key="QuotaThresholdPercentage" value="10" />
<add key="ThrottleEvtLimit" value="100" />
<add key="FeaturesListFilePath" value="D:\Data\CampaignMiddleTier\AppRoot\API\Configuration\FeatureListFile.config" />
<!--- Middletier URLs -->
<!--This value setting key can be set to modify the hostname in the WSDL generated by IIS-->
<!--Enforce having customer or account Id.-->
<!--If negative keyword expansion is enabled, specify the batch limit of entities-->
<!--Timeout for downloading of image data by CDN URL using WebClient-->
<!-- Editorial MT Service Urls-->
<!-- CampaignManagementService Service Url-->
<add key="CampaignManagementServiceV13LocalEndpoint" value="http://localhost:10094/CampaignManagementService13.svc" />
<!-- BulkService Service Url-->
<add key="BulkServiceV13LocalEndpoint" value="http://localhost:10094/BulkService13.svc" />
<!--Flag to disable WCF channel pool for ClientCenter MT-->
<!--Flag to disable WCF channel pool for CAM MT-->
<!-- use this to specify if request.ToString needs to be logged. Should be set to false in producton -->
<!-- Quota Usage Percentage above which the value would be picked from DB -->
<add key="LogConfigurationFileV13" value="LogConfigurationV13.config" />
<!-- DB AAD Auth Settings -->
<add key="AADAuthUserIdentityClientId" value="" />
<add key="EnableApiResponseCompression" value="true" />
<add key="EnableRestApi" value="false" />
<add key="EnableCampaignRestApi" value="false" />
<add key="EnableGetBIDataForAccountsRestApi" value="false" />
<add key="EnableBulkRestApi" value="false" />
<add key="AutomatedCallToActionEnabled" value="false" />
<add key="DSAAccountNotificationServiceBusConnectionString" value="KeyVaultSecret:DSAAccountNotificationServiceBusConnectionString" />
<add key="DSAAccountNotificationServiceBusTopic" value="fraudaccountevent" />
<add key="DSAAccountNotificationDelayMinutes" value="1" />
<add key="AzureResourceCredentialConfigConnectionString" value="UseLocalAuth=true" />
<!-- Clickhouse migration-->
<add key="ClickhouseAzureStorageAccountConnectionString" value="KeyVaultSecret:ClickhouseAzureStorageAccountConnectionStringKey" />
<add key="AdvBi2ChAdgroupShardgroupMapping" value="1:1" />
<add key="ReportDataServiceDataValidationFiles" value="D:\CampaignMT\ReportDataServiceDataValidationFiles" />
<add key="ClickhouseLoadBalancerPolicy" value="Default" />
<add key="ClickhouseEnv" value="CI" />
<add key="RestApiCorsOrigins" value="" />
</appSettings>
<!--add comma-separated types as sharding gets enabled for them-->
<!-- Note, when adding new DB types below, make sure also put its credential (userid/pwd) into KeyVault for SI/Prod and add its KeyVaultName in this config, see 'CampaignDBKeyVaultName' as an exmaple. Better to test it in LDC3 before check-in as local/CI does not require keyvault -->
<!-- A report preview call timeout is 120 seconds, set the DBCommandTimeout to the same value -->
<!-- Clickhouse migration-->
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-sandbox-bingads-microsoft-com" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-sandbox-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-sandbox-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DistributedQueryServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="95" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SystemMemorySizeCheckPoint2']/@value" value="70" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsLocalFanoutEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,MultiChannelAds,MultiChannelAdsSystemDB,CampaignSharedLibraryShard,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,MultiChannelAds,MultiChannelAdsSystemDB,CampaignSharedLibraryShard,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,MultiChannelAds,MultiChannelAdsSystemDB,CampaignSharedLibraryShard,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShardedDbTypes']/@value" value="AzureAuditHistory,VerticalDBReadOnly,VerticalDB,CampaignAdGroupShard,MultiChannelAds,MultiChannelAdsSystemDB,AdvBiAzureAccount,AdvBiAzureAdgroup,CampaignDB,CampaignNegativeKeywordShard,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvbiAdgroupDVS,AdvbiAzureAccount2,AuditHistoryReport,NegativeKeywordCatalogShard,AdvBIAdgroupDim,AzureChangeHistoryUI,AzureChangeHistorySA,MerchantCenterOfferDB,BIElasticQuery,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DBTypesLoadedFromMetadataService']/@value" value="CampaignDB,CampaignAdGroupShard,CampaignNegativeKeywordShard,VerticalDB,CampaignSharedLibraryShard,AdvBiAccountMT_1,AdvBiAccountMT_2,AdvBIAdgroupDim,AdvbiAdgroupDVS,AdvbiAzureAccount,AdvbiAzureAccount2,AdvbiAzureAdgroup,AuditHistoryReport,AzureAuditHistory,AzureChangeHistorySA,AzureChangeHistoryUI,MultiChannelAds,MultiChannelAdsSystemDB,NegativeKeywordCatalogShard,VerticalDBReadOnly,BIElasticQuery,MerchantCenterOfferDB,BillingDBPartitioned,AdvBiCHAccount,AdvBiCHAdgroup" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="1:1,2:1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="1:1,2:1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LoadBIStatisticsFromCache']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<!-- From CampaignMT Implementation App.Config -->
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadMaxFileSizeInBytes']/@value" value="**********" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadMaxFileSizeInBytes']/@value" value="**********" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\CampaignMT\MultiAccountBulkUpload" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="D:\data\EAP\Bulk\MultiAccountBulkUpload" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiAccountBulkUploadStagingFolderPath']/@value" value="/data/bulk/multiAccountBulkUpload" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\CampaignMT\BulkUploadDownload" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="D:\data\EAP\Bulk\BulkUploadDownload" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDownloadStagingFolder']/@value" value="/data/bulk/bulkUploadDownload" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value=".\CampaignMT\ImportToBulkFiles" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="D:\data\EAP\Import\ImportToBulkFiles" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportToBulkFiles']/@value" value="/data/import/importToBulkFiles" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-si-2024-1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-si-2024-1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsTokensKeyVaultCertName']/@value" value="secretsencryption-prod-2024" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value=".\CampaignMT\AzureStagingForImport" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="D:\data\EAP\Import\AzureStagingForImport" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportStagingFolderPath']/@value" value="/data/import/azureStagingForImport" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value=".\CampaignMT\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="D:\data\EAP\Import\ImportAdWordsIdLocalFilePathForAdDeletion" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdWordsIdLocalFilePathForAdDeletion']/@value" value="/data/import/importAdWordsIdLocalFilePathForAdDeletion" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="D:\data\EAP\Import\ImportUpload" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value=".\CampaignMT\ImportUpload" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncCosmosUploadFolder']/@value" value="/data/import/importUpload" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value=".\CampaignMT\GSyncV2StagingFolder" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="D:\data\EAP\Import\GSyncV2StagingFolder" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GSyncV2StagingFolder']/@value" value="/data/import/gSyncV2StagingFolder" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value=".\CampaignMT\GMBStagingFolder" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="D:\data\EAP\Import\GMBStagingFolder" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GMBStagingFolder']/@value" value="/data/import/gmbStagingFolder" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value=".\CampaignMT\CampaignIdsToUpload" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="D:\data\EAP\Import\CampaignIdsToUpload" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ScheduledImportCampaignIdsUploadFolder']/@value" value="/data/import/campaignIdsToUpload" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\CampaignMT\MIUnifiedData" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="D:\data\EAP\Import\MIUnifiedData" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UnifiedMarketIntelligenceDataFolder']/@value" value="/data/import/miUnifiedData" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\CampaignMT\MIUpLoad" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="D:\data\EAP\Import\MIUpLoad" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceCosmosUploadFolder']/@value" value="/data/import/miUpLoad" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureReportTaskStatusTableName']/@value" value="MarketIntelligenceReportTaskStatus" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MarketIntelligenceAzureEntitySchemaTableName']/@value" value="MarketIntelligenceEntitySchema" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\CampaignMT\MIStagingFolder" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="D:\data\EAP\Import\MIStagingFolder" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MIStagingFolder']/@value" value="/data/import/miStagingFolder" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-si.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-si.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="http://localhost:1901/MdsPublicServiceMock.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceUrl']/@value" value="https://aismdsmt-prod.trafficmanager.net:3101/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-si.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-si.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://localhost:1901/MdsPublicServiceMock.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MdsServiceHealthProbeUrl']/@value" value="http://aismdsmt-prod.trafficmanager.net:3002/v1/AudienceIntelligenceService/MDSHealthProbeService" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AsyncCacheWrite']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogIncomingOrOutgoingRequests']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogIncomingOrOutgoingRequests']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://localhost:10875/AdIntelligenceMTMockService.svc" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://localhost:1901/AdIntelligenceMTMockService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://appsdevdev.redmond.corp.microsoft.com:8223/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='Redmond-CI' and @appinsightmt='true' " ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://int.adinsight-mt.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMtServiceUrl']/@value" value="http://adinsight-mt-ap.trafficmanager.net:823/AdInsightMT/V2/AdInsightMTService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://siccmt.trafficmanager.net:3089/clientcenter/mt" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/mt" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://siccmt.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://siccmt.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/billing" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientcenterBillingBaseaddress']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/billing" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="{LabmanCIMachine}" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="dummy" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="BY2APPPERFSQL03" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="BY2APPPERFSQL03" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="BY2BAMTSQL\BY2BAMTSQL" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="BY2BAMTSQL\BY2BAMTSQL" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="CH1BAMTSQL\CH1BAMTSQL" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="CH1BAMTSQL\CH1BAMTSQL" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="BY2BAMTSQL\BY2BAMTSQL" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server']/@value" value="CH1BAMTSQL\CH1BAMTSQL" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="dummy" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="adCenter_Metadata" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="adCenter_Metadata" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB_Prod_Shadow" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB']/@value" value="MetadataDB_Prod_Shadow" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="{LabmanCIMachine}" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="dummy" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="BY2APPPERFSQL03" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="BY2APPPERFSQL03" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="BY2ACINS1\BY2ACINSSQL1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="BY2ACINS1\BY2ACINSSQL1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="CH1ACCPSQL2\CH1ACCPSQL2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="CH1ACCPSQL2\CH1ACCPSQL2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="BY2ACINS1\BY2ACINSSQL1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_Server_Secondary']/@value" value="CH1ACCPSQL2\CH1ACCPSQL2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="dummy" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="dummy" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="dummy" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="dummy" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Metadata_DB_Secondary']/@value" value="MetadataDB" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://edtmtsi-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://edtmtsi-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://localhost:1901/" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://invalid_editorialurl_failCallFromShadow:802/EditorialMT/" when="@mf='MF_Advertiser_MTAPI_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://invalid_editorialurl_failCallFromShadow:802/EditorialMT/" when="@mf='MF_Advertiser_MT_Campaign_Shadow'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialServiceUrl']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="500" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="500" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="500" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadThrottleRequestsLimit']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenBulkColumns']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowCountAllowed']/@value" value="25000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowCountAllowed']/@value" value="25000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowCountAllowed']/@value" value="12000000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowCountAllowed']/@value" value="12000000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowsInMemory']/@value" value="100" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowsInMemory']/@value" value="500" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadMaxRowsInMemory']/@value" value="500" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="30" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthPingTimeoutSeconds']/@value" value="6" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,127;PriceEstimation1,128,255" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PriceEstimationPartition']/@value" value="PriceEstimation0,0,63;PriceEstimation1,64,127;PriceEstimation2,128,191;PriceEstimation3,192,255" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".beta5.api.idss.msn.com/mobileweb" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".beta5.api.idss.msn.com/mobileweb" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".beta5.api.idss.msn.com/mobileweb" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdcenterMobileUrlSuffix']/@value" value=".adcenterpages.com" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="" when="@mf='MF_Advertiser_MTAPI_Campaign' and @environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsName']/@value" value="adcenterapi.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="" when="@mf='MF_Advertiser_MTAPI_Campaign' and @environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAds']/@value" value="api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="" when="@mf='MF_Advertiser_MTAPI_Campaign' and @environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsCampaign']/@value" value="campaign.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.Sandbox.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="" when="@mf='MF_Advertiser_MTAPI_Campaign' and @environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceHostDnsNameBingAdsBulk']/@value" value="bulk.api.bingads.microsoft.com" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi') and @environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://by2adce052:8022" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://by2adce052:8022" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://localhost:1900/UserMapMockService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreUrl']/@value" value="http://************:8022" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://si_bingads_adintelligence_mt.phx.gbl:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://si_bingads_adintelligence_mt.phx.gbl:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://aidev1:3001/v1/AudienceIntelligenceService/MetadataService/PublicService" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceInteligenceServiceUrl']/@value" value="net.tcp://10.2.7.37:3001/v1/AudienceIntelligenceService/MetadataService/SegmentDefinitionService" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://siccmt.trafficmanager.net:3089/clientcenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/mt/cryptography" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CryptographyServiceUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt/cryptography" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-int-mt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-int-mt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://localhost:806/ReportingMiddleTierMockService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="http://localhost:10875/ReportingMiddleTierMockService.svc" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingMTUrlv2']/@value" value="net.tcp://dvs-prod-cmt-gtm.trafficmanager.net:9798/MiddleTierService/ReportMTV2.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-int-mt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-int-mt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://localhost:1900/ReportingAggregatorMockService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://localhost:10875/ReportingAggregatorMockService.svc" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportingAggregatorUrl']/@value" value="http://dvs-prod-cmt-gtm.trafficmanager.net:8020/TypedService/AdCenterV35.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="30" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ReportMTPingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="30" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='InternalWCFServicePingTimeoutSeconds']/@value" value="3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BatchSizeForAudienceReadUsingMap']/@value" value="7000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BatchSizeForAudienceReadUsingMap']/@value" value="7000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MinBatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MinBatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MinBatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MinBatchSizeForAudienceReadUsingMap']/@value" value="4000" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\By2apsesc01\UIDiskCache" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\By2apsesc01\UIDiskCache" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="d:\temp" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\BY2BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\BY2BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\CH1BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\CH1BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\BY2BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportFileShare']/@value" value="\\CH1BACMMTFS\CampaignImport" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxRowsReturnedFromImportDB']/@value" value="10" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxRowsReturnedFromImportDB']/@value" value="10" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFraudCheck']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_PartialSuccessDueToAdgroupSharding']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestFunctionality']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DynamicSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_LinkedInForSearchBscDsa']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ResponsiveSearchAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_EnhancedResponsiveAd']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_FinalUrlSuffix']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ImageAdExtensionsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_HouseholdIncomeTargeting']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SearchCompanies']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignFraudStatusEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountSummaryReportGridEntityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://sifraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://sifraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="net.tcp://appsdevdev:4326/Int/FraudInt.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudServiceUrl']/@value" value="http://fraudmt-gtm.trafficmanager.net:4325/Int/FraudInt.svc/NonSecureEndPoint" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FraudMTTimeoutInMilliseconds']/@value" value="0" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="525501,2151741,2566020" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="525501,2151741,2566020" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UserIdsUsedForYahooMigration']/@value" value="957323" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://api.si.bingads.microsoft.com/Api/Advertiser/V9Beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://api.si.bingads.microsoft.com/Api/Advertiser/V9Beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DomainDataDownloadBaseUrl']/@value" value="https://maf.adcenter.microsoft.com/Api/Advertiser/V9beta/CampaignManagement/DomainData/" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://apiqsgint.playmsn.com:83/bdigeneric/xmlxml.aspx?atlahostname=team-ads.XAP-Prod-Bn1.BN1.ap.phx.gbl&amp;atlaport=89" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://apiqsgint.playmsn.com:83/bdigeneric/xmlxml.aspx?atlahostname=team-ads.XAP-Prod-Bn1.BN1.ap.phx.gbl&amp;atlaport=89" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://localhost:1901/CheckLiveOffersMock.aspx" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIURL']/@value" value="http://fd.loadbalancer.binginternal.com/bdigeneric/xmlxml.aspx" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=SOSPartners" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=AdsInt" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=AdsInt" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterFlightHeader']/@value" value="xap=Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterHeadersEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Environment_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://c1_si_bingads_editorial_mt.phx.gbl:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://c2_si_bingads_editorial_mt.phx.gbl:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://BY2ADCK159:802/EditorialMT/AdInsightService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightServiceEndpoint']/@value" value="http://editorialmt01-GTM.trafficmanager.net:802/EditorialMT/AdInsightService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagement_Tier_TestEntry']/@value" value="Test" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnforced']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="false" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnabled']/@value" value="true" when="(@mf='MF_CampaignAPI' or @mf='MF_CampaignAPICore' or @appName='campaignapi' or @mf='MF_ODataAPI' or @appName='odata') and @environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableGlobalCache']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnableLimitForSpecificCustomers']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="1000" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitPerPeriod']/@value" value="300" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForPollStatus']/@value" value="500" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkDownload']/@value" value="500" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkUpload']/@value" value="500" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkUpload']/@value" value="500" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkUpload']/@value" value="500" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkDownload']/@value" value="500" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleLimitForBulkDownload']/@value" value="500" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxEnabled']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SandboxHostPrefix']/@value" value="api.Sandbox.bingads" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://c1_fileupload.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://fileupload.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B' and @aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://c2_fileupload.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://campaign.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://tip_fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadUrl']/@value" value="https://tip_fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://c1_fileupload.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://fileupload.api.sandbox.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B' and @aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://c2_fileupload.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://tip_fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkUploadDirectUrl']/@value" value="https://tip_fileupload.api.bingads.microsoft.com/Api/Advertiser/CampaignManagement/FileUpload/File/UploadBulkFile" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxBulkEntitiesFetchedFromDatabase']/@value" value="1000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Click2CallQueuePauseDelayInHours']/@value" value="0" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Click2CallQueuePauseDelayInHours']/@value" value="0" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Click2CallQueuePauseDelayInHours']/@value" value="0" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="5000" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="5000" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="5000" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="300000" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="300000" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="300000" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="300000" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="5000" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DynamicConfigRefreshIntervalMillis']/@value" value="5000" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\By2apsesc01\BISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\By2apsesc01\BISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\BingDrops\AppsServiceCIShare\CI_DataFiles\PPSCredentialShare\QueueProcessorCrededntials_encrypted.xml" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\BY2BACMMTFS\AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\BY2BACMMTFS\AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\CH1BACMMTFS\BCP_AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\CH1BACMMTFS\BCP_AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\BY2BACMMTFS\AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CredentialsFilePath']/@value" value="\\CH1BACMMTFS\BCP_AppNexusBISyncPassStore\PhoneProvisioningServiceCredentials_encrypted.xml" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/pmt"  when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://siccmt.trafficmanager.net:3089/clientcenter/pmt" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://siccmt.trafficmanager.net:3089/clientcenter/pmt" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationEndpointUrl']/@value" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/pmt" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptionCertificateThumbprint']/@value" value="NA" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedKey']/@value" value="KeyVaultSecret:EncryptedKey-RedmondCI" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="o9D7ssf3F7fxkuLlvuPP9y5Zuh4KoYkuqsUI6r0gVUby3XqJT30M5wBmPb2a3OluT7DCt+5SPcIVPs2edb4INp05SuF46PHSvSQw2DXHlqU6pXHlm8W8xX/VlwLV8XtYtBIpNjD6Fte6PQaADCyiiiB6liaLrxtge9E8S1R0BzI5PkONsPBA6OK0qE6deCEI9LLz9V7MB29tiUWbbQhVRJlBrmykjJQk7dy5Nz9G+RJO29K0Q1B6MhEpuj4eJ45pdJxJyma9bBIyu8PoPx8Nti8Wqxl3aK9S8bUL/+mPw/5JmDeg+GofBtkmXApDnfeZTGZA8TjfgTuTHgzAlyj1Zw==" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EncryptedIV']/@value" value="qTaFujxA6HkQPRyoQFgKspqxMxkOAMsBHeYw3sytWy1VODlFsdiDJQbcdFzJvkEHlWSEzUATQs2LeUtgf5DJG69E50GdE2fEnneF0tgQ4yx1Lfpx0ROn8HHLY5x4p/9v6e0oKPUvq7p9dzDVpghhlpzm0vTj842PrWUvXlgqFLZ5iIXxfA7aKdbzVrZNdhoKm8bWpLJE4yow6D69y5qJn3KpI3u5AbSz+i2n/94CvcObf/tvE+d92tgop2FLWGSKq326etp3ktRGKb9qTwEmo3/jp4vNi6BI+D1CFdRafn9DQVVMUnHRLcKqULVyoCOixp5WoMmX+DotQhVRCWf/cw==" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdGroupCallTrackingEligibilityMinClicks']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdGroupCallTrackingEligibilityMinClicks']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdGroupCallTrackingEligibilityMinSpend']/@value" value="10" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdGroupCallTrackingEligibilityMinSpend']/@value" value="10" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SharedPartition.InitialAssociationLoadInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='SharedPartition.InitialAssociationLoadInSeconds']/@value" value="300" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogConfigurationPath']/@value" value=".." when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='LogConfigurationFile']/@value" value="LogConfiguration.config" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='LogSchemaFile']/@value" value="LogConfiguration.xsd" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='SecurityTicketExpirationBufferInSeconds']/@value" value="60" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ConfigServiceEnabled']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BlacklistEnabled']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='VirtualEarthCommonServiceUrl']/@value" value="https://common.virtualearth.net/find-30/common.asmx" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='GenerateLocationsXMLOnStartup']/@value" value="0" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='UserMapStoreTimeoutSeconds']/@value" value="300" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='StandardSegmentsCacheValidityInSeconds']/@value" value="300" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='MinimumClicksForLowBudgetStatus']/@value" value="10" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='DBConnectionTimeout']/@value" value="10" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='RetryFailedDBOperation']/@value" value="4" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='MonthlyBudgetMinimum']/@value" value="5" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BudgetPercent']/@value" value="1" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='RefreshCustomerFeatureCacheMinutes']/@value" value="1" when="@environment='Redmond-CI'"?>
<?ap-config target="/configuration/appSettings/add[@key='LiveMarketDistributionChannel']/@value" value="UnitedStates,CanadaEN,CanadaFR" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='AdGroupMaxEndDate']/@value" value="1/1/2050 23:59:59" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ReadOnlyEnabled']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordExpansionEntityLimit']/@value" value="25" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='StructuredNegativeKeywordEntityLimit']/@value" value="1" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='StructuredNegativeKeywordLimitForBulkUpload']/@value" value="10000" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='StructuredNegativeKeywordAddDeleteBatchLimit']/@value" value="1000" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ImportDoEditorialValidationsDuringInitialFileUpload']/@value" value="true" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ImportDoNormalizationDuringInitialFileUpload']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ValidateCamAssets']/@value" value="true" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='SmallBatchLimit']/@value" value="20" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='RetryWaitTimeInMinutes']/@value" value="1" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='AssetSchemasCacheValidDurationInSeconds']/@value" value="600" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='EnforceMonthlySpendCheckForCampaignBudgetUpdate']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='NKWConflictsReadyFromBI']/@value" value="true" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BulkEntryThresholdInPercentage']/@value" value="30" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='IsTaskPostponedFeatureEnabled']/@value" value="true" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='RunBulkRequestsInLowPriorityThreads']/@value" value="false" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='EnableDbTrackingIdLogging']/@value" value="true" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterApplicationId']/@value" value="A59218844EBFD8F318DD52C4AB6A267A7C93D47E" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterDestinationSchema']/@value" value="BdiGeneric.VerifyFiltersResponseData[1.1]" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDIVersion']/@value" value="1.0" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterVersionHeader']/@value" value="1.0" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterBDITimeoutSeconds']/@value" value="3" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='BingMerchantCenterTimeoutSeconds']/@value" value="3" when="true()"?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEvtLimit']/@value" value="100" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='CloseConnectionOnNthCallEnabled']/@value" value="true" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='CloseConnectionRequestCount']/@value" value="6000" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnforceCustomerOrAccountIdInHeader']/@value" value="true" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='DependencyXMLFile']/@value" value="Dependency.xml" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableClientCenterMTChannelPooling']/@value" value="false" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableCamMTChannelPooling']/@value" value="false" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='CacheExpiryDurationInSeconds']/@value" value="300" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogRequestInfo']/@value" value="true"  when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='QuotaThresholdPercentage']/@value" value="10" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableClick2CallQueue']/@value" value="true" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConnectionDrainMonitorFilePath']/@value" value="D:\data\ConnectionDrain\ConnectionDrain.txt" when="true()" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='GetAdGroupCriterionsAdGroupEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FlexLocationHierarchyBitMap']/@value" value="1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="115" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="SIP" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="SIP" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="115" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="115" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="CO3" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="CO3" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="CH1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="CH1" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="BY2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DataCenterName']/@value" value="CH1" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="LDC0" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="LDC1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="LDC2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="LDC0" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CO3-LDC1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CO3-LDC2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CO3-LDC3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CH1-LDC3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CH1-LDC1" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LDC']/@value" value="EAP-CH1-LDC2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_DataTableAdExtension']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppAdExtensionAutoGoalCreationEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_closeTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_closeTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_openTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_openTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_receiveTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_receiveTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_sendTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_sendTimeout']/@value" value="00:00:04" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_closeTimeout']/@value" value="00:01:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_openTimeout']/@value" value="00:01:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_receiveTimeout']/@value" value="00:01:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdInsightMt_sendTimeout']/@value" value="00:01:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DbTypesUsingFlattenedShardGroupMap']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://localhost:818" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://metadataservice-si-adsapps.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://metadataservice-si-adsapps.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://bcp-metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://bcp-metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://c3-ch01-metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataServiceEndpoint']/@value" value="http://c3-mw1-metadataservice.trafficmanager.net:818" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ChecksumDBTypes']/@value" value="CampaignDB,CampaignAdGroupShard" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Editorial_closeTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Editorial_openTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Editorial_receiveTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='Editorial_sendTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialShortTimeout_closeTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialShortTimeout_openTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialShortTimeout_receiveTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EditorialShortTimeout_sendTimeout']/@value" value="00:10:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://localhost:3877/ExecutionManagement" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://sitaskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://sitaskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://localhost:3877/ExecutionManagement" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://tip-taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TaskEngineUrl']/@value" value="http://tip-taskengine.trafficmanager.net:3877/ExecutionManagement" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString-LDC3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString-LDC3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubConnectionString']/@value" value="KeyVaultSecret:EventHubConnectionString" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionprod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionprod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionprod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionprod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionldc3" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionldc3" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionsi" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubName']/@value" value="anomalydetectionsi" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionprod-ns.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionprod-ns.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionprod-ns.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionprod-ns.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionsi.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionsi.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionsi.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubEndpoint']/@value" value="sb://anomalydetectionsi.servicebus.windows.net" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEventHubManagedIentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsEAPEnv']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClientCenter.UserSecurityClient.IsTestEnvironment']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoConnectionLoggerEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoUrl']/@value" value="https://ingest-bingads.kusto.windows.net:443" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoUrl']/@value" value="https://ingest-bingads.kusto.windows.net:443" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoUrl']/@value" value="https://ingest-bingads.kusto.windows.net:443" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoUrl']/@value" value="https://ingest-bingads.kusto.windows.net:443" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="D:\DATA\EAP\DesktopFileUploadLocation" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DownloadedFileLocation']/@value" value="/data/desktopFileUploadLocation" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFileStreamStorage']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableFileStreamStorage']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogConnectionDetailsForSqlExceptions']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogConnectionDetailsForSqlExceptions']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='LogConnectionDetailsForSqlExceptions']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ServiceStopWhenInitFail']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableDynamicConfigOverride']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableDynamicConfigOverride']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableDynamicConfigOverride']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfig']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableUniversalConfigMonitor']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableLoadFromUniversalConfigFile']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC1-CH01" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC1-MW1" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC2-CH01" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC2-MW1" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC3-CH01" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-LDC3-MW1" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-SI-LDC1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignHealthProbeServiceBusTopicName']/@value" value="CampaignHealthProbeServiceBusTopic-SI-LDC2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableAzureBlobStorageBasedAccountParentCustomerIdLoad']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableAzureBlobStorageBasedAccountParentCustomerIdLoad']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountIDCustomerIDCacheRefreshInterval']/@value" value="00:00:00" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AccountIDCustomerIDCacheRefreshInterval']/@value" value="00:00:00" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OptimizedLocationCacheLoad']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ConditionalPeriodicCacheRefreshEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_TestConfiguration']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVCI" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVSI" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSecretsKeyVaultName']/@value" value="CampaignSecretsKVProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="UseDevelopmentStorage=true;" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageConnectionString']/@value" value="KVSecret:campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataKeyVaultEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDataStorageAccountName']/@value" value="campaigndbdonotdropprod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="campaigndbdonotdropci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TextAssetStorageAccountName']/@value" value="textassetsprodwus" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="CampaignSecretsKVCI" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultSI" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetadataDBKeyVaultName']/@value" value="MetadataDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignStaticKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignAdGroupShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignNegativeKeywordShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignSharedLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='NegativeKeywordCatalogShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsServiceBusConnectionString']/@value" value="KeyVaultSecret:MultiChannelAdsServiceBusConnectionStringCI" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsServiceBusConnectionString']/@value" value="KeyVaultSecret:MultiChannelAdsServiceBusConnectionStringTIP" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignEntityLibraryShardKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='VerticalDBReadOnlyKeyVaultName']/@value" value="VerticalDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureAuditHistoryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AuditHistoryReportKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAzureAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_1KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiAccountMT_2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvertiserBIBSCKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MerchantCenterOfferDBKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BIElasticQueryKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIAdgroupDimKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistoryUIKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureChangeHistorySAKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignBulkDBv2KeyVaultName']/@value" value="SAKeyvaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AppsConsolidatedInAzureKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BillingDBPartitionedKeyVaultName']/@value" value="AnBDBKeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='PubBIDbKeyName']/@value" value="PubBI" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBIBSCShardedKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAdgroupDVSKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvbiAzureAccount2KeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAccountKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBiCHAdgroupKeyVaultName']/@value" value="BIDataMartsKVMTProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultSI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OrderManagementSystemDBKeyVaultName']/@value" value="CampaignDB-KeyVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='DisableProfileDataRefresh']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableProfileDataInitializationForLinkedInCampaign']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="4370957c1e379360328f08dbb8cb60bf3204346f" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="a6891a60b50398be469d5bde6085ae49c75d9c3c" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="a6891a60b50398be469d5bde6085ae49c75d9c3c" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonation_ClientCertificateThumbprint']/@value" value="‎62c7b45a863f31602df6b805004c88018f83a989" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-ci" when="@environment='Redmond-CI' and @aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-si" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-si" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CcmtHttpsImpersonationTokenCertificateName']/@value" value="ads-ccimpersonation-prod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="CI" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-SI" when="@environment='BingAdsServiceEAPLDC1-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-SI" when="@environment='BingAdsServiceEAPLDC2-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-SI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-SI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC3-Prod-BCP" when="@environment='BingAdsServiceEAPLDC3-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC3-Prod-Primary" when="@environment='BingAdsServiceEAPLDC3-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-Prod-BCP" when="@environment='BingAdsServiceEAPLDC1-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-Prod-BCP" when="@environment='BingAdsServiceEAPLDC2-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-Prod-Primary" when="@environment='BingAdsServiceEAPLDC1-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-Prod-Primary" when="@environment='BingAdsServiceEAPLDC2-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC3-Prod-BCP" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC3-Prod-Primary" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-Prod-BCP" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-Prod-BCP" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC1-Prod-Primary" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleEnvironmentIdentifier']/@value" value="LDC2-Prod-Primary" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-eus-si-redis-throttle.redis.cache.windows.net" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-eus-si-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-eus-si-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheEntryPoint']/@value" value="cmmt-wus-bcp-redis-throttle.redis.cache.windows.net" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-eus-si-redis-throttle" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-eus-si-redis-throttle" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-eus-si-redis-throttle" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ThrottleRedisCacheSecretKey']/@value" value="cmmt-wus-bcp-redis-throttle" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://c1-mw1-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://c1-ch01-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://c2-mw1-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://c2-ch01-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://tip-odataapi-bingads.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://tip-odataapi-bingads.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://c1-si-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Advertiser/V2" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://localhost:18080/ODataApi/Advertiser/V2" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='OdataEndpointAddress']/@value" value="http://{Env:ODATA_LOADBALANCER_INTERNAL_SERVICE_HOST}:{Env:ODATA_LOADBALANCER_INTERNAL_SERVICE_PORT}/ODataApi/Advertiser/V2" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsDataServiceForReportingEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\CampaignMT\ExperimentStagingFolder" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="D:\data\EAP\ExperimentStagingFolder" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ExperimentStagingFolder']/@value" value="/data/experimentStagingFolder" when="@aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MetricClientEnabledForPartitionSupport']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='FeedUploadTaskTypeName']/@value" value="FeedUploadTest" when="starts-with(@environment, 'BingAdsServiceEAPLDC3-Prod')" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MaxDurationToCacheBigTokenInMinutes']/@value" value="60" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultName']/@value" value="MCAVaultProd" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='MultiChannelAdsSecretsKeyVaultAppId']/@value" value="e4fd439e-0b7b-485c-84c2-9ed967f6c565" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='McaEnvironment']/@value" value="PROD" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_ApiUploadEntityRecords']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ShowHiddenWsdlEntries_SponsorProductAdsV2']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignManagementServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-Co3'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-Ch1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='BulkServiceV13LocalEndpoint']/@value" value="http://localhost:10094/Api/Advertiser/CampaignManagement/V13/BulkService.svc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="271efe02-3add-46b7-be27-ecfac6c3f45c" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="271efe02-3add-46b7-be27-ecfac6c3f45c" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AADAuthUserIdentityClientId']/@value" value="4da2b3aa-08ce-4220-a9f5-6f3df43552fe" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AggregatorKeywordNetworkOptimizationThreshold']/@value" value="500" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertThumbprint']/@value" value="f57685d922b4157b04506dcf6152bd22cda5a865" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLPort']/@value" value="10098" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLPort']/@value" value="443" when="@environment='Redmond-CI' and @aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-ci-ads-microsoft-com" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-sandbox-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-sandbox-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignServiceSSLCertName']/@value" value="api-bingads-microsoft-com" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='IsOneCertEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ODataNetCorePort']/@value" value="18080" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultUseManagedIdentity']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KeyVaultRunAs']/@value" value="RunAs=App;AppId=6bbff5f7-3611-4603-95b0-650c9003a41d" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="10095,10096,10097" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="" when="@environment='Redmond-CI' and @aks='true'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnAlternatePorts']/@value" value="801" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTManageIdentityId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableCampaignRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableGetBIDataForAccountsRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='EnableBulkRestApi']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AutomatedCallToActionEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ListenOnMultipleAddresses']/@value" value="false" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="20" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="20" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="20" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="20" when="@environment='Local'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AudienceGroupMaxBatchSize']/@value" value="100" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="1:1,2:1" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="1:1,2:1" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AdvBi2ChAdgroupShardgroupMapping']/@value" value="19:1,7:1,1:2,26:2,2:3,12:3,3:4,17:4,4:5,18:5,9:6,22:6,11:7,23:7,10:8,25:8" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="SI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ClickhouseEnv']/@value" value="Prod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="false" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoManagedIdentityEnabled']/@value" value="true" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="UseLocalAuth=true" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ClientId=cf627068-a4da-44b7-9d67-c9fee3ce6653;TenantId=72f988bf-86f1-41af-91ab-2d7cd011db47;ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureResourceCredentialConfigConnectionString']/@value" value="ManagedIdentityId=c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="0fb88dd0-e942-4c39-9aa5-8326638948f9" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="45479217-77a7-44ed-bd51-b52cc96d87e3" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationId']/@value" value="c93e4409-bc06-4a83-ab6c-b6863b8a11cc" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageConnectionString']/@value" value="KeyVaultSecret:ImportAzureStorageCIConnectionString" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTClientCertName']/@value" value="bingads-campaignmt-clientcert-corp" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='CampaignMTClientCertName']/@value" value="bingads-campaignmt-clientcert-pme" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationCertName']/@value" value="bingads-campaignmt-clientcert-corp" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='KustoApplicationCertName']/@value" value="bingads-campaignmt-clientcert-pme" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationsSAConnString']/@value" value="KeyVaultSecret:ImportAzureStorageCIConnectionString" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationsSAConnString']/@value" value="KeyVaultSecret:ImportPMEAzureStorageConnectionString" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationsSAConnString']/@value" value="KeyVaultSecret:ImportPMEAzureStorageConnectionString" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecommendationspme" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportRecommendationStorageAccountName']/@value" value="importrecoprodsa" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstoragesi" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageAccountName']/@value" value="importstorageprod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstoragesipme" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportPMEAzureStorageAccountName']/@value" value="importstorageprodpme" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="importstorageci" when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstoragesi" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC3-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageAccountName']/@value" value="bingadsappsstorageprod" when="@environment='BingAdsServiceEAPLDC3-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAzureStorageConnectionString']/@value" value="KeyVaultSecret:ImportAzureStorageCIConnectionString" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='ImportAdsAppsStorageConnectionString']/@value" value="KeyVaultSecret:ImportAzureStorageCIConnectionString" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMILocal" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMICI"  when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionString" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='AzureBlobConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMILocal" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMICI"  when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionString" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='TipTileConnectionString']/@value" value="KeyVaultSecret:AzureBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMILocal" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMICI"  when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionString" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigBlobConnectionString']/@value" value="KeyVaultSecret:UniversalConfigBlobConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMILocal" when="@environment='Redmond-CI'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMICI"  when="@environment='Redmond-CI' and @tenant='CORP'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionString" when="@environment='Redmond-CI' and @tenant='PME'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-SI-BN2B'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-CH01'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC3-PROD-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC1-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='UniversalConfigServiceBusConnectionString']/@value" value="KeyVaultSecret:UniversalConfigServiceBusConnectionStringMI" when="@environment='BingAdsServiceEAPLDC2-Prod-MW1'" ?>
<?ap-config target="/configuration/appSettings/add[@key='RestApiCorsOrigins']/@value" value="http://app1.localhost.redmond.corp.microsoft.com:12000" when="@environment='Redmond-CI'" ?>
</configuration>
