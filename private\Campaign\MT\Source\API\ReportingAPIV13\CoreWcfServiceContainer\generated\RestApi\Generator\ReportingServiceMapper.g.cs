﻿using System;
using System.Collections.Generic;

namespace HostedService.ReportingService
{
    public class ReportingServiceMapper
    {
        private static readonly string[] POST = ["POST"];
        private static readonly string[] PUT = ["PUT"];
        private static readonly string[] DELETE = ["DELETE"];

        public static IEnumerable<(string[], string, Func<object, object>, Type, string, Type)> GetMethods(IReportingService service) =>
            [
                (POST, "Reporting/v13/GenerateReport/Submit", r => service.SubmitGenerateReport((SubmitGenerateReportRequest)r), typeof(SubmitGenerateReportRequest), "SubmitGenerateReport", typeof(SubmitGenerateReportResponse)),
                (POST, "Reporting/v13/GenerateReport/Poll", r => service.PollGenerateReport((PollGenerateReportRequest)r), typeof(PollGenerateReportRequest), "PollGenerateReport", typeof(PollGenerateReportResponse)),
            ];
    }
}