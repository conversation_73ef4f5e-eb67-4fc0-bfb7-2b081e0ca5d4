﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <!-- CampaignMT -->
    <appSettings>
        <add key="AdCenterEnvironment" value="##TestEnvironmentName##" />
        <add key="RunInstanceId" value="##TestRunInstanceId##" />
        <add key="CampaignServiceVersion" value="##ServiceBuildNumber##" />
        <add key="DataCenterId" value="1" />
        <add key="ValidateDB" value="true" />
        <!-- IT SHOULD BE SET TO FALSE FOR LOAD TESTS -->
        <add key="FindLogsForFailedTests" value="true" />
        <!-- Use Customers.xml -->
        <add key="UseCustomUserCredential" value="false" />
        <add key="DefaultCustomerPartition" value="" />
        <add key="UseShardGroupHistogramForCampaignDb" value="true" />
        <!-- Logger Configuration -->
        <!-- LogLevel Values: Info, Debug, Warn, <PERSON><PERSON><PERSON>, Fatal -->
        <add key="LogLevel" value="Info" />
        <add key="LogToFile" value="false" />
        <add key="LogFilePath" value="\\machine\share\log.txt" />
        <!-- todo: Retrieve from BiSync web.config -->
        <!--<add key="ReportingMiddleTierServiceMock" value ="net.tcp://advertisertest2:806/AdvertiserMocks/ReportingMiddleTierMockService.svc" />-->
        <add key="ReportingMiddleTierServiceMock" value="net.tcp://BY2ADCK585:806/ReportingMiddleTierMockService.svc" />
        <!--SI-->
        <!--<add key="InternalUserNameForYahooMigration" value="5bhtUGGZMKXKQgA7iR"/>
    <add key="InternalUserPasswordForYahooMigration" value="Gl8BFcK6SKzGYEj"/>-->
    <!--pace-->
    <add key="InternalUserNameForYahooMigration" value="BWj8yZxON" />
    <add key="InternalUserPasswordForYahooMigration" value="bBc9hh1u" />
    <add key="WaitForCCMTCacheUpdateSec" value="15" />
    <add key="Clientcenter.UserSecurityClient.IsTestEnvironment" value="true"/>
  </appSettings>
    <startup>
    </startup>
</configuration>
