﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <appSettings file="DoNotCheckin.config">
        <!--<add key="AdCenterEnvironment" value="br2pubdevgen02" />-->
        <add key="AdCenterEnvironment" value="LocalApps" />
        <!--<add key="AdCenterEnvironment" value="SI" />-->
        <add key="ReportingAggregatorUrl" value="net.tcp://by2adck162:806/AdvertiserMocks/ReportingAggregatorMockService.svc" />
        <add key="MiddleTierEndPointName" value="http://$ADCORECAMPAIGNMTSERVER$:10094/CampaignMT/Service.svc" />
        <add key="MiddleTierSyncEndPointName" value="http://$ADCORECAMPAIGNMTSERVER$:10094/CampaignMT/SyncService.svc" />
        <add key="MiddleTierAggregatorEndPointName" value="http://$ADCORECAMPAIGNMTSERVER$:10094/CampaignMT/AggregatorService.svc" />
        <add key="ClientCenterMT" value="net.tcp://$CLIENTCENTERMT30SERVER$:$CLIENTCENTERMT_PORT$/clientcenter/mt" />
        <add key="CampaignMTFraudCallbackUrl" value="http://advertisertest2.redmond.corp.microsoft.com:10094/CampaignMT/V8.3/FraudCallback.svc" />
        <add key="UseConfigCustomerAccountUser" value="false" />
        <add key="DefaultCustomerPartition" value="" />
        <add key="MTConfigFilePath" value="\\CO2APSC115V2\Configurations\CampaignMT" />
        <add key="EditorialMockURL" value="http://by2adck162:808/AdvertiserMocks/" />
        <add key="CustomerCacheFolderName" value="Default" />
        <!-- IT SHOULD BE SET TO FALSE FOR LOAD TESTS -->
        <add key="FindLogsForFailedTests" value="true" />
        <add key="CampaignServiceVersion" value="9.6" />
        <add key="PrintLogsInConsole" value="true" />
        <!-- pace -->
        <add key="CustomUserName" value="aa254f819e6ed20e" />
        <add key="CustomPassword" value="password" />
        <add key="UserName" value="2ebca75c20164e4" />
        <add key="Password" value="password" />
        <add key="CustomerID" value="********" />
        <add key="AccountID" value="********" />
        <add key="CampaignSecretsKeyVaultName-TEST" value="CampaignSecretsKVSI"/>
        <add key="CampaignSecretsKeyVaultName-TEST-CORP" value="CampaignSecretsKVCI"/>
        
        <add key="CpmEnabledUserName" value="devtest1api" />
        <add key="CpmEnabledPassword" value="livesearch" />
        <add key="CpmEnabledCustomerID" value="264388" />
        <add key="CpmEnabledAccountID" value="326807" />
        <add key="CashBackEnabledUserName" value="devtest4api" />
        <add key="CashBackEnabledPassword" value="livesearch" />
        <add key="CashBackEnabledCustomerID" value="264391" />
        <add key="CashBackEnabledAccountID" value="256493" />
        <add key="QualityScoreEnabledUserName" value="devtest3api" />
        <add key="QualityScoreEnabledPassword" value="livesearch" />
        <add key="QualityScoreEnabledCustomerID" value="264390" />
        <add key="QualityScoreEnabledAccountID" value="256492" />
        <add key="QualityScoreDisabledUserName" value="devtest4api" />
        <add key="QualityScoreDisabledPassword" value="livesearch" />
        <add key="QualityScoreDisabledCustomerID" value="264391" />
        <add key="QualityScoreDisabledAccountID" value="256493" />
        <add key="CashBackEnabledSimpleDirectUserName" value="devtest3api" />
        <add key="CashBackEnabledSimpleDirectPassword" value="livesearch" />
        <add key="CashBackEnabledSimpleDirectCustomerID" value="1" />
        <add key="CashBackEnabledSimpleDirectAccountID" value="256492" />
        <add key="AdExtensionEnabledUserName" value="cctest" />
        <add key="AdExtensionEnabledCustomerID" value="171" />
        <add key="AdExtensionEnabledAccountID" value="13103" />
        <add key="AdultAdsUserName" value="AdultUser1" />
        <add key="AdultAdsPassword" value="livesearch" />
        <add key="NotAdultAdsUserName" value="NotAdultUser9" />
        <add key="NotAdultAdsPassword" value="livesearch" />
        <add key="AdultAdsCustomerID" value="264388" />
        <add key="AdultAdsAccountID" value="326807" />
    </appSettings>
</configuration>