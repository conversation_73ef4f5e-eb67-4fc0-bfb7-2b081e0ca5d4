﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="AdWordsApi" type="System.Configuration.DictionarySectionHandler"/>
  </configSections>
  
  <!-- CampaignMT -->
  <appSettings>
    <!-- C2CTestLoopRetries * C2CTestLoopSleepMsIntervalMs / 60 equals total potential retry wait timeout in MilliSeconds -->
    <add key="C2CTestLoopRetries" value="300"/>
    <add key="C2CTestLoopSleepMsIntervalMs" value="5000"/>
    <add key="EnableC2CTestsForACS" value="true" />
    <add key="ACSPhoneNumberTableName" value="ACSPhoneNumberPoolTest" />
    <add key="ACSMappingTableName" value="ACSVNMappingTest" />
    <!--<add key="AdCenterEnvironment" value="LocalApps" />-->
    <add key="AdCenterEnvironment" value="##TestEnvironmentName##"/>
    <!--<add key="AdCenterEnvironment" value="appsdevdev" />-->
    <!--<add key="AdCenterEnvironment" value="SI-" />-->
    <add key="RunInstanceId" value="##TestRunInstanceId##"/>
    <add key="CampaignServiceVersion" value="##ServiceBuildNumber##"/>
    <add key="DataCenterId" value="1"/>
    <add key="ValidateDB" value="true"/>
    <!-- IT SHOULD BE SET TO FALSE FOR LOAD TESTS -->
    <add key="UseConfigCustomerAccountUser" value="false"/>
    <add key="FindLogsForFailedTests" value="true"/>
    <add key="PrintLogsInConsole" value="true"/>
    <!-- Use Customers.xml -->
    <add key="UseCustomUserCredential" value="false"/>
    <add key="DefaultCustomerPartition" value=""/>
    <add key="UseShardGroupHistogramForCampaignDb" value="true"/>
    <!-- Logger Configuration -->
    <!-- LogLevel Values: Info, Debug, Warn, Error, Fatal -->
    <add key="LogLevel" value="Info"/>
    <add key="LogToFile" value="false"/>
    <add key="LogFilePath" value="\\machine\share\log.txt"/>
    <!-- todo: Retrieve from BiSync web.config -->
    <!--<add key="ReportingMiddleTierServiceMock" value ="net.tcp://advertisertest2:806/AdvertiserMocks/ReportingMiddleTierMockService.svc" />-->
    <add key="ReportingMiddleTierServiceMock" value="net.tcp://BY2ADCK585:806/ReportingMiddleTierMockService.svc"/>
    <add key="ReportingShare" value="d:\Share"/>
    <!-- uncomment to override url for functional tests - resolves bug when running local FTs against CI box -->
    <!--<add key="ReportingAggregatorUrl" value="http://WIN-O62VPDLH7OK:1900/ReportingAggregatorMockService.svc"/> -->
    <!--SI-->
    <!--<add key="InternalUserNameForYahooMigration" value="5bhtUGGZMKXKQgA7iR"/>
    <add key="InternalUserPasswordForYahooMigration" value="Gl8BFcK6SKzGYEj"/>-->
    <!--pace-->
    <add key="InternalUserNameForYahooMigration" value="BWj8yZxON"/>
    <add key="InternalUserPasswordForYahooMigration" value="bBc9hh1u"/>
    <add key="MMC_API_Server" value="mmcapi.ads-int.microsoft.com" />
    <add key="BackCompatDetectorBasePath" value="D:\BackCompactDetector"/>
    <add key="BackCompatDetectorCICampaignMTAppPath" value="D:\App\CampaignMT\AppRoot"/>
    <add key="BackCompatDetectorCIReportingAPIV13AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V13"/>
    <add key="BackCompatDetectorCIReportingAPIV12AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V12"/>
    <add key="BackCompatDetectorCIReportingAPIV11AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V11"/>
    <add key="CopyBackCompatDetectorFromBuild" value="false"/>
    <add key="WaitForCCMTCacheUpdateSec" value="15"/>
    <!-- Use for PlateformE2E Test (Decryption Use)-->
    <add key="DecryptionKeyDir" value="."/>
    <add key="ClientcenterBillingBaseaddress" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/billing"/>
    <add key="EnableUETMigrationTestsForSI" value="true"/>
    <add key="FileDownloadRetryCount" value="10"/>
    <add key="FileDownloadRetryWaitInMilliSeconds" value="1000"/>
    <add key="CampaignSecretsKeyVaultName-TEST" value="CampaignSecretsKVSI"/>
    <add key="CampaignSecretsKeyVaultName-TEST-CORP" value="CampaignSecretsKVCI"/>
    <add key="CampaignSecretsKeyVaultRunAs-TEST" value="RunAs=App;AppId=644646e3-7089-4e32-b417-24edfa15f2f9" />
    <add key="CustomerCacheFolderName" value="Default"/>
    <add key="Clientcenter.UserSecurityClient.IsTestEnvironment" value="true"/>
    <add key="CampaignUserSecretKeyVaultName-TEST" value="CampaignUserSecretKVSI" /> 
    <add key="CampaignUserSecretKeyVaultName-TEST-CORP" value="CampaignUserSecretKVCI" /> 
    <add key="MetadataDBKeyVaultName" value="MetadataDB-KeyVaultSI" />


    <add key="FraudSecretsKeyVaultName-SI" value="AdQualityVaultSI" />
    <add key="FraudKeyVaultSecretCI" value="fraudevaluatedatatestconnectionstring" />
    <add key="FraudKeyVaultSecretSI" value="fraudevaluatedataconnectionstring" />
    <add key="FraudEvaluateAdGroupUpdateEventHubNameCI" value="evaluateadgroupupdates" />
    <add key="FraudEvaluateAdGroupUpdateEventHubNameSI" value="evaluateadgroupupdates" />
    <add key="FraudConsumerGroupCI" value="evaluateadgroupupdates" />
    <add key="FraudConsumerGroupSI" value="evaluateadgroupupdates" />
    <add key="FraudEvaluateCampaignEventHubNameCI" value="evaluatecampaignrequest" />
    <add key="FraudEvaluateCampaignEventHubNameSI" value="evaluatecampaignrequest" />
    <add key="FraudCampaignConsumerGroupCI" value="evaluatecampaignrequest" />
    <add key="FraudCampaignConsumerGroupSI" value="evaluatecampaignrequest" />
    <add key="FraudEvaluateAdsEventHubName" value="evaluateadsrequest" />
    <add key="FraudEvaluateAccountsEventHubName" value="evaluateaccountupdaterequest" />
    <add key="EditorialEventHubConnectionStringKeyVaultSecretCI" value="EditorialTestEventHubConnectionString" />
    <add key="EditorialEventHubConnectionStringKeyVaultSecretSI" value="EditorialEventHubConnectionString" />
    <add key="EditorialTestUseMICI" value="true" />
    <add key="EditorialTestUseMISI" value="true" />
    <add key="EditorialTestEventHubEndpointCI" value="sb://editorialsyncservicetest.servicebus.windows.net" />
    <add key="EditorialTestEventHubEndpointCI2" value="sb://editorialsyncservicetest2.servicebus.windows.net" />
    <add key="EditorialTestEventHubEndpointSI" value="sb://EditorialSyncServiceTestSIpme.servicebus.windows.net" />
    <add key="EditorialEventHubEndpointSI" value="sb://editorialcommonentityhub2.servicebus.windows.net" />
    <add key="EditorialTestServiceBusEndpointCI" value="sb://fraudstatusupdate.servicebus.windows.net" />
    <add key="EditorialTestServiceBusEndpointSI" value="sb://EditorialServiceBusSIPME.servicebus.windows.net" />
    <add key="FraudTestUseMICI" value="true" />
    <add key="FraudTestUseMISI" value="true" />
    <add key="FraudTestEventHubEndpointCI" value="sb://fraudevaluatedatatest.servicebus.windows.net" />
    <add key="FraudTestEventHubEndpointSI" value="sb://fraudevaluatedata.servicebus.windows.net" />
    <add key="FraudTestServiceBusEndpointCI" value="sb://fraudstatusupdate.servicebus.windows.net" />
    <add key="FraudTestServiceBusEndpointSI" value="sb://fraudstatusupdatepme.servicebus.windows.net" />
    <add key="EditorialAppealEventHubNameCI" value="appeal_delta" />
    <add key="EditorialAppealEventHubNameSI" value="appeal_delta" />
    <add key="MMCTestingToken" value="49793341-CF1D-4847-B4E4-2FD9019F3ED1" />
  	<add key="CampaignSecretsKeyVaultName" value="CampaignSecretsKVSI"/>
    <add key="DefaultCustomerPilot" value=""/>
    <add key="AzureResourceCredentialConfigConnectionString" value="ClientId=cf627068-a4da-44b7-9d67-c9fee3ce6653;TenantId=72f988bf-86f1-41af-91ab-2d7cd011db47;ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="AzureResourceCredentialConfigConnectionStringSI" value="ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="AzureResourceCredentialConfigConnectionStringCorp" value="ManagedIdentityId=bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" />
  </appSettings>
  <AdWordsApi>
    <!-- <add key="RetryCount" value="1"/> -->
    
    <!-- <add key="Timeout" value="100000"/> -->    
    <!--
      This section contains the settings specific to AdWords and DoubleClick Ad
      Exchange Buyer API DotNet Client Library. You can use the App.config /
      Web.config for quickly configuring and running a simple application.
      However, it is not mandatory to provide your settings in the config file,
      you may also set or override these settings at runtime. See
      https://github.com/googleads/googleads-dotnet-lib/wiki/Getting-Started
      for details.

      You can refer to
      https://github.com/googleads/googleads-dotnet-lib/wiki/Understanding-App.config
      for detailed explanation of each configuration key.
    --><!-- Settings related to SOAP logging. --><!-- Proxy settings for library. --><!-- Settings specific to AdWords API.--><!-- The name to uniquely identify your application. The client library
         will include this text in the user agent when sending requests to the
         server. This name may be used to uniquely identify your application's
         API requests from the server logs. This field is optional. If
         specified, the value should contain only printable ASCII characters.
         If this field is not set, a default value of "unknown" will be used. --><!-- <add key="UserAgent" value="INSERT_YOUR_COMPANY_OR_APPLICATION_NAME_HERE" /> --><!-- See https://developers.google.com/adwords/api/docs/signingup for
         instructions on signing up for a developer token.--><!-- If your application is a simple script that makes calls to only a
         single Adwords account, then you can set your customer ID here. If you
         have multiple customer IDs to deal with in your account, then you can
         comment out this key and set the value at runtime by setting
         ((AdWordsAppConfig) user.Config).ClientCustomerId = "xxx";
    --><!-- Use the following settings to skip the report header and summary rows
         when downloading a report in CSV, TSV or their gzipped formats. --><!-- Use the following setting to include zero impression rows when
         downloading a report. If this setting is commented out, then the server
         behaves as explained in
         https://developers.google.com/adwords/api/docs/guides/zero-impression-reports#default_behavior.
    --><!-- <add key="IncludeZeroImpressions" value="true" /> --><!-- Use the following setting to return enum values as actual enum values
         instead of display values when downloading a report. If this setting
         is commented out, then the server behaves as explained in
         https://developers.google.com/adwords/api/docs/guides/reporting#request-headers.
    --><!-- <add key="UseRawEnumValues" value="true" /> --><!-- Settings specific to use OAuth2 as authentication mechanism. You could
         run Common\Util\OAuth2TokenGenerator.cs to generate this section of the
         config file.
    --><!-- Provide the OAuth2 client ID and secret. You can create one from
         https://console.developers.google.com. See
         https://github.com/googleads/googleads-dotnet-lib/wiki/Using-OAuth2
         for more details.
    --><!-- The following OAuth2 settings are optional. --><!-- Provide a different OAuth2 scope if required. Multiple scopes should be
         separated by spaces. --><!-- <add key="OAuth2Scope" value="INSERT_OAUTH2_SCOPE_HERE" /> --><!-- Use the following keys if you want to use Web / Installed application
         OAuth flow.--><!-- If you are using a single AdWords manager account's credentials to make
         calls to all your accounts, then you can run OAuth2TokenGenerator.cs to
         generate a RefreshToken for that account and set this key in your
         application's App.config / Web.config. If you are making calls to
         multiple unrelated accounts, then you need to implement OAuth2 flow in
         your account and set this key at runtime. See OAuth folder under
         Examples folder for a web and a console application example.
    --><add key="ProxyServer" value="http://itgproxy"/>
    
    
    
    
    <add key="UserAgent" value="TestClientApp"/>
    
    
    
    
    
    
    
    <!-- If you are using a single MCC account's credentials to make calls to
         all your accounts, then you can run OAuth2TokenGenerator.cs to generate
         a RefreshToken for that account and set this key in your application's
         App.config / Web.config. If you are making calls to multiple unrelated
         accounts, then you need to implement OAuth2 flow in your account and
         set this key at runtime. See OAuth folder under Examples folder for a
         web and a console application example.
    -->
    <!--<add key="MerchantCenterId" value="*********" />-->
    <add key="MerchantCenterId" value="9712329"/>
    <add key="AuthorizationMethod" value="OAuth2"/>
    
    
  <!-- Settings related to general library behaviour. --><!-- Use this key to automatically retry a call that failed due to a
         recoverable error like expired credentials. --><!-- <add key="RetryCount" value="1" /> --><!-- Set the service timeout in milliseconds. --><!-- <add key="Timeout" value="100000" /> --><!-- Use this key to enable or disable gzip compression in SOAP requests.--><!-- Set this flag to true to include the list of client library utilities
         that your code uses in the user agent string.--><add key="MaskCredentials" value="true"/><add key="EnableGzipCompression" value="true"/><add key="IncludeUtilitiesInUserAgent" value="true"/><add key="ProxyServer" value=""/><add key="ProxyUser" value=""/><add key="ProxyPassword" value=""/><add key="ProxyDomain" value=""/><add key="DeveloperToken" value="INSERT_YOUR_DEVELOPER_TOKEN_HERE"/><add key="ClientCustomerId" value="INSERT_YOUR_CLIENT_CUSTOMER_ID_HERE"/><add key="SkipReportHeader" value="false"/><add key="SkipReportSummary" value="false"/><add key="SkipColumnHeader" value="false"/><add key="OAuth2ClientId" value="INSERT_OAUTH2_CLIENT_ID_HERE"/><add key="OAuth2ClientSecret" value="INSERT_OAUTH2_CLIENT_SECRET_HERE"/><add key="OAuth2Mode" value="APPLICATION"/><add key="OAuth2RefreshToken" value="INSERT_OAUTH2_REFRESH_TOKEN_HERE"/><!-- Optional: Specify an OAuth2 redirect url if you are building a
         web application and implementing OAuth2 web flow in your application.
    --><!-- <add key="OAuth2RedirectUri" value="" /> --><!-- Use the following keys if you want to use OAuth2 service account flow.
         You should comment out all the keys for Web / Installed application
         OAuth flow above. See
         https://developers.google.com/adwords/api/docs/guides/service-accounts
         https://github.com/googleads/googleads-dotnet-lib/wiki/Using-OAuth2
         for more details.
    --><!--
    <add key="OAuth2Mode" value="SERVICE_ACCOUNT" />
    <add key="OAuth2PrnEmail" value="INSERT_OAUTH2_USER_EMAIL_HERE" />
    <add key="OAuth2SecretsJsonPath" value="INSERT_OAUTH2_SECRETS_JSON_FILE_PATH_HERE" />
    --></AdWordsApi>
  
  
  <system.diagnostics>
    <sources>
      
      <source name="AdsClientLibs.SoapXmlLogs" switchName="AdsClientLibs.SoapXmlLogs" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <!-- Modify the initializeData attribute below to control the
                path to the SOAP XML log file. -->
          <add name="soapXmlLogListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="C:\Logs\soap_xml.log"/>
          <remove name="Default"/>
        </listeners>
      </source>
      <source name="AdsClientLibs.RequestInfoLogs" switchName="AdsClientLibs.RequestInfoLogs" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <!-- Modify the initializeData attribute below to control the
                path to the request info log file. -->
          <add name="requestInfoLogListener" type="System.Diagnostics.TextWriterTraceListener" initializeData="C:\Logs\request_info.log"/>
          <remove name="Default"/>
        </listeners>
      </source>
    <source name="AdsClientLibs.DeprecationMessages" switchName="AdsClientLibs.DeprecationMessages" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <add name="myListener" type="System.Diagnostics.EventLogTraceListener" initializeData="Application"/>
        </listeners>
      </source><source name="AdsClientLibs.DetailedRequestLogs" switchName="AdsClientLibs.DetailedRequestLogs" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <add name="detailedRequestLogListener" type="System.Diagnostics.ConsoleTraceListener" initializeData="true"/>
          <!-- Use the following to log to file. Modify the initializeData
               attribute to control the path to the detailed request log file.
          <add name="detailedRequestLogListener" type="System.Diagnostics.TextWriterTraceListener"
               initializeData="C:\Logs\AdWords\detailed_logs.log"/>
          -->
          <remove name="Default"/>
        </listeners>
      </source><source name="AdsClientLibs.SummaryRequestLogs" switchName="AdsClientLibs.SummaryRequestLogs" switchType="System.Diagnostics.SourceSwitch">
        <listeners>
          <add name="summaryRequestLogListener" type="System.Diagnostics.ConsoleTraceListener" initializeData="true"/>
          <!-- Use the following to log to file. Modify the initializeData
               attribute to control the path to the summary request log file.
          <add name="summaryRequestLogListener" type="System.Diagnostics.TextWriterTraceListener"
               initializeData="C:\Logs\AdWords\summary_logs.log"/>
          -->
          <remove name="Default"/>
        </listeners>
      </source></sources>
    <switches>
      
      
      <!-- Use this trace switch to control the SOAP XML logs written by Ads*
          .NET libraries. The default level is set to Off. Logs are generated at
          both the Error and Information levels. -->
      <!-- Use this trace switch to control the deprecation trace messages
          written by Ads* .NET libraries. The default is level is set to
          Warning. To disable all messages, set this value to Off. See
          http://msdn.microsoft.com/en-us/library/system.diagnostics.sourcelevels.aspx
          for all possible values this key can take. --><!-- Use this trace switch to control the detailed request logs written by Ads*
          .NET libraries. The default level is set to Off. Logs are generated at
          both the Error and Information levels. --><!-- Use this trace switch to control the summary request logs written by
          Ads* .NET libraries. The default level is set to Off. Logs are
          generated at both the Error and Information levels. --><add name="AdsClientLibs.SoapXmlLogs" value="Off"/>
      <!-- Use this trace switch to control the Request Info logs written by
          Ads* .NET libraries. The default level is set to Off. Logs are
          generated at both the Error and Information levels. -->
      <add name="AdsClientLibs.RequestInfoLogs" value="Off"/>
    <add name="AdsClientLibs.DeprecationMessages" value="Warning"/><add name="AdsClientLibs.DetailedRequestLogs" value="Off"/><add name="AdsClientLibs.SummaryRequestLogs" value="Off"/></switches>
    
  <trace autoflush="true"/></system.diagnostics>
  </configuration>