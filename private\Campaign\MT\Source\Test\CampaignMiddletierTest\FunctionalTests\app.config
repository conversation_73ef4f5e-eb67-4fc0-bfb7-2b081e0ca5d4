﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <section name="AdWordsApi" type="System.Configuration.DictionarySectionHandler"/>
  </configSections>
  
  <!-- CampaignMT -->
  <appSettings>
    <!-- C2CTestLoopRetries * C2CTestLoopSleepMsIntervalMs / 60 equals total potential retry wait timeout in MilliSeconds -->
    <add key="C2CTestLoopRetries" value="300"/>
    <add key="C2CTestLoopSleepMsIntervalMs" value="5000"/>
    <add key="EnableC2CTestsForACS" value="true" />
    <add key="ACSPhoneNumberTableName" value="ACSPhoneNumberPoolTest" />
    <add key="ACSMappingTableName" value="ACSVNMappingTest" />
    <!--<add key="AdCenterEnvironment" value="LocalApps" />-->
    <add key="AdCenterEnvironment" value="##TestEnvironmentName##"/>
    <!--<add key="AdCenterEnvironment" value="appsdevdev" />-->
    <!--<add key="AdCenterEnvironment" value="SI-" />-->
    <add key="RunInstanceId" value="##TestRunInstanceId##"/>
    <add key="CampaignServiceVersion" value="##ServiceBuildNumber##"/>
    <add key="DataCenterId" value="1"/>
    <add key="ValidateDB" value="true"/>
    <!-- IT SHOULD BE SET TO FALSE FOR LOAD TESTS -->
    <add key="UseConfigCustomerAccountUser" value="false"/>
    <add key="FindLogsForFailedTests" value="true"/>
    <add key="PrintLogsInConsole" value="true"/>
    <!-- Use Customers.xml -->
    <add key="UseCustomUserCredential" value="false"/>
    <add key="DefaultCustomerPartition" value=""/>
    <add key="UseShardGroupHistogramForCampaignDb" value="true"/>
    <!-- Logger Configuration -->
    <!-- LogLevel Values: Info, Debug, Warn, Error, Fatal -->
    <add key="LogLevel" value="Info"/>
    <add key="LogToFile" value="false"/>
    <add key="LogFilePath" value="\\machine\share\log.txt"/>
    <!-- todo: Retrieve from BiSync web.config -->
    <!--<add key="ReportingMiddleTierServiceMock" value ="net.tcp://advertisertest2:806/AdvertiserMocks/ReportingMiddleTierMockService.svc" />-->
    <add key="ReportingMiddleTierServiceMock" value="net.tcp://BY2ADCK585:806/ReportingMiddleTierMockService.svc"/>
    <add key="ReportingShare" value="d:\Share"/>
    <!-- uncomment to override url for functional tests - resolves bug when running local FTs against CI box -->
    <!--<add key="ReportingAggregatorUrl" value="http://WIN-O62VPDLH7OK:1900/ReportingAggregatorMockService.svc"/> -->
    <!--SI-->
    <!--<add key="InternalUserNameForYahooMigration" value="5bhtUGGZMKXKQgA7iR"/>
    <add key="InternalUserPasswordForYahooMigration" value="Gl8BFcK6SKzGYEj"/>-->
    <!--pace-->
    <add key="InternalUserNameForYahooMigration" value="BWj8yZxON"/>
    <add key="InternalUserPasswordForYahooMigration" value="bBc9hh1u"/>
    <add key="MMC_API_Server" value="mmcapi.ads-int.microsoft.com" />
    <add key="BackCompatDetectorBasePath" value="D:\BackCompactDetector"/>
    <add key="BackCompatDetectorCICampaignMTAppPath" value="D:\App\CampaignMT\AppRoot"/>
    <add key="BackCompatDetectorCIReportingAPIV13AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V13"/>
    <add key="BackCompatDetectorCIReportingAPIV12AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V12"/>
    <add key="BackCompatDetectorCIReportingAPIV11AppPath" value="D:\App\ReportingAPI\ReportingAPIV12\V11"/>
    <add key="CopyBackCompatDetectorFromBuild" value="false"/>
    <add key="WaitForCCMTCacheUpdateSec" value="15"/>
    <!-- Use for PlateformE2E Test (Decryption Use)-->
    <add key="DecryptionKeyDir" value="."/>
    <add key="ClientcenterBillingBaseaddress" value="https://ClientCenterMT.redmond.corp.microsoft.com:3089/clientcenter/billing"/>
    <add key="EnableUETMigrationTestsForSI" value="true"/>
    <add key="FileDownloadRetryCount" value="10"/>
    <add key="FileDownloadRetryWaitInMilliSeconds" value="1000"/>
    <add key="CampaignSecretsKeyVaultName-TEST" value="CampaignSecretsKVSI"/>
    <add key="CampaignSecretsKeyVaultName-TEST-CORP" value="CampaignSecretsKVCI"/>
    <add key="KeyVaultManageIdentityId-TEST" value="644646e3-7089-4e32-b417-24edfa15f2f9" />
    <add key="CampaignMTManageIdentityAuthId-TEST" value="ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="UseCampaignMTManageIdentityAuthId-TEST" value="true" />
    <add key="AzureBlobStorageAccountName-TEST" value="metricmonitorsi" />
    <add key="CustomerCacheFolderName" value="Default"/>
    <add key="Clientcenter.UserSecurityClient.IsTestEnvironment" value="true"/>
    <add key="CampaignUserSecretKeyVaultName-TEST" value="CampaignUserSecretKVSI" /> 
    <add key="CampaignUserSecretKeyVaultName-TEST-CORP" value="CampaignUserSecretKVCI" /> 
    <add key="MetadataDBKeyVaultName" value="MetadataDB-KeyVaultSI" />

    <add key="FraudSecretsKeyVaultName-SI" value="AdQualityVaultSI" />
    <add key="FraudKeyVaultSecretCI" value="fraudevaluatedatatestconnectionstring" />
    <add key="FraudKeyVaultSecretSI" value="fraudevaluatedataconnectionstring" />
    <add key="FraudEvaluateAdGroupUpdateEventHubNameCI" value="evaluateadgroupupdates" />
    <add key="FraudEvaluateAdGroupUpdateEventHubNameSI" value="evaluateadgroupupdates" />
    <add key="FraudConsumerGroupCI" value="evaluateadgroupupdates" />
    <add key="FraudConsumerGroupSI" value="evaluateadgroupupdates" />
    <add key="FraudEvaluateAssetGroupUpdateEventHubNameCI" value="evaluateassetgroupupdates" />
    <add key="FraudEvaluateAssetGroupUpdateEventHubNameSI" value="evaluateassetgroupupdates" />
    <add key="FraudAssetGroupConsumerGroupCI" value="evaluateassetgroupupdates" />
    <add key="FraudAssetGroupConsumerGroupSI" value="evaluateassetgroupupdates" />
    <add key="FraudEvaluateCampaignEventHubNameCI" value="evaluatecampaignrequest" />
    <add key="FraudEvaluateCampaignEventHubNameSI" value="evaluatecampaignrequest" />
    <add key="FraudCampaignConsumerGroupCI" value="evaluatecampaignrequest" />
    <add key="FraudCampaignConsumerGroupSI" value="evaluatecampaignrequest" />
    <add key="FraudEvaluateAdsEventHubName" value="evaluateadsrequest" />
    <add key="FraudEvaluateAccountsEventHubName" value="evaluateaccountupdaterequest" />
    <add key="EditorialEventHubConnectionStringKeyVaultSecretCI" value="EditorialTestEventHubConnectionString" />
    <add key="EditorialEventHubConnectionStringKeyVaultSecretSI" value="EditorialEventHubConnectionString" />
    <add key="EditorialAppealEventHubNameCI" value="appeal_delta" />
    <add key="EditorialAppealEventHubNameSI" value="appeal_delta" />
    <add key="MMCTestingToken" value="********-CF1D-4847-B4E4-2FD9019F3ED1" />
    <add key="CampaignSecretsKeyVaultName" value="CampaignSecretsKVSI"/>
    <add key="DefaultCustomerPilot" value=""/>
    <add key="EditorialTestServiceBusEndpointCI" value="sb://fraudstatusupdate.servicebus.windows.net" />
    <add key="EditorialTestServiceBusEndpointSI" value="sb://EditorialServiceBusSIPME.servicebus.windows.net" />
    <add key="EditorialTestUseMICI" value="true" />
    <add key="EditorialTestUseMISI" value="true" />
    <add key="EditorialTestEventHubEndpointCI" value="sb://editorialsyncservicetest.servicebus.windows.net" />
    <add key="EditorialTestEventHubEndpointCI2" value="sb://editorialsyncservicetest2.servicebus.windows.net" />
    <add key="EditorialTestEventHubEndpointSI" value="sb://EditorialSyncServiceTestSIpme.servicebus.windows.net" />
    <add key="EditorialEventHubEndpointSI" value="sb://editorialcommonentityhub2.servicebus.windows.net" />
    <add key="MMPEventHubEndpointCI" value="sb://mmp-eventhub-ci.servicebus.windows.net" />
    <add key="MMPEventHubEndpointSI" value="sb://dsp-eventhub-si.servicebus.windows.net" />
    <add key="FraudTestEventHubEndpointCI" value="sb://fraudevaluatedatatest.servicebus.windows.net" />
    <add key="FraudTestEventHubEndpointSI" value="sb://fraudevaluatedata.servicebus.windows.net" />
    <add key="FraudTestUseMICI" value="true" />
    <add key="FraudTestUseMISI" value="true" />
    <add key="FraudTestServiceBusEndpointCI" value="sb://fraudstatusupdate.servicebus.windows.net" />
    <add key="FraudTestServiceBusEndpointSI" value="sb://fraudstatusupdatepme.servicebus.windows.net" />
    <add key="AzureResourceCredentialConfigConnectionString" value="ClientId=cf627068-a4da-44b7-9d67-c9fee3ce6653;TenantId=72f988bf-86f1-41af-91ab-2d7cd011db47;ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="AzureResourceCredentialConfigConnectionStringSI" value="ManagedIdentityId=b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="AzureResourceCredentialConfigConnectionStringCorp" value="ManagedIdentityId=bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" />

    <!-- Universal Config -->
    <!--BatadminCorp ManagedIdentity-->
    <add key="CampaignMTManageIdentityAuthId-CI" value="bf7d8c98-83a4-40a9-8afe-6c2734a78e6f" />
    <!--Batadmin ManagedIdentity-->
    <add key="CampaignMTManageIdentityAuthId-TEST" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    <add key="CampaignMTManageIdentityAuthId-PROD" value="b3bcf3b4-7b61-4ad0-b18a-c938c1caaed4" />
    
    <add key="UseCampaignMTManageIdentityAuthId-CI" value="true" />
    <add key="UseCampaignMTManageIdentityAuthId-TEST" value="true" />
    <add key="UseCampaignMTManageIdentityAuthId-PROD" value="true" />
    
    <add key="AzureBlobStorageAccountName-CI" value="universalconfigci" />
    <add key="AzureBlobStorageAccountName-TEST" value="metricmonitorsi" />
    <add key="AzureBlobStorageAccountName-TEST-Local" value="universalconfigsilocal" />
    <add key="AzureBlobStorageAccountName-PROD" value="metricmonitorprod" />
    <add key="AzureBlobStorageAccountName-PROD-Local" value="universalconfigprodlocal" />
    
    <!--If local auth is being used and Env is SI/Prod, will have to use a Corp service bus to prevent cross-tenant access, which is not allowed. I.e. adc-appdev can't have roles for PME resources-->
    <add key="AzureServiceBusNamespace-CI" value="campaignmtci.servicebus.windows.net" />
    <add key="AzureServiceBusNamespace-TEST" value="campaignmttest.servicebus.windows.net" />
    <add key="AzureServiceBusNamespace-TEST-Local" value="campaignmttestlocal.servicebus.windows.net" />
    <add key="AzureServiceBusNamespace-PROD" value="campaignmtprod.servicebus.windows.net" />
    <add key="AzureServiceBusNamespace-PROD-Local" value="campaignmtprodlocal.servicebus.windows.net" />
  </appSettings>
</configuration>
