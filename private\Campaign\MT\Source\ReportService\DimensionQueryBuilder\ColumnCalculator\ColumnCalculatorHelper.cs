namespace Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.ColumnCalculator
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Runtime.CompilerServices;
    using global::ReportService.Interface.Common;
    using global::ReportService.Interface.Localization;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.Reporting;
    using Microsoft.AdCenter.Advertiser.CampaignManagement.MT.ReportService.DataContract;
    using Microsoft.AdCenter.Shared.MT;

    public class ColumnCalculatorHelper
    {
        readonly ColumnCalculator[] columnCalculators;
        readonly int[] ordinals;
        private bool hasGoalDependency;

        public ColumnCalculatorHelper(
            ILogShared logger,
            List<ReportColumnId> selectedColumns,
            ReportInfo reportInfo,
            DimensionRowDataAdapter dataAdapter,
            DownloadRequestType downloadRequestType,
            ILocalizationManager localizationManager,
            CultureInfo cultureInfo,
            bool skipDimensionColumnCompute = false,
            bool hasGoalDependency = false)
        {
            this.columnCalculators = new ColumnCalculator[selectedColumns.Count];
            this.ordinals = new int[selectedColumns.Count];
            this.hasGoalDependency = hasGoalDependency;

            if (dataAdapter.FieldCount > 0)
            {
                for (int i = 0; i < selectedColumns.Count; i++)
                {
                    var columnMeta = reportInfo.Column(selectedColumns[i]);
                    if (reportInfo.IsDeprecated(selectedColumns[i]))
                    {
                        this.columnCalculators[i] = new NullConstant(logger, dataAdapter, columnMeta);
                        continue;
                    }

                    // Don't assign to columnCalculator if its dimension column and skipDimensionColumnCompute is set. That means dimension columns are already computed.
                    if (skipDimensionColumnCompute && !columnMeta.IsBIColumn)
                    {
                        var dbColumnName = DimensionRowDataAdapter.GetDbColumnName(logger, columnMeta);
                        ordinals[i] = dataAdapter.Ordinal(dbColumnName);
                        continue;
                    }

                    if (columnMeta.IsColumnCalculationRequired || columnMeta.IsGoalDependent)
                    {
                        // If the column has dependent columns, it'll look up its dependent column oridinal by DBColumnName in dataAdapter (columnIdMap), and
                        // the dependent columns will be used in the calculate function. So there is an assumption that the dependent columns should also exist
                        // in dataAdaptor (columnIdMap), otherwise, it'll fail.  
                        this.columnCalculators[i] = GetColumnCalculator(logger, columnMeta, dataAdapter, localizationManager, downloadRequestType, cultureInfo);
                    }
                    else
                    {
                        ordinals[i] = dataAdapter.Ordinal(columnMeta.DbColumnName);
                    }
                }
            }
        }

        public ColumnCalculatorHelper(
            ILogShared logger,
            ReportColumnId selectedColumns,
            ReportInfo reportInfo,
            DimensionRowDataAdapter dataAdapter,
            DownloadRequestType downloadRequestType,
            ILocalizationManager localizationManager,
            CultureInfo cultureInfo,
            bool skipDimensionColumnCompute = false)
            : this(logger, new List<ReportColumnId> { selectedColumns }, reportInfo, dataAdapter, downloadRequestType, localizationManager, cultureInfo, skipDimensionColumnCompute)
        {
        }

        public ColumnCalculator GetColumnCalculator(
            ILogShared logger,
            ReportColumn column,
            DimensionRowDataAdapter dataAdapter,
            ILocalizationManager localizationManager,
            DownloadRequestType downloadRequestType,
            CultureInfo cultureInfo)
        {
            if (column.IsColumnCalculationRequired)
            {
                if (!column.CalculationFunction.TryGetValue(downloadRequestType, out var calculationFunction))
                {
                    // If we want ReportUIDisplay and that is not present, check if caclulation Function for ReportUI is present and use that
                    if (downloadRequestType == DownloadRequestType.ReportUIDisplay)
                    {
                        if (!column.CalculationFunction.TryGetValue(DownloadRequestType.ReportUI, out calculationFunction))
                        {
                            calculationFunction = column.CalculationFunction[DownloadRequestType.Default];
                        }
                    }
                    else
                    {
                        calculationFunction = column.CalculationFunction[DownloadRequestType.Default];
                    }
                }

                switch (calculationFunction.FunctionType)
                {
                    case ColumnCalculationFunctionType.Divide:
                        return new Divide(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.DivideMultiply:
                        return new DivideMultiply(logger, dataAdapter, column, (DivideMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.AddDivideMultiply:
                        return new AddDivideMultiply(logger, dataAdapter, column, (DivideMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.DivideMultiplyWithThreshold:
                        return new DivideMultiplyWithThreshold(logger, dataAdapter, column, (DivideMultiplyWithThresholdParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.DateTimeAddHour:
                        return new DateTimeAddHour(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.ChangeHistoryDimension:
                        return new ChangeHistoryDimension(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.ChangeHistoryReport:
                        return new ChangeHistoryReport(logger, dataAdapter, column, (AttributeMappingParameters)calculationFunction.Parameters, localizationManager, cultureInfo);
                    case ColumnCalculationFunctionType.Add:
                        return new Add(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.BigIntSubtract:
                        return new BigIntSubtract(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.LeftOperandPercent:
                        return new LeftOperandPercent(logger, dataAdapter, column, (DivideMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.ConditionedDivideMultiplyWithThreshold:
                        return new ConditionedDivideMultiplyWithThreshold(logger, dataAdapter, column, (DivideMultiplyWithThresholdParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.ConditionedSumDivideMultiplyWithThreshold:
                        return new ConditionedSumDivideMultiplyWithThreshold(logger, dataAdapter, column, (DivideMultiplyWithThresholdParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.ChangeHistoryMadeBy:
                        return new ChangeHistoryMadeBy(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.ChangeHistoryTool:
                        return new ChangeHistoryTool(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.CompoundDivideMultiply:
                        return new CompoundDivideMultiply(logger, dataAdapter, column, (CompoundDivideMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.AccountEntity:
                        return new AccountEntityCalculator(logger, dataAdapter, column, dataAdapter.ColumnCalculatorData.GetAccountReportingInfo);
                    case ColumnCalculationFunctionType.CampaignEntity:
                        return new CampaignEntityCalculator(logger, dataAdapter, column, dataAdapter.ColumnCalculatorData.GetCampaignReportingInfo);
                    case ColumnCalculationFunctionType.DateCalculator:
                        return new DateCalculator(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.LabelName:
                        return new LabelName(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.VarianceMultiply:
                        return new VarianceMultiply(logger, dataAdapter, column, (DivideMultiplyParameters)calculationFunction.Parameters); ;
                    case ColumnCalculationFunctionType.CompoundVarianceMultiply:
                        return new CompoundVarianceMultiply(logger, dataAdapter, column, (CompoundVarianceMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.CompoundRateVarianceMultiply:
                        return new CompoundRateVarianceMultiply(logger, dataAdapter, column, (CompoundRateVarianceMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.ReturnOnAdSpendCPS:
                        return new ReturnOnAdSpendCPS(logger, dataAdapter, column, (ReturnOnAdSpendCPSMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.AdStrengthParser:
                        return new AdStrengthParser(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.AdStrengthActionItemsParser:
                        return new AdStrengthActionItemsParser(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.CombinationAssetParser:
                        return new CombinationAssetParser(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.RevenueCPSMultiply:
                        return new RevenueCPSMultiply(logger, dataAdapter, column, (RevenueCPSMultiplyParameters)calculationFunction.Parameters);
                    case ColumnCalculationFunctionType.ConversionDelayCalculate:
                        return new ConversionDelayCalculate(logger, dataAdapter, column);
                    case ColumnCalculationFunctionType.DummyCalculator:
                        return new DummyCalculator(logger, dataAdapter, column);
                }
            }
            else if (column.IsGoalDependent)
            {
                return new ConditionedOnGoal(logger, dataAdapter, column, this.hasGoalDependency);
            }

            return null;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public object CalculateColumn(IList<object> row, int index)
        {
            if (this.columnCalculators[index] != null)
            {
                return this.columnCalculators[index].Calculate(row);
            }
            else
            {
                return row[ordinals[index]];
            }
        }

        public object CalculateColumn(IList<object> row)
        {
            return this.columnCalculators[0].Calculate(row);
        }
    }
}
