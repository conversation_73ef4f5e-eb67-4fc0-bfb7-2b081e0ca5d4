﻿<?xml version="1.0" encoding="utf-8"?>
<Environments>
  <Environment Name="LocalApps">
    <Config key="MetadataService.Service.ServiceUri" value="http://localhost:10878" />
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://localhost:10094/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://localhost:10094/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://localhost:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://localhost:10094/Aggregator/v6" />
    <Config key="CampaignManagement.ImportService2.ServiceUri" value="http://localhost:10094/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://localhost:10094/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://localhost:10094/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://{localhost}:5597/,http://br2pubdevgen03:5597/" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://localhost:10094/AppExtensionsMetadataService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://localhost:10870/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://{localhost}.redmond.corp.microsoft.com:4080/clientcenter/billing/mt2" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="." />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_MetadataDB" />
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://localhost:10870/clientcenter/mt/cryptography" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="." />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="ClientCenter.CentralStoreDB.ServerName" value="{localhost}" />
    <Config key="ClientCenter.CentralStoreDB.DBName" value="CCDB_CentralStore" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="***********" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="Dimensions20" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAANYpb6aCq39S2LSOkY4D8ve2CKQtkePkjWRK4oBjWnEX" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAADgRFMp3mqg2M3g4yJDwIPVTWVPJxahQ4wKqyG5sOI7x" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\{localhost}\d$\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="{localhost}" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://ccpapptsthyp823:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.APIV13.ServiceUri" value="http://localhost:10094/CampaignManagementService13.svc" />
    <Config key="CampaignManagement.APIV13BulkService.ServiceUri" value="http://localhost:10094/BulkService13.svc" />
    <Config key="CampaignManagement.APIV13FileUploadService.ServiceUri" value="http://localhost:10094/FileUploadService13.svc" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="{localhost}" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://localhost:10877/Advertiser" />
    <Config key="CampaignManagement.PartnerODataApi.ServiceUri" value="http://localhost:10877/Partner" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://localhost:10877/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://localhost:10877/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://localhost:10877/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="http://localhost:11000" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://localhost:10999/McaOfflineService" />
    <Config key="CampaignManagement.MockGserver.ServiceUri" value="http://localhost:10875/gserver" />
    <Config key="CampaignManagement.MockMmc.ServiceUri" value="http://localhost:10875/bmc.mock/BingAds/" />
     <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://***********:5598" />
    <Config key="CampaignManagement.AccountReparentingService.ServiceUri" value="http://localhost:927" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://localhost:3877/ExecutionManagement" />
    <Config key="CampaignManagement.AdInsightTestService.ServiceUri" value="http://{localhost}:32823/api" />
    <Config key="CampaignManagement.AdInsightODataService.ServiceUri" value="http://localhost:32897/sds/ObjectStoreQuery/V1" />
    <Config key="Aggregation.AggregationService.ServiceUri" value="http://localhost:1901/aggsvc.mock/" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="pxhOmLUF8fZ4Dd4FjQA/vKOBd5aUGDNaykJfZhoaxWgR" />
    <Config key="TestFramework.Params.EncryptedKey" value="mSDRl0+JYANIi/SfQMTlP4o/haG57MxxCJbEy7hM4iSCx1tAy0wT48BNEyoYYQTc4OmDiu8RplhgXvp2KE5WByr3AXss2kYwSU9nZrfju+ZZ1VzYe6M9n3HBDXoCZzMrJAj0znmRMwFC+b3PC8Q62aymM3vPL5OkQgpu2/q3IPaBB2BQMnkC3DXdzpEqw+BysFGJihDIGUCr/chlMvk+hN3DqYk2zGmMtqHBe/FfJkM/tI12qorWlu/hqhzOQD+PBQECaX+q/LzjzjVSSufeTBpzlSCQRyv+8lClPIQkYaXrtZt7B2OUGO4mpDBkGKHooKvZE2/vu8E5wEgR4FXQbQ==" />
    <Config key="TestFramework.Params.EncryptedIV" value="o9D7ssf3F7fxkuLlvuPP9y5Zuh4KoYkuqsUI6r0gVUby3XqJT30M5wBmPb2a3OluT7DCt+5SPcIVPs2edb4INp05SuF46PHSvSQw2DXHlqU6pXHlm8W8xX/VlwLV8XtYtBIpNjD6Fte6PQaADCyiiiB6liaLrxtge9E8S1R0BzI5PkONsPBA6OK0qE6deCEI9LLz9V7MB29tiUWbbQhVRJlBrmykjJQk7dy5Nz9G+RJO29K0Q1B6MhEpuj4eJ45pdJxJyma9bBIyu8PoPx8Nti8Wqxl3aK9S8bUL/+mPw/5JmDeg+GofBtkmXApDnfeZTGZA8TjfgTuTHgzAlyj1Zw==" />
  </Environment>
  <Environment Name="Production-Shadow"></Environment>
  <Environment Name="appsdevdev">
    <Config key="MetadataService.Service.ServiceUri" value="http://appsdevdev:818" />
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6//CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6//EventTrackingContract.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\appsdevdev\d$\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://appsdevdev:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://appsdevdev:10094/Aggregator/v6" />
    <Config key="CampaignManagement.ImportService2.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://appsdevdev:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://appsdevdev:5597/,http://br2pubdevgen03:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://appsdevdev.redmond.corp.microsoft.com:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://appsdevdev.redmond.corp.microsoft.com:4080/clientcenter/billing/mt2" />
    <Config key="ClientCenter.MetadataDB.DBTypeName" value="MetadataDB" />
    <Config key="ClientCenter.MetadataDB.KeyVaultName" value="CampaignSecretsKVCI" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="appsdevdev" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAANYpb6aCq39S2LSOkY4D8ve2CKQtkePkjWRK4oBjWnEX" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAADgRFMp3mqg2M3g4yJDwIPVTWVPJxahQ4wKqyG5sOI7x" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="appsdevdev" />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="appsdevdev" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="Dimensions20" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="appsdevdev" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CampaignManagement.APIV13.ServiceUri" value="http://appsdevdev.redmond.corp.microsoft.com:8080/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" />
    <Config key="CampaignManagement.APIV13BulkService.ServiceUri" value="http://appsdevdev:8080/Api/Advertiser/CampaignManagement/V13/BulkService.svc" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://appsdevdev:3877/ExecutionManagement" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="appsdevdev" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://appsdevdev:5598" />
    <Config key="CampaignManagement.AccountReparentingService.ServiceUri" value="http://appsdevdev:927" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://appsdevdev:18080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.PartnerODataApi.ServiceUri" value="http://appsdevdev:18080/ODataApi/Partner" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://appsdevdev:18080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://appsdevdev:18080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://appsdevdev:18080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="http://appsdevdev" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://appsdevdev:10999/McaOfflineService" />
    <Config key="CampaignManagement.AdInsightTestService.ServiceUri" value="http://appsdevdev:32823/api" />
    <Config key="CampaignManagement.AdInsightODataService.ServiceUri" value="http://appsdevdev:32897/sds/ObjectStoreQuery/V1" />
    <!--<Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://localhost/Travel" />-->
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="pxhOmLUF8fZ4Dd4FjQA/vKOBd5aUGDNaykJfZhoaxWgR" />
    <Config key="TestFramework.Params.EncryptedKey" value="C+ys2VKYURBhWuugb1tQAFzloPTZZMUl7Lg466UEH1zlqlLJntZPfXSHeOo7KHMAgCa04rt3eGaGuKNR2r2MtduuiknrtvwKm7c9NPeJkTvzuw58v0KzdfY5nu8bYgDt0rjgDCV4HkruuqTOJTRnqEcpCp5hR8gjGFtU74MJw8I=" />
    <Config key="TestFramework.Params.EncryptedIV" value="WzdkItA7ZfmBrM81urPsiEHGBiZ/U5Pq61FjmzihR6zY7wcOlbSDvRy+9Ha+bsaGxLbvg68mKIofPF9SmJt5/BxGlgDEBxvaIbH9FL/ZLRorBV7guc7qq8MxxpbztSdTpce9nwClG0n+JjxPFBn2xR8vEsTa0hxw39CmwIWqT30=" />
  </Environment>
  <Environment Name="rahul">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://localhost:777/CampaignService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://localhost:777/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://localhost:777/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://localhost:777/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://localhost:777/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://localhost:777/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://localhost:777/AdIntelligenceService.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://localhost:777/CampaignSyncService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://advertisertest2:5597/,http://br2pubdevgen03:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://WIN-IEVC2SNA3C7:3080/clientcenter/mt" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="..\..\..\..\..\MsfService\ServiceHost" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="WIN-IEVC2SNA3C7" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="WIN-IEVC2SNA3C7" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="WIN-IEVC2SNA3C7" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="WIN-IEVC2SNA3C7" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://sadaptsthyp105:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="localhost:8969" />
  </Environment>
  <Environment Name="localgopalbl1">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://localhost/CampaignMT/CampaignService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\CampaignServices\CampaignMT\Jan" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://apps-devdev:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://apps-devdev:10094/Aggregator/v6" />
    <Config key="CampaignManagement.ImportService2.ServiceUri" value="http://apps-devdev:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://apps-devdev:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://apps-devdev:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="???" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://apps-devdev:3080/clientcenter/mt" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="apps-devdev" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="apps-devdev" />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://sadaptsthyp105:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://apps-devdev:5598" />
  </Environment>
  <Environment Name="mtreit-local">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://localhost:10874/CampaignService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="C:\AdsGroup\Advertiser\CampaignManagementMT\Main\MsfService\ServiceHost" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://localhost:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://localhost:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://localhost:10874/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://localhost:10874/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://localhost:10874/EditorialCallbackService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://apps-devdev:3080/clientcenter/mt" />
    <!-- Check my permissions - can set to some other folder -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="apps-devdev" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="apps-devdev" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="apps-devdev" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://sadaptsthyp105:8081/CamServicev8/CamService.svc" />
  </Environment>
  <Environment Name="DevDevAbghosh">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://advertisertest2:10094/CampaignMT/abghosh/CampaignService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\CampaignServices\CampaignMT\V9.4" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://advertisertest2:10094/CampaignMT/abghosh/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://advertisertest2:10094/Aggregator/v6" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://advertisertest2:10094/CampaignMT/abghosh/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://advertisertest2:10094/CampaignMT/abghosh/EditorialCallbackService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://SAAPPDEVHYP040:3080/clientcenter/mt" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="SAAPPDEVHYP031" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="SAAPPDEVHYP031" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="SAAPPDEVHYP031" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
  </Environment>
  <Environment Name="SI-">
    <Config key="MetadataService.Service.ServiceUri" value="http://metadataservice-si-adsapps.trafficmanager.net:818" />
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://siaggsvcazure.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://siaggsvcazure.trafficmanager.net:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="CampaignManagement.APIV13.ServiceUri" value="https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc" />
    <Config key="CampaignManagement.APIV13BulkService.ServiceUri" value="https://c2_campaignapi_azure.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://sitaskengine.trafficmanager.net:3877/ExecutionManagement" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/mt/cryptography" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.DBTypeName" value="MetadataDB" />
    <Config key="ClientCenter.MetadataDB.KeyVaultName" value="MetadataDB-KeyVaultSI" />
    <Config key="ClientCenter.MetadataDB.DisableLocalAccess" value="true" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <Config key="CampaignDBKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignStaticKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignAdGroupShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignNegativeKeywordShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignSharedLibraryShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="MultiChannelAdsKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="MultiChannelAdsSystemDBKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="VerticalDBKeyVaultName" value="VerticalDB-KeyVaultSI" />
    <Config key="VerticalDBReadOnlyKeyVaultName" value="VerticalDB-KeyVaultSI" />
    <Config key="CampaignEntityLibraryShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="AdvBiAzureAccountKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBiAzureAdgroupKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvertiserBIBSCKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBiCHAccountKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBiCHAdgroupKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBIBSCShardedKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="MerchantCenterOfferDBKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AzureAuditHistoryKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AzureChangeHistoryUIKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="BIElasticQueryKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="CampaignBulkDBv2KeyVaultName" value="SAKeyvaultSI" />
    <Config key="AppsConsolidatedInAzureKeyVaultName" value="AnBDBKeyVaultSI" />
    <Config key="BillingDBPartitionedKeyVaultName" value="AnBDBKeyVaultSI" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.PartnerODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Partner" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://mcacallback.sandbox.ads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://cmmt-mcaofflineservice-si-mttest.trafficmanager.net/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="M4h4QZgu6RCpIdRHxpdx8qIOcASbjcBpPosJumQoUmp8HJAEjW4Laha6Bnx1Fv2WdR09P8smFwbN29MSqKFxhJXpBmDh5YvEGdXSBPNV9euSLYpW7WHhzDJZXPgtuiy6CvlktVjZzZ8UJeZ+5hoPtvRxPzJ7aXjP0s5o++FY0OuwjQP2tshF6GZJqdiNimQ2cYbugg7Rh31LT1tlcuhhTPp6ozhpLOXxQFwZ1p8X87hdN6/30pYWLnvqm8LG1zOMJ9DbSmw2bnj2U6V3AZbUMZclvUAqyVvX6nDp6gkeL4GiWC0OJzfOIzaAayuljVL1/wkCNLhUTZtlDqgbJ9inUQ==" />
    <Config key="TestFramework.Params.EncryptedIV" value="Ghkm4KK/acGG7vhTR03OGMTtB+vy6sP6lT8GZEgZXV7jyglKrGQCMvvnruR+VbN4hsXLZ5/dWwCTXULgcA16nbRs3NRyV7tAAjgHH2KssPTl7sen1XCXC8qcnY/WqSGaDngxlU+3BgW5SpblE871TP30PAU6Hoa4YlAE8s/oLbJV0pAOlkTmBP4V/UAh68PXBSWEC3hqHxIyEGFM+njSkmX8xTk1RE7pN5Af5lumvarNlRjceZY0tdsVPcxZ73iNtVlu/BGAzWEB/4caWDRVCJzqSmUIoO1YM8HrDpW0csNtHdGfP+WoFY4mCSN81IJhzVFQXbsGrmLfs6YZ0pm+Ew==" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="FCDD3/VxA+5i9YVgR4QyymugNGXOl2QtMp4d3rNukITE" />
  </Environment>
  <Environment Name="SI-AKS">
    <Config key="MetadataService.Service.ServiceUri" value="http://metadataservice-si-adsapps.trafficmanager.net:818" />
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://cmmt-aggregatorservice-si-ldc2.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://cmmt-aggregatorservice-si-ldc2.trafficmanager.net:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://cmmt-campaignmt-si-ldc2.trafficmanager.net:801/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="CampaignManagement.APIV13.ServiceUri" value="https://c2_campaignapi_aks.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/CampaignManagementService.svc" />
    <Config key="CampaignManagement.APIV13BulkService.ServiceUri" value="https://c2_campaignapi_aks.si.bingads.microsoft.com/Api/Advertiser/CampaignManagement/v13/BulkService.svc" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://sitaskengine.trafficmanager.net:3877/ExecutionManagement" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/mt/cryptography" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.DBTypeName" value="MetadataDB" />
    <Config key="ClientCenter.MetadataDB.KeyVaultName" value="MetadataDB-KeyVaultSI" />
    <Config key="ClientCenter.MetadataDB.DisableLocalAccess" value="true" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <Config key="CampaignDBKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignStaticKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignAdGroupShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignNegativeKeywordShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="CampaignSharedLibraryShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="MultiChannelAdsKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="MultiChannelAdsSystemDBKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="VerticalDBKeyVaultName" value="VerticalDB-KeyVaultSI" />
    <Config key="VerticalDBReadOnlyKeyVaultName" value="VerticalDB-KeyVaultSI" />
    <Config key="CampaignEntityLibraryShardKeyVaultName" value="CampaignDB-KeyVaultSI" />
    <Config key="AdvBiAzureAccountKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBiAzureAdgroupKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvertiserBIBSCKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AdvBIBSCShardedKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="MerchantCenterOfferDBKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AzureAuditHistoryKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="AzureChangeHistoryUIKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="BIElasticQueryKeyVaultName" value="BIDataMartsKVMTSI" />
    <Config key="CampaignBulkDBv2KeyVaultName" value="SAKeyvaultSI" />
    <Config key="AppsConsolidatedInAzureKeyVaultName" value="AnBDBKeyVaultSI" />
    <Config key="BillingDBPartitionedKeyVaultName" value="AnBDBKeyVaultSI" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://c2-si-odataapi-aks.trafficmanager.net:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.PartnerODataApi.ServiceUri" value="http://c2-si-odataapi-aks.trafficmanager.net:8080/ODataApi/Partner" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://c2-si-odataapi-aks.trafficmanager.net:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://c2-si-odataapi-aks.trafficmanager.net:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://c2-si-odataapi-aks.trafficmanager.net:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://mcacallback.sandbox.ads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://cmmt-mcaofflineservice-si-mttest.trafficmanager.net/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="M4h4QZgu6RCpIdRHxpdx8qIOcASbjcBpPosJumQoUmp8HJAEjW4Laha6Bnx1Fv2WdR09P8smFwbN29MSqKFxhJXpBmDh5YvEGdXSBPNV9euSLYpW7WHhzDJZXPgtuiy6CvlktVjZzZ8UJeZ+5hoPtvRxPzJ7aXjP0s5o++FY0OuwjQP2tshF6GZJqdiNimQ2cYbugg7Rh31LT1tlcuhhTPp6ozhpLOXxQFwZ1p8X87hdN6/30pYWLnvqm8LG1zOMJ9DbSmw2bnj2U6V3AZbUMZclvUAqyVvX6nDp6gkeL4GiWC0OJzfOIzaAayuljVL1/wkCNLhUTZtlDqgbJ9inUQ==" />
    <Config key="TestFramework.Params.EncryptedIV" value="Ghkm4KK/acGG7vhTR03OGMTtB+vy6sP6lT8GZEgZXV7jyglKrGQCMvvnruR+VbN4hsXLZ5/dWwCTXULgcA16nbRs3NRyV7tAAjgHH2KssPTl7sen1XCXC8qcnY/WqSGaDngxlU+3BgW5SpblE871TP30PAU6Hoa4YlAE8s/oLbJV0pAOlkTmBP4V/UAh68PXBSWEC3hqHxIyEGFM+njSkmX8xTk1RE7pN5Af5lumvarNlRjceZY0tdsVPcxZ73iNtVlu/BGAzWEB/4caWDRVCJzqSmUIoO1YM8HrDpW0csNtHdGfP+WoFY4mCSN81IJhzVFQXbsGrmLfs6YZ0pm+Ew==" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="FCDD3/VxA+5i9YVgR4QyymugNGXOl2QtMp4d3rNukITE" />
  </Environment>
  <Environment Name="Sandbox"></Environment>
  <Environment Name="SI-LDC1">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2acsicmpmt01:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2acsicmpmt01:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://c1_siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://by2acsiadapi01/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://by2acsiadapi01" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://by2acsiadapi01:10999/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="XGBcMXCN6iSfbrbXDW//vbv5qz+KBOjrghSWh+hiesoio6aVJy6mKEQ7GidOnWFBOB65kMFuc5CN8YZuNNBd7RZTRFPuDRgC2tDUkfTQVddHED+P+u4T+Cp90cu3nT6gUK/TLNsMjjphOWax25/KI2Bev7w2Eg5tDP8uR5Ry36hbysZhJOCMyYENqDyeMC7OEec3NWOdwSLz7ZBoiaOy9YAbIoy/FPCSZ8N/IZpmPgi5ugXC7IQpUCIjSUvrKZfXrKdbfwogJMeiAjs/GpspSo/IyAgWbWoqY4AS349JamiAohpYfmzBGUqgTBCYyDYYTAchHJphC/4g/HrFKFUHXA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" />
  </Environment>
  <Environment Name="SI-LDC1-VIP">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://c1-sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/EditorialCallbackService.svc" />
    <!--<Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02262013_2132__9_8_02253_14" />-->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://c1_siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://c1_api.si.bingads.microsoft.com/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://c1_api.si.bingads.microsoft.com/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://c1_api.si.bingads.microsoft.com/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://c1_api.si.bingads.microsoft.com/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://mcacallback.sandbox.ads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://cmmt-mcaofflineservice-si-ldc1.trafficmanager.net/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="XGBcMXCN6iSfbrbXDW//vbv5qz+KBOjrghSWh+hiesoio6aVJy6mKEQ7GidOnWFBOB65kMFuc5CN8YZuNNBd7RZTRFPuDRgC2tDUkfTQVddHED+P+u4T+Cp90cu3nT6gUK/TLNsMjjphOWax25/KI2Bev7w2Eg5tDP8uR5Ry36hbysZhJOCMyYENqDyeMC7OEec3NWOdwSLz7ZBoiaOy9YAbIoy/FPCSZ8N/IZpmPgi5ugXC7IQpUCIjSUvrKZfXrKdbfwogJMeiAjs/GpspSo/IyAgWbWoqY4AS349JamiAohpYfmzBGUqgTBCYyDYYTAchHJphC/4g/HrFKFUHXA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" />
  </Environment>
  <Environment Name="SI-LDC2-VIP">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://siaggsvcazure.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://siaggsvcazure.trafficmanager.net:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://sicampaignmtazure.trafficmanager.net:801/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://c2_siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://si-odataapi.trafficmanager.net:8080/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://mcacallback.sandbox.ads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://cmmt-mcaofflineservice-si-mttest.trafficmanager.net/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="XGBcMXCN6iSfbrbXDW//vbv5qz+KBOjrghSWh+hiesoio6aVJy6mKEQ7GidOnWFBOB65kMFuc5CN8YZuNNBd7RZTRFPuDRgC2tDUkfTQVddHED+P+u4T+Cp90cu3nT6gUK/TLNsMjjphOWax25/KI2Bev7w2Eg5tDP8uR5Ry36hbysZhJOCMyYENqDyeMC7OEec3NWOdwSLz7ZBoiaOy9YAbIoy/FPCSZ8N/IZpmPgi5ugXC7IQpUCIjSUvrKZfXrKdbfwogJMeiAjs/GpspSo/IyAgWbWoqY4AS349JamiAohpYfmzBGUqgTBCYyDYYTAchHJphC/4g/HrFKFUHXA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" />
  </Environment>
  <Environment Name="SI-LDC2">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2acsicmpmt02:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://by2acsicmpmt02:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2acsicmpmt02:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2acsicmpmt02:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2acsicmpmt02:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2acsicmpmt02:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt02\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://by2acsicmpmt02:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://c2_siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="https://siccmt.trafficmanager.net:3089/clientcenter/billing" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ASIDIMSQL1" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimension" />
    <config key="LiveConnect.UI.Uri" value="https://login.live-int.com" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://globalsippsmt.trafficmanager.net:5598" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://by2acsiadapi02/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://by2acsiadapi02/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://by2acsiadapi02/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://by2acsiadapi02/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://mcacallback.sandbox.ads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://cmmt-mcaofflineservice-si-mttest.trafficmanager.net/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="XGBcMXCN6iSfbrbXDW//vbv5qz+KBOjrghSWh+hiesoio6aVJy6mKEQ7GidOnWFBOB65kMFuc5CN8YZuNNBd7RZTRFPuDRgC2tDUkfTQVddHED+P+u4T+Cp90cu3nT6gUK/TLNsMjjphOWax25/KI2Bev7w2Eg5tDP8uR5Ry36hbysZhJOCMyYENqDyeMC7OEec3NWOdwSLz7ZBoiaOy9YAbIoy/FPCSZ8N/IZpmPgi5ugXC7IQpUCIjSUvrKZfXrKdbfwogJMeiAjs/GpspSo/IyAgWbWoqY4AS349JamiAohpYfmzBGUqgTBCYyDYYTAchHJphC/4g/HrFKFUHXA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" />
  </Environment>
  <Environment Name="pace3_LDC1">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://BY2ADCK580:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://BY2ADCK580:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://BY2ADCK580:10094/CampaignMT/v6/AdIntelligenceService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://BY2ADCK577:3080/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://BY2ADCVM332:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2ADCL032" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2ADCL029" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://by2adck578:8081/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ADCL024" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://BY2ADCK580:5598" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://BY2ADCK580:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://BY2ADCK580/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://BY2ADCK580/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://BY2ADCK580/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://BY2ADCK580" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://BY2ADCK580:10999/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="UxOL+lH82VbpfGoKNNPVnFfBxvMYZOqgXp4RCmg398slJq1nS5NcAbeb6EMPq4oYs/9TNfIWXL6cmjv0fh6JPEX0M/uGUaaApvT5Ssf42dGr1IROXZTf74U3fXrSLBSvAbDI2GMXrAtEkecPVp9s13V9+sHbAGEkxdYbb3Bjf53a/JRhMwHDr096moInVne1UBNoGDau/KXdK5Zz+nZkjwtzkfBcQ84wqkwZScjkntnf2N2PzDDd6M19l7cTk6MTZbvsZ2VtRH76tGIXbgkzl9G2HRWQlS2Dnb6cVg/OnMWv0zBkToueWpuwzaTxPT5RycqR4Dg45nY1gWUmh6J/tA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="YOdQ/OLp2ZsKQJMW9mvrCU61o4d47xXuY43aqym6gtDK8NqJ698B97h+OOnUfOJ5ISf+CKK7ZgXE9AuhKevkKartYc9Nq537hSCmGgPtyAivatd2XcFNkirfoQN6Lh+RS1tesL4N3bCWZubvohaSAxDSc/Vvyc7GKaooVDplhnopV2Ov+TXfzOpnFpKz7wa1/Y+rc6Zs9stNKWumO+acsFTwxnrdW15vGba8/A7s5HVIAw2ZlgodCuJbEVz89kbEl45gI1vJBymzus+b+Vp1+ufnoe0BwxPBd5UdOxPfA6cJ7IBOSXOVi9cz04rWmdZWz1jw5dxeXjlv+6XHBRDrLA==" />
  </Environment>
  <Environment Name="pace3_LDC2">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2adck585:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2adck585:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://BY2ADCK590:5597/" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/AdIntelligenceService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://BY2ADCVM332.phx.gbl:3089/clientcenter/mt" />
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://BY2ADCVM332.phx.gbl:3089/clientcenter/mt/cryptography" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://BY2ADCVM332:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2ADCL032" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2ADCL029" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://by2adck588:8081/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ADCL024" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2ADCVM274" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="AdCenter_Dimensions20" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT_Scrub" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCL023" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://BY2ADCK590:5598" />
    <Config key="CampaignManagement.CMMTs.0.ServicesPrefixUri" value="http://by2adck585:10094/CampaignMT/v6" />
    <Config key="CampaignManagement.CMMTs.1.ServicesPrefixUri" value="http://by2adck585:10094/CampaignMT/v6" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://by2adck585:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://by2adck585:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://by2adck585:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://by2adck585:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://by2adck585" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://by2adck585:10999/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificateThumbprint" value="04488bb735bb9688d42effa3757f4f8591857688" />
    <Config key="TestFramework.Params.EncryptedKey" value="UxOL+lH82VbpfGoKNNPVnFfBxvMYZOqgXp4RCmg398slJq1nS5NcAbeb6EMPq4oYs/9TNfIWXL6cmjv0fh6JPEX0M/uGUaaApvT5Ssf42dGr1IROXZTf74U3fXrSLBSvAbDI2GMXrAtEkecPVp9s13V9+sHbAGEkxdYbb3Bjf53a/JRhMwHDr096moInVne1UBNoGDau/KXdK5Zz+nZkjwtzkfBcQ84wqkwZScjkntnf2N2PzDDd6M19l7cTk6MTZbvsZ2VtRH76tGIXbgkzl9G2HRWQlS2Dnb6cVg/OnMWv0zBkToueWpuwzaTxPT5RycqR4Dg45nY1gWUmh6J/tA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="YOdQ/OLp2ZsKQJMW9mvrCU61o4d47xXuY43aqym6gtDK8NqJ698B97h+OOnUfOJ5ISf+CKK7ZgXE9AuhKevkKartYc9Nq537hSCmGgPtyAivatd2XcFNkirfoQN6Lh+RS1tesL4N3bCWZubvohaSAxDSc/Vvyc7GKaooVDplhnopV2Ov+TXfzOpnFpKz7wa1/Y+rc6Zs9stNKWumO+acsFTwxnrdW15vGba8/A7s5HVIAw2ZlgodCuJbEVz89kbEl45gI1vJBymzus+b+Vp1+ufnoe0BwxPBd5UdOxPfA6cJ7IBOSXOVi9cz04rWmdZWz1jw5dxeXjlv+6XHBRDrLA==" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="VNU7jvauptDzyTgHyk5CP48YxM2+xPuLPpRgLOk6mZxB" />
    <!--for bing shopping campaign-->
  </Environment>
  <Environment Name="pace3_private">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://BY2ADCK590:802/CMMT/CampaignService.svc " />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://BY2ADCK590:802/CMMT/EventTrackingContract.svc " />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://BY2ADCK590:802/CMMT/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://BY2ADCK590:802/CMMT/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://BY2ADCK590:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://BY2ADCK590:802/CMMT/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://BY2ADCK590:802/CMMT/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://BY2ADCK590:802/CMMT/AdIntelligenceService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://BY2ADCVM332:3080/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://BY2ADCVM332:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2ADCL032" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2ADCL029" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://by2adck588:8081/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ADCL024" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://by2adck590:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://by2adck590:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://by2adck590:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://by2adck590:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://by2adck590" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://by2adck590:10999/McaOfflineService" />
  </Environment>
  <Environment Name="Wallace">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://BY2ADCVM313:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://BY2ADCVM313:10094/Aggregator/v6" />
    <Config key="CampaignManagement.ImportService2.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://BY2ADCVM313:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://by2apsg045.phx.gbl:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://by2apsg045.phx.gbl:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="E:\Advertiser\CampaignMT\v6" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2MTZSSQL09" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2MTZSSQL09" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="BY2APPPERFSQL04" />
    <!--TODO: This should be updated to get ClientCenter.Dimension20DB.ServerName value from the MetaDataDB -->
    <Config key="ClientCenter.Dimension20DB.DBName" value="Adcenter_Dimension20" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2ADCL032" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://by2adck588:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT_Scrub" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCL023" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://BY2ADCVM316:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://BY2ADCVM316:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://BY2ADCVM316:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://BY2ADCVM316:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://BY2ADCVM316" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://BY2ADCVM316:10999/McaOfflineService" />
  </Environment>
  <Environment Name="Gromit">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2adcl138:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://by2adcl138:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2adcl138:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2adcl138:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2adcl138:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2adcl138:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://BY2APSG045.phx.gbl:3089/clientcenter/mt" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\data\CampaignMiddleTier\AppRoot\CampaignMT\V6" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2ADCF078" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_MetadataDB" />
  </Environment>
  <Environment Name="Local">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://localhost:10874/CampaignService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://localhost:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://localhost:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://localhost:10874/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://localhost:10874/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://localhost:10874/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://localhost:10874/AdIntelligenceService.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://localhost:10874/CampaignSyncService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://advertisertest2:5597/,http://br2pubdevgen03:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="net.tcp://{LabmanCiMachine}:3080/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://{LabmanCiMachine}:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="..\..\..\..\..\MsfService\ServiceHost" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="{LabmanCiMachine}" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="{LabmanCiMachine}" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="{LabmanCiMachine}" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="{LabmanCiMachine}" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CampaignManagement.AdInsightTestService.ServiceUri" value="http://{LabmanCiMachine}:32823/api" />
    <Config key="CampaignManagement.AdInsightODataService.ServiceUri" value="http://{LabmanCiMachine}:32897/sds/ObjectStoreQuery/V1" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://sadaptsthyp105:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="localhost:8969" />
    <Config key="CampaignManagement.AccountReparentingService.ServiceUri" value="http://localhost:927" />
  </Environment>
  <Environment Name="ProductionAPI">
    <Config key="LiveConnect.UI.Uri" value="https://login.live.com" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://api.bingads.microsoft.com/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://api.bingads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://api.bingads.microsoft.com/McaOfflineService" />
  </Environment>
  <Environment Name="ProductionAPI-LDC3">
    <Config key="LiveConnect.UI.Uri" value="https://login.live.com" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://tip.api.si.bingads.microsoft.com/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://tip.api.si.bingads.microsoft.com/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://tip.api.si.bingads.microsoft.com/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://tip.api.si.bingads.microsoft.com/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://tip.api.si.bingads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://tip.api.si.bingads.microsoft.com/McaOfflineService" />
  </Environment>
  <Environment Name="Prod-CH1">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://[MTLoadBalancerAddr]:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/Aggregator/v6" />
    <!--CH1 DNC CCMT EndPoint-->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <!-- CH1 DNS End point End here -->
    <config key="LiveConnect.UI.Uri" value="https://login.live.com" />
    <!--CH1 Production Server List Starts here-->
    <Config key="CampaignManagement.CMMTs.0.ServicesPrefixUri" value="http://CH1ADCCAMPMT01:10094/CampaignMT/v6" />
    <!--<Config key="CampaignManagement.CMMTs.1.ServicesPrefixUri" value="http://CH1ADCCAMPMT02:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.2.ServicesPrefixUri" value="http://CH1ADCCAMPMT03:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.3.ServicesPrefixUri" value="http://CH1ADCCAMPMT04:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.4.ServicesPrefixUri" value="http://CH1ADCCAMPMT05:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.5.ServicesPrefixUri" value="http://CH1ADCCAMPMT06:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.6.ServicesPrefixUri" value="http://CH1ADCCAMPMT07:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.7.ServicesPrefixUri" value="http://CH1ADCCAMPMT08:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.8.ServicesPrefixUri" value="http://CH1ADCCAMPMT09:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.9.ServicesPrefixUri" value="http://CH1ADCPCAMPMT01:10094/CampaignMT/v6"/>-->
    <!--<Config key="CampaignManagement.CMMTs.0.ServicesPrefixUri" value="http://CH1ADCK053:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.1.ServicesPrefixUri" value="http://CH1ADCK054:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.2.ServicesPrefixUri" value="http://CH1ADCK055:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.3.ServicesPrefixUri" value="http://CH1ADCK056:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.4.ServicesPrefixUri" value="http://CH1ADCK057:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.5.ServicesPrefixUri" value="http://CH1ADCK058:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.6.ServicesPrefixUri" value="http://CH1ADCK059:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.7.ServicesPrefixUri" value="http://CH1ADCK060:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.8.ServicesPrefixUri" value="http://CH1ADCK061:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.9.ServicesPrefixUri" value="http://CH1ADCK062:10094/CampaignMT/v6"/>-->
    <!--Production Server List End here-->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\CampaignServices\CampaignMTDevDevOslo" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://adcenter_campaign_mt_bcp.phx.gbl/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://adcenter_campaign_mt_bcp.phx.gbl" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://adcenter_campaign_mt_bcp.phx.gbl/McaOfflineService" />
  </Environment>
  <Environment Name="Prod-BY2">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://[MTLoadBalancerAddr]:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint-->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://internalccmtpd.trafficmanager.net:3080/clientcenter/billing/mt2" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\CampaignServices\CampaignMTDevDevOslo" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <config key="LiveConnect.UI.Uri" value="https://login.live.com" />
    <!--BY2 Production Server List Starts here-->
    <Config key="CampaignManagement.CMMTs.0.ServicesPrefixUri" value="http://BY2ADCCAMPMT01:10094/CampaignMT/v6" />
    <!--<Config key="CampaignManagement.CMMTs.1.ServicesPrefixUri" value="http://BY2ADCCAMPMT02:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.2.ServicesPrefixUri" value="http://BY2ADCCAMPMT03:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.3.ServicesPrefixUri" value="http://BY2ADCCAMPMT04:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.4.ServicesPrefixUri" value="http://BY2ADCCAMPMT05:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.5.ServicesPrefixUri" value="http://BY2ADCCAMPMT06:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.6.ServicesPrefixUri" value="http://BY2ADCCAMPMT07:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.7.ServicesPrefixUri" value="http://BY2ADCCAMPMT08:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.8.ServicesPrefixUri" value="http://BY2ADCCAMPMT09:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.9.ServicesPrefixUri" value="http://BY2ADCCAMPMT10:10094/CampaignMT/v6"/>-->
    <!--<Config key="CampaignManagement.CMMTs.0.ServicesPrefixUri" value="http://BY2ADCCAMPMT11:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.1.ServicesPrefixUri" value="http://BY2ADCCAMPMT12:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.2.ServicesPrefixUri" value="http://BY2ADCCAMPMT13:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.3.ServicesPrefixUri" value="http://BY2ADCCAMPMT14:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.4.ServicesPrefixUri" value="http://BY2ADCCAMPMT15:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.5.ServicesPrefixUri" value="http://BY2ADCCAMPMT16:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.6.ServicesPrefixUri" value="http://BY2ADCCAMPMT17:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.7.ServicesPrefixUri" value="http://BY2ADCCAMPMT18:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.8.ServicesPrefixUri" value="http://BY2ADCCAMPMT19:10094/CampaignMT/v6"/>
<Config key="CampaignManagement.CMMTs.9.ServicesPrefixUri" value="http://BY2ADCCAMPMT20:10094/CampaignMT/v6"/>-->
    <!--Production Server List End here-->
  </Environment>
  <Environment Name="PACE3_Production_Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2adck585:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2adck585:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://BY2ADCK590:5597/" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AdIntelligenceMT.ServiceUri" value="http://by2adck585:10094/CampaignMT/v6/AdIntelligenceService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://BY2ADCVM332.phx.gbl:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://BY2ADCVM332:4080/clientcenter/billing/mt2" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2ADCL032" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="BY2ADCL029" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://by2adck588:8081/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ADCL024" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT_Scrub" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCL023" />
  </Environment>
  <Environment Name="ProductionTest">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://##ServerName##:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://##ServerName##:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://BY2ADCVM332.phx.gbl:3089/clientcenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="Bay-ProductionTest">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://##ServerName##:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://##ServerName##:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="CH1-ProductionTest">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://##ServerName##:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://##ServerName##:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <!-- Below environment name looks confusing but all API test use this environment CHI, not CH1 -->
  <Environment Name="CHI-ProductionTest">
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
  </Environment>
  <Environment Name="BayLDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1_By2_adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="BayLDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2_By2_adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="Bay-ProductionTest-Online">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/clientcenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="CH1LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1_ch1_adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="CH1LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2_ch1_adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2_ch1_adcenter_campaign_mt.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2_ch1_adcenter_campaign_mt.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCH1LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1_ch1_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1_ch1_azure.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1_ch1_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1_ch1_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1_ch1_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCH01LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1-ch01-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1-ch01-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://c1-ch01-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPCH01LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2-ch01-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2-ch01-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://c2-ch01-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPCH01LDC3-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c3-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c3-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c3-ch01-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c3-ch01-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c3-ch01-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://tip-odataapi-bingads.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPMW1LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1-mw1-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1-mw1-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://c1-mw1-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPMW1LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2-mw1-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2-mw1-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://c2-mw1-odataapi.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPMW1LDC3-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c3-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c3-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c3-mw1-aggsvc.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c3-mw1-aggsvc.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c3-mw1-aggsvc.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://tip-odataapi-bingads.trafficmanager.net:8080/ODataApi/Advertiser" />
  </Environment>
  <Environment Name="EAPCH1LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2_ch1_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2_ch1_azure.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2_ch1_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2_ch1_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2_ch1_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCO3LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1_co3_aggsvc_azure.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1_co3_aggsvc_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1_co3_aggsvc_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1_co3_aggsvc_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCO3LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2_co3_aggsvc_azure.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2_co3_aggsvc_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2_co3_aggsvc_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2_co3_aggsvc_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="CH1-ProductionTest-Online">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://aggregatorservice.trafficmanager.net:801/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://aggregatorservice.trafficmanager.net:801/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://aggregatorservice.trafficmanager.net:801/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://aggregatorservice.trafficmanager.net:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://aggregatorservice.trafficmanager.net:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="Azure-PostDeploymentValidation-PROD">
    <!-- Please note - Don't copy these values into any other environment or change these values. These are strictly for Azure Post deployment validations-->
    <!-- Contact vaits or karanvo for any changes in any of the following environment settings. -->
    <!-- For Bay, Campaign MT is http://adcenter_campaign_mt.phx.gbl:10094/CampaignMT/v6/-->
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://adcenter_campaign_mt_bcp.phx.gbl:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <!-- This is keyword BI sync service. It is deprecated.-->
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <!-- Value for Bay after BCP is - https://by2internalccmtpd.trafficmanager.net:3089/ClientCenter/mt -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- Value for Bay after BCP is - BY2AC2K8MTSQL -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="CH1BAMTSQL\CH1BAMTSQL" />
    <Config key="ClientCenter.MetadataDB.DBName" value="MetadataDB" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <!-- Bay value is BY2ADCSTMSQL -->
    <Config key="ClientCenter.CustomerDB.ServerName" value="CH1AC2K8CSTMSQL" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="##PPSAzureURL##" />
    <!-- TODO: Ask from Database team. Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://api.bingads.microsoft.com/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://api.bingads.microsoft.com/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://api.bingads.microsoft.com" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://api.bingads.microsoft.com/McaOfflineService" />
  </Environment>
  <Environment Name="Azure-PostDeploymentValidation-SI">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <config key="CampaignManagement.AggregatorService.ServiceUri" value="http://by2acsicmpmt01:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://by2acsicmpmt01:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\by2acsicmpmt01\d$\app\CampaignManagementMT.AP_AdCore_CampaignMT_02112013_2036__9_8_02083_7\AppRoot" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://by2acsicmpmt01:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="http://by2apse217:5597/" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="http://siccmt.trafficmanager.net:3088/clientcenter/billing/mt" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
    <Config key="ClientCenter.CustomerDB.DBName" value="AdCenter_Customer" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="BY2ACSICTM\BY2ACSICTMSQL" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="##PPSAzureURL##" />
    <!-- Required to get API dev token -->
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="BY2ADCF085\BY2SI2K8EDTSQL2" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="AdCenter_MT" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="https://by2acsiadapi01/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://by2acsiadapi01/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="https://by2acsiadapi01" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="https://by2acsiadapi01/McaOfflineService" />
  </Environment>
  <Environment Name="SI-LDC1-AutoBVTTests">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://##ServerName##:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://##ServerName##:10094/Aggregator/v6" />
    <!--not used by autoBVT-->
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/ImportService2.svc" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <!--not used by autoBVT-->
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <!--not used by autoBVT-->
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
  </Environment>
  <Environment Name="SI-LDC2-AutoBVTTests">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://##ServerName##:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://##ServerName##:10094/Aggregator/v6" />
    <!--not used by autoBVT-->
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://##ServerName##:10094/CampaignMT/v6/ImportService2.svc" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://siccmt.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="sc-bametadatasi1.database.windows.net" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAAJekrQ+yoW74XQCcRuIsRgNCo84X9GXX2FQu8dqcClp8" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAAPQaMLCb+sj4WZV8rTErjcB93IY9KBpCSZMNypjTRAWXGSjwwOZNn9YpLU0h4emoOg==" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_Metadata" />
    <!--not used by autoBVT-->
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="BY2APSE159\BY2SI2K8BISQL5" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://camp-si-wcf.phx.gbl:808/CamServicev8/CamService.svc" />
  </Environment>
  <Environment Name="app-services-ci">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://{machine}:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://{machine}:10094/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/ImportService2.svc" />
    <config key="CampaignManagement.FraudCallback.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/FraudCallback.svc" />
    <config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <config key="CampaignManagement.BiSync.ServiceUri" value="http://{machine}:5597/,http://br2pubdevgen03:5597/" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://{machine}:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://{machine}.redmond.corp.microsoft.com:3089/ClientCenter/mt" />
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://{machine}.redmond.corp.microsoft.com:3089/ClientCenter/mt/cryptography" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://{machine}.redmond.corp.microsoft.com:4080/clientcenter/billing/mt2" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="{machine}" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_MetadataDB" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="{machine}" />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="ClientCenter.CentralStoreDB.ServerName" value="{machine}" />
    <Config key="ClientCenter.CentralStoreDB.DBName" value="CCDB_CentralStore" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="{machine}" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="Dimensions20" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\{machine}\d$\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="{machine}" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://{machine}:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://{machine}:3877/ExecutionManagement" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="{machine}" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://{machine}:18080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://{machine}:18080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://{machine}:18080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://{machine}:18080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="http://{machine}:11000" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://{machine}:10999/McaOfflineService" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="VNU7jvauptDzyTgHyk5CP48YxM2+xPuLPpRgLOk6mZxB" />
    <Config key="TestFramework.Params.EncryptedKey" value="XGBcMXCN6iSfbrbXDW//vbv5qz+KBOjrghSWh+hiesoio6aVJy6mKEQ7GidOnWFBOB65kMFuc5CN8YZuNNBd7RZTRFPuDRgC2tDUkfTQVddHED+P+u4T+Cp90cu3nT6gUK/TLNsMjjphOWax25/KI2Bev7w2Eg5tDP8uR5Ry36hbysZhJOCMyYENqDyeMC7OEec3NWOdwSLz7ZBoiaOy9YAbIoy/FPCSZ8N/IZpmPgi5ugXC7IQpUCIjSUvrKZfXrKdbfwogJMeiAjs/GpspSo/IyAgWbWoqY4AS349JamiAohpYfmzBGUqgTBCYyDYYTAchHJphC/4g/HrFKFUHXA==" />
    <Config key="TestFramework.Params.EncryptedIV" value="JoeFQyEs2NYHWarQMbp3tF/Ih8Gik+**************************+IYxhU0Ju7LHEOtLCNNWZ8oExs/45mmhwZaWgnhRGnerJszwTU3hwFqMRRmLhAEUsbb36HiJZNLURWnQQ3dv49oGHzu36P4f+tN3PRZ6u/+bW/Z0hVH3LhdC/41l7YaAf9leKwDIzgzQvEnMwQJVxAzabv8O6cUkOJ2LnoJUCvAf9h3IugG/OZvNTN850sFc5gt7Ltr4ciZ3tQTVtARdQV/nAdIjJP52UJ5dOV5vOHfE5RvQrt6BRgTUO1x0L3V2hqVaau0GT6bi33x5C1VwEcZoGsKAtQ==" />
  </Environment>
  <Environment Name="app-services-ci-labman">
    <Config key="MetadataService.Service.ServiceUri" value="http://{localhost}:818" />
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://{localhost}:10095/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://localhost:10095/Aggregator/v6" />
    <config key="CampaignManagement.ImportService2.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/ImportService2.svc" />
    <config key="CampaignManagement.FraudCallback.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/FraudCallback.svc" />
    <config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/EditorialCallbackService.svc" />
    <config key="CampaignManagement.BiSync.ServiceUri" value="http://{localhost}:5597/,http://br2pubdevgen03:5597/" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://{localhost}:10096/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://{localhost}.redmond.corp.microsoft.com:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://{localhost}.redmond.corp.microsoft.com:4080/clientcenter/billing/mt2" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="{localhost}" />
    <Config key="ClientCenter.MetadataDB.DBName" value="AdCenter_MetadataDB" />
    <Config key="ClientCenter.MetadataDB.DBTypeName" value="MetadataDB" />   <!-- This is added to invoke keyvault related call in CI run to detect related bindingredirect failure earier than SI --> 
    <Config key="ClientCenter.MetadataDB.KeyVaultName" value="CampaignSecretsKVCI" /> <!-- This is added to invoke keyvault related call in CI run to detect related bindingredirect failure earier than SI --> 
    <Config key="ClientCenter.Cryptography.ServiceUri" value="https://{localhost}.redmond.corp.microsoft.com:3089/ClientCenter/mt/cryptography" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="{localhost}" />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="ClientCenter.CentralStoreDB.ServerName" value="{localhost}" />
    <Config key="ClientCenter.CentralStoreDB.DBName" value="CCDB_CentralStore" />
    <Config key="ClientCenter.Dimension20DB.ServerName" value="{localhost}" />
    <Config key="ClientCenter.Dimension20DB.DBName" value="Dimensions20" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAANYpb6aCq39S2LSOkY4D8ve2CKQtkePkjWRK4oBjWnEX" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAADgRFMp3mqg2M3g4yJDwIPVTWVPJxahQ4wKqyG5sOI7x" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="\\{localhost}\d$\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="{localhost}" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="net.tcp://{localhost}:8081/CamServicev8/CamService.svc" />
    <Config key="CampaignManagement.APIV13.ServiceUri" value="https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/CampaignManagementService.svc" />
    <Config key="CampaignManagement.APIV13BulkService.ServiceUri" value="https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/BulkService.svc" />
    <Config key="CampaignManagement.APIV13FileUploadService.ServiceUri" value="https://api.ci.ads.microsoft.com:10098/Api/Advertiser/CampaignManagement/V13/FileUploadService.svc" />
    <Config key="CampaignManagement.AdCenter_MTDB.ServerName" value="{localhost}" />
    <Config key="CampaignManagement.AdCenter_MTDB.DBName" value="adCenter_MT_Scrub" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://{localhost}:18080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.PartnerODataApi.ServiceUri" value="http://{localhost}:18080/ODataApi/Partner" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://{localhost}:18080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://{localhost}:18080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://{localhost}:18080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="http://localhost:11000" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://localhost:10999/McaOfflineService" />
    <Config key="CampaignManagement.MockGserver.ServiceUri" value="http://localhost:1901/gserver" />
    <Config key="CampaignManagement.MockMmc.ServiceUri" value="http://localhost:1901/bmc.mock/BingAds/" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://{localhost}:5598" />
    <Config key="CampaignManagement.AccountReparentingService.ServiceUri" value="http://localhost:927" />
    <Config key="CampaignManagement.TaskEngineService.ServiceUri" value="http://{localhost}:3877/ExecutionManagement" />
    <Config key="CampaignManagement.AdInsightTestService.ServiceUri" value="http://{localhost}:32823/api" />
    <Config key="CampaignManagement.AdInsightODataService.ServiceUri" value="http://localhost:32897/sds/ObjectStoreQuery/V1" />
    <Config key="Aggregation.AggregationService.ServiceUri" value="http://localhost:1901/aggsvc.mock/" />
    <Config key="TestFramework.Params.EncryptionCertificatePath" value="NA" />
    <Config key="TestFramework.Params.EncryptionCertificatePassword" value="pxhOmLUF8fZ4Dd4FjQA/vKOBd5aUGDNaykJfZhoaxWgR" />
    <Config key="TestFramework.Params.EncryptedKey" value="mSDRl0+JYANIi/SfQMTlP4o/haG57MxxCJbEy7hM4iSCx1tAy0wT48BNEyoYYQTc4OmDiu8RplhgXvp2KE5WByr3AXss2kYwSU9nZrfju+ZZ1VzYe6M9n3HBDXoCZzMrJAj0znmRMwFC+b3PC8Q62aymM3vPL5OkQgpu2/q3IPaBB2BQMnkC3DXdzpEqw+BysFGJihDIGUCr/chlMvk+hN3DqYk2zGmMtqHBe/FfJkM/tI12qorWlu/hqhzOQD+PBQECaX+q/LzjzjVSSufeTBpzlSCQRyv+8lClPIQkYaXrtZt7B2OUGO4mpDBkGKHooKvZE2/vu8E5wEgR4FXQbQ==" />
    <Config key="TestFramework.Params.EncryptedIV" value="o9D7ssf3F7fxkuLlvuPP9y5Zuh4KoYkuqsUI6r0gVUby3XqJT30M5wBmPb2a3OluT7DCt+5SPcIVPs2edb4INp05SuF46PHSvSQw2DXHlqU6pXHlm8W8xX/VlwLV8XtYtBIpNjD6Fte6PQaADCyiiiB6liaLrxtge9E8S1R0BzI5PkONsPBA6OK0qE6deCEI9LLz9V7MB29tiUWbbQhVRJlBrmykjJQk7dy5Nz9G+RJO29K0Q1B6MhEpuj4eJ45pdJxJyma9bBIyu8PoPx8Nti8Wqxl3aK9S8bUL/+mPw/5JmDeg+GofBtkmXApDnfeZTGZA8TjfgTuTHgzAlyj1Zw==" />
  </Environment>
  <Environment Name="CO3LDC3-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c3_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c3_co3_campaignmt_azure.phx.gbl.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c3_co3_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c3_co3_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c3_co3_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCO3LDC2-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c2_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c2_co3_campaignmt_azure.phx.gbl.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c2_co3_campaignmt_azure.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c2_co3_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c2_co3_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="EAPCO3LDC1-ProductionTest-Vip">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://c1_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://c1_co3_campaignmt_azure.phx.gbl.gbl:10094/CampaignMT/v6/EventTrackingContract.svc" />
    <Config key="CampaignManagement.CampaignMTSync.ServiceUri" value="http://c1_co3_campaignmt_azure.phx.gbl:10094/CampaignMT/v6/CampaignSyncService.svc" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://c1_co3_azure.phx.gbl:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://c1_co3_azure.phx.gbl:10094/Aggregator/v6" />
    <!-- BCP DNS CCMT Endpoint, update to PACE LDC2 CCMT now for testing purpose -->
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://internalccmtpd.trafficmanager.net:3089/ClientCenter/mt" />
    <!-- BCP DNS CCMT End point End here -->
    <Config key="ClientCenter.MetadataDB.ServerName" value="" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="d:\data\CampaignMiddleTier\AppRoot\CampaignMT\v6" />
    <Config key="CampaignManagement.AnalyticsDB.ServerName" value="" />
    <Config key="CampaignManagement.AnalyticsDB.DBName" value="AdCenter_AnalyticsOLTP" />
    <Config key="CAM.CAMService.ServiceUri" value="http://localhost:1746/CamMockService.svc" />
  </Environment>
  <Environment Name="shardingdevdev">
    <Config key="CampaignManagement.CampaignMT.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6//CampaignService.svc" />
    <Config key="CampaignManagement.EventTrackingContract.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6//EventTrackingContract.svc" />
    <Config key="CampaignManagement.AppExtensionsMetadataService.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6/AppExtensionsMetadataService.svc" />
    <Config key="CampaignManagement.CampaignMT.CodePath" value="D:\CampaignServices\CampaignMT\Jan" />
    <Config key="CampaignManagement.AggregatorService.ServiceUri" value="http://shardingdevdev:10094/AggregatorService.svc" />
    <Config key="CampaignManagement.AggregatorServiceHttpApi.ServiceUri" value="http://shardingdevdev:10094/Aggregator/v6" />
    <Config key="CampaignManagement.ImportService2.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6/ImportService2.svc" />
    <Config key="CampaignManagement.FraudCallback.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6/FraudCallback.svc" />
    <Config key="CampaignManagement.EditorialCallback.ServiceUri" value="http://shardingdevdev:10094/CampaignMT/v6/EditorialCallbackService.svc" />
    <Config key="CampaignManagement.BiSync.ServiceUri" value="???" />
    <Config key="ClientCenter.CCMT5.ServiceUri" value="https://shardingdevdev.redmond.corp.microsoft.com:3089/clientcenter/mt" />
    <Config key="Billing.BillingMT.ServiceUri" value="net.tcp://shardingdevdev.redmond.corp.microsoft.com:4080/clientcenter/billing/mt2" />
    <Config key="ClientCenter.MetadataDB.ServerName" value="shardingdevdev" />
    <Config key="ClientCenter.MetadataDB.DBName" value="Adcenter_MetadataDB" />
    <Config key="ClientCenter.MetadataDB.EncryptedUserId" value="EAAAANYpb6aCq39S2LSOkY4D8ve2CKQtkePkjWRK4oBjWnEX" />
    <Config key="ClientCenter.MetadataDB.EncryptedPwd" value="EAAAADgRFMp3mqg2M3g4yJDwIPVTWVPJxahQ4wKqyG5sOI7x" />
    <Config key="ClientCenter.CustomerDB.ServerName" value="shardingdevdev" />
    <Config key="ClientCenter.CustomerDB.DBName" value="CustomerDB" />
    <Config key="CampaignManagement.PhoneProvisioningAPI.ServiceUri" value="http://shardingdevdev:5598" />
    <Config key="CampaignManagement.BingAdsODataApi.ServiceUri" value="http://shardingdevdev:8080/ODataApi/Advertiser" />
    <Config key="CampaignManagement.TravelODataApi.ServiceUri" value="http://shardingdevdev:8080/ODataApi/Travel" />
    <Config key="CampaignManagement.McaODataApi.ServiceUri" value="http://shardingdevdev:8080/ODataApi/Mca" />
    <Config key="CampaignManagement.McaPartnerODataApi.ServiceUri" value="http://shardingdevdev:8080/ODataApi/McaPartner" />
    <Config key="CampaignManagement.McaWebhookService.ServiceUri" value="http://shardingdevdev:11000" />
    <Config key="CampaignManagement.McaOfflineService.ServiceUri" value="http://shardingdevdev:10999/McaOfflineService" />
  </Environment>
</Environments>