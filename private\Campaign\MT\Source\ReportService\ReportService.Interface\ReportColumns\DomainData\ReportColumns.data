[{"ReportColumnId": "Report.AccountStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "4": "Inactive"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Inactive": "4"}}, {"ReportColumnId": "Report.ContentType", "IsFilterable": true, "LocalizationKeys": {"default": {"52": "Placement", "56": "Topic"}}, "NameToDbValues": {"Placement": "52", "Topic": "56"}}, {"ReportColumnId": "Report.Content", "IsFilterable": true, "LocalizationKeys": {"default": {"52.1": "MSNHomepages", "52.2": "MSNNews", "52.3": "MSNSports", "52.4": "MSNFinance", "52.5": "MSNEntertainment", "52.6": "MSNLifestyle", "52.7": "MSNHealth", "52.8": "MSN<PERSON>eather", "52.9": "MSNFood", "52.10": "MSNTravel", "52.11": "MSNAutos", "52.12": "MicrosoftEdgeNewTabPage", "52.13": "Productivity", "52.14": "Gaming", "52.15": "MSN"}}, "NameToDbValues": {"MSNHomepages": "52.1", "MSNNews": "52.2", "MSNSports": "52.3", "MSNFinance": "52.4", "MSNEntertainment": "52.5", "MSNLifestyle": "52.6", "MSNHealth": "52.7", "MSNWeather": "52.8", "MSNFood": "52.9", "MSNTravel": "52.10", "MSNAutos": "52.11", "MicrosoftEdgeNewTabPage": "52.12", "Productivity": "52.13", "Gaming": "52.14", "MSN": "52.15"}}, {"ReportColumnId": "Report.AdDistribution", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Search", "2": "Content", "3": "Search", "4": "Content", "10": "Audience", "12": "LinkedIn", "255": "Cross-network"}}, "NameToDbValues": {"Search": "1", "Content": "2", "Audience": "10", "LinkedIn": "12", "Cross-network": "255"}}, {"ReportColumnId": "Report.AdGroupStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted", "6": "Draft", "7": "Expired"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3", "Expired": "7"}}, {"ReportColumnId": "Report.LocationType", "LocalizationKeys": {"default": {"0": "Unknown", "1": "PhysicalLocation", "2": "LocationOfInterest"}}}, {"ReportColumnId": "Report.AdType", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "TextAd", "2": "MobileAd", "4": "ImageAd", "8": "LocalAd", "16": "RichMediaAd", "32": "ThirdPartyCreativeAd", "64": "RichAd", "128": "ProductAd", "256": "AppInstallAd", "512": "DynamicSearchAd", "1024": "ExpandedTextAd", "4096": "ResponsiveAd", "8192": "ResponsiveSearchAd", "16384": "LinkedInAd"}}, "NameToDbValues": {"Text": "1", "MobileAd": "2", "ImageAd": 4, "Local": "8", "RichMediaAd": "16", "ThirdPartyCreativeAd": "32", "RichAd": "64", "Product": "128", "AppInstall": "256", "DynamicSearchAd": "512", "ExpandedText": "1024", "ResponsiveAd": "4096", "ResponsiveSearchAd": "8192"}}, {"ReportColumnId": "Report.BidMatchType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "2": "Phrase", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}, "NameToDbValues": {"Unknown": 0, "Exact": "1", "Phrase": "2", "Broad": "3", "Content": "5", "Predictive": "9", "NotApplicable": "127"}}, {"ReportColumnId": "Report.BidStrategyType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "InheritParent", "1": "ManualCPC", "2": "MaximizeClicks", "3": "MaximizeConversions", "4": "TargetCPA", "5": "EnhancedCPC", "6": "TargetRoas", "8": "MaxConversionValue", "9": "TargetImpressionShare", "10": "PortfolioBidStrategy", "11": "ManualCPV", "12": "ManualCPM", "13": "PercentCPC", "14": "Commission", "15": "CostPerSale", "16": "ManualCPA"}}, "NameToDbValues": {"ManualCpc": "1", "MaximizeClicks": "2", "MaximizeConversions": "3", "TargetCPA": "4", "EnhancedCpc": "5", "TargetRoas": "6", "MaxConversionValue": "8", "TargetImpressionShare": "9", "ManualCpv": "11", "ManualCpm": "12", "PercentCPC": "13", "Commission": "14", "ManualCpa": "16"}}, {"ReportColumnId": "ReportV12.CampaignStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted", "5": "BudgetPaused", "8": "Suspended"}}, "NameToDbValues": {"Active": "1", "Cancelled": "1", "Paused": "2", "Deleted": "3", "BudgetPaused": "5", "Suspended": "8"}}, {"ReportColumnId": "Report.CampaignStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted", "5": "BudgetPaused", "8": "Suspended"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3", "BudgetPaused": "5", "Suspended": "8"}}, {"ReportColumnId": "Report.DeliveredMatchType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "-1": "ExactCloseVariant", "2": "Phrase", "-2": "PhraseClose<PERSON><PERSON>t", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}, "NameToDbValues": {"Unknown": "0", "Exact": "1", "ExactCloseVariant": "-1", "Phrase": "2", "PhraseCloseVariant": "-2", "Broad": "3", "Content": "5", "Predictive": "9", "NotApplicable": "127"}}, {"ReportColumnId": "ReportPreview.DeliveredMatchType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "-1": "ExactCloseVariant", "2": "Phrase", "-2": "PhraseClose<PERSON><PERSON>t", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}, "NameToDbValues": {"Unknown": "0", "Exact": "1", "ExactCloseVariant": "-1", "Phrase": "2", "PhraseCloseVariant": "-2", "Broad": "3", "Content": "5", "Predictive": "9", "NotApplicable": "127"}}, {"ReportColumnId": "ReportPreviewAC.DeliveredMatchType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "-1": "ExactCloseVariant", "2": "Phrase", "-2": "PhraseClose<PERSON><PERSON>t", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}, "NameToDbValues": {"Unknown": "0", "Exact": "1", "ExactCloseVariant": "-1", "Phrase": "2", "PhraseCloseVariant": "-2", "Broad": "3", "Content": "5", "Predictive": "9", "NotApplicable": "127"}}, {"ReportColumnId": "Report.DeviceType", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "1": "Computer", "2": "Smartphone", "3": "NonsmartPhone", "4": "Tablet", "16": "ConnectedTVs"}}, "NameToDbValues": {"Unknown": "0", "Computer": "1", "SmartPhone": "2", "NonSmartPhone": "3", "Tablet": "4", "ConnectedTVs": "16"}}, {"ReportColumnId": "Report.KeywordStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3"}}, {"ReportColumnId": "Report.Network", "LocalizationKeys": {"default": {"0": "Unknown", "1": "BingYahooSearch", "2": "SyndicatedSearchPartners", "3": "AOLSearch", "4": "Content", "5": "Audience", "6": "InHouse", "7": "MicrosoftSitesAndSelectTraffic", "8": "OnO", "9": "XandrMonetize", "10": "ThreeP", "11": "LinkedIn", "255": "Cross-network"}}}, {"ReportColumnId": "Report.AdRelevance", "IsFilterable": true, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report.ExpectedCtr", "IsFilterable": true, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report.LandingPageExperience", "IsFilterable": true, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report.Keyword", "IsFilterable": true, "MaxCount": 75, "MaxLength": 100}, {"ReportColumnId": "Report.QualityScore", "IsFilterable": true, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, {"ReportColumnId": "Report2.AdRelevance", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.ExpectedCtr", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.ExpectedCtrCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.ExpectedCtrAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.ExpectedCtrKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.LandingPageExperience", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.LandingPageExperienceKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.LandingPageExperienceCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.LandingPageExperienceAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}, "NameToDbValues": {"1": "1", "2": "2", "3": "3"}}, {"ReportColumnId": "Report2.QualityScore", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, {"ReportColumnId": "Report2.QualityScoreCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, {"ReportColumnId": "Report2.QualityScoreAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, {"ReportColumnId": "Report2.QualityScoreKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, "NameToDbValues": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}, {"ReportColumnId": "Report2.HistoricalAdRelevance", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalAdRelevanceCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalAdRelevanceAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalAdRelevanceKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalExpectedCtr", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalExpectedCtrCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalExpectedCtrAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalExpectedCtrKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalLandingPageExperience", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalLandingPageExperienceKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3"}}}, {"ReportColumnId": "Report2.HistoricalQualityScore", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}}, {"ReportColumnId": "Report2.HistoricalQualityScoreCampaign", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}}, {"ReportColumnId": "Report2.HistoricalQualityScoreAdGroup", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}}, {"ReportColumnId": "Report2.HistoricalQualityScoreKeyword", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7", "8": "8", "9": "9", "10": "10"}}}, {"ReportColumnId": "Report.Language", "IsFilterable": true, "LocalizationKeys": {"default": {"AA": "AA", "AB": "AB", "AF": "AF", "AM": "AM", "AR": "AR", "AS": "AS", "AY": "AY", "AZ": "AZ", "BA": "BA", "BE": "BE", "BG": "BG", "BH": "BH", "BI": "BI", "BN": "BN", "BO": "BO", "BR": "BR", "BS": "BS", "CA": "CA", "CO": "CO", "CS": "CS", "CY": "CY", "DA": "DA", "DE": "DE", "DZ": "DZ", "EL": "EL", "EN": "EN", "EO": "EO", "ES": "ES", "ET": "ET", "EU": "EU", "FA": "FA", "FI": "FI", "FJ": "FJ", "FO": "FO", "FR": "FR", "FY": "FY", "GA": "GA", "GD": "GD", "GL": "GL", "GN": "GN", "GU": "GU", "HA": "HA", "HI": "HI", "HR": "HR", "HU": "HU", "HY": "HY", "IA": "IA", "IE": "IE", "IK": "IK", "ID": "ID", "IS": "IS", "IT": "IT", "IW": "IW", "JA": "JA", "JI": "JI", "JV": "JV", "KA": "KA", "KK": "KK", "KL": "KL", "KM": "KM", "KN": "KN", "KO": "KO", "KS": "KS", "KU": "KU", "KY": "KY", "LA": "LA", "LN": "LN", "LO": "LO", "LT": "LT", "LV": "LV", "MG": "MG", "MI": "MI", "MK": "MK", "ML": "ML", "MN": "MN", "MO": "MO", "MR": "MR", "MS": "MS", "MT": "MT", "MY": "MY", "NA": "NA", "NB": "NB", "NE": "NE", "NL": "NL", "NO": "NO", "OC": "OC", "OM": "OM", "OR": "OR", "PA": "PA", "PL": "PL", "PS": "PS", "PT": "PT", "QU": "QU", "RM": "RM", "RN": "RN", "RO": "RO", "RU": "RU", "RW": "RW", "SA": "SA", "SD": "SD", "SG": "SG", "SH": "SH", "SI": "SI", "SK": "SK", "SL": "SL", "SM": "SM", "SN": "SN", "SO": "SO", "SQ": "SQ", "SR": "SR", "SS": "SS", "ST": "ST", "SU": "SU", "SV": "SV", "SW": "SW", "TA": "TA", "TE": "TE", "TG": "TG", "TH": "TH", "TI": "TI", "TK": "TK", "TL": "TL", "TN": "TN", "TO": "TO", "TR": "TR", "TS": "TS", "TT": "TT", "TW": "TW", "UK": "UK", "UR": "UR", "UZ": "UZ", "VI": "VI", "VO": "VO", "WO": "WO", "XH": "XH", "YO": "YO", "ZH": "ZH", "ZU": "ZU", "ZH-HANS": "ZH_HANS", "ZH-HANT": "ZH_HANT"}}, "NameToDbValues": {"Bulgarian": "BG", "Croatian": "HR", "Czech": "CS", "Danish": "DA", "Dutch": "NL", "English": "EN", "Estonian": "ET", "Finnish": "FI", "French": "FR", "German": "DE", "Greek": "EL", "Hungarian": "HU", "Italian": "IT", "Japanese": "JA", "Latvian": "LV", "Lithuanian": "LT", "Maltese": "MT", "Norwegian": "NB", "Polish": "PL", "Portuguese": "PT", "Romanian": "RO", "Slovak": "SK", "Slovenian": "SL", "Swedish": "SV", "Spanish": "ES", "Arabic": "AR", "Hebrew": "IW", "Korean": "KO", "Russian": "RU", "TraditionalChinese": "ZH-HANT", "Indonesian": "ID", "Thai": "TH", "Turkish": "TR", "Vietnamese": "VI", "SimplifiedChinese": "ZH-HANS", "Malay": "MS", "Tagalog": "TL", "Hindi": "HI"}}, {"ReportColumnId": "Report.TopVsOther", "LocalizationKeys": {"default": {"0": "Unknown", "10": "BingYahooSearchTop", "11": "BingYahooSearchTop", "12": "BingYahooSearchOther", "21": "SyndicatedSearchPartnersTop", "22": "SyndicatedSearchPartnersOther", "31": "AOLSearchTop", "32": "AOLSearchOther", "40": "Content", "50": "Audience", "51": "AudienceTop", "52": "AudienceOther", "61": "InHouseTop", "62": "InHouseOther", "71": "MicrosoftSitesAndSelectTrafficTop", "72": "MicrosoftSitesAndSelectTrafficOther", "110": "LinkedIn", "254": "Cross-network"}}}, {"ReportColumnId": "Report.DeviceOS", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Unknown", "201": "Other", "202": "Windows", "203": "iOS", "204": "Android", "205": "BlackBerry"}}, "NameToDbValues": {"Other": "201", "Windows": "202", "iOS": "203", "Android": "204", "BlackBerry": "205"}}, {"ReportColumnId": "Report.AdExtensionType", "LocalizationKeys": {"default": {"0": "Unknown", "10": "SitelinkExtension", "11": "LocationExtension", "12": "CallExtension", "13": "ProductExtension", "14": "MeteredCallExtension", "8": "ClickTocall", "9": "ClickToURL", "35": "ImageExtension", "38": "AppAdExtension", "45": "DataTableExtension", "40": "ActionExtension", "41": "FormExtension", "39": "ReviewExtension", "42": "CalloutExtension", "46": "VideoExtension", "47": "SocialExtension", "50": "DynamicProductExtension", "51": "StructuredSnippetExtension", "53": "FilterLinkExtension", "54": "FlyerExtension", "55": "PriceExtension", "56": "LocalInventoryExtension", "57": "PromotionExtension", "58": "AffiliateLocationExtension", "59": "DynamicDataExtension", "62": "LeadFormExtension", "63": "LogoExtension", "16062": "ChatExtension", "16203": "DynamicProductExtension", "16205": "FilterLinkExtension", "16207": "DynamicDataExtension", "16210": "OfferExtension", "17000": "DynamicLocationExtensions", "17001": "ConsumerRating", "17002": "DynamicAdEnhancement", "17003": "DynamicCallout", "17004": "DynamicPartnerDataEnhancement", "17005": "DynamicSitelink", "17006": "DynamicStructuredSnippet", "17007": "LongerAdHeadline", "17008": "PreviousVisit", "17009": "SellerRating", "17011": "DynamicMultimediaExtensions", "17013": "SyndicationOnlyDecorations", "17014": "BusinessLogo"}}}, {"ReportColumnId": "Report.ClickType", "LocalizationKeys": {"default": {"1": "AdTitleHeadline", "2": "PhoneCall", "3": "DrivingDirection", "4": "Sitelink", "5": "BusinessImage", "6": "Image", "8": "InstallApp", "9": "DataTable", "10": "ActionLink", "11": "Form", "12": "Review", "13": "Callout", "29": "Video", "30": "Offer", "31": "DynamicData", "32": "Social", "33": "DynamicProduct", "34": "FilterLink", "35": "GetRide", "38": "ImageText", "39": "Flyer", "40": "DynamicProductImage", "41": "Cha<PERSON>", "42": "Price", "44": "Promotion", "45": "AffiliateLocation", "46": "DynamicSitelink", "47": "DynamicCallout", "48": "DynamicStructuredSnippet", "49": "DynamicAdEnhancements", "50": "VideoActionLink", "51": "DynamicDataExtension", "53": "AdImage", "54": "AdActionLink", "57": "LeadForm"}}}, {"ReportColumnId": "Report.AdStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"64": "Paused", "70": "Pending", "71": "Active", "73": "Rejected", "75": "Deleted"}}, "NameToDbValues": {"Paused": "64", "Pending": "70", "Active": "71", "Rejected": "73", "Deleted": "75"}}, {"ReportColumnId": "Report.AssociationStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"165": "Excluded", "90": "Active", "89": "Paused", "91": "Deleted", "1": "Excluded", "2": "Enabled", "3": "Paused", "4": "Deleted"}}, "NameToDbValues": {"Excluded": "165", "Active": "90", "Paused": "89", "Deleted": "91"}}, {"ReportColumnId": "Report.TargetingSetting", "LocalizationKeys": {"default": {"0": "Bid<PERSON>nly", "1": "TargetAndBid"}}}, {"ReportColumnId": "Report.AudienceType", "LocalizationKeys": {"default": {"23": "RemarketingList", "26": "CustomAudience", "27": "InmarketAudience", "32": "ProductAudience", "34": "SimilarToRemarketingList", "37": "CustomerList", "38": "CombinedList", "45": "PredictiveTargeting", "46": "ContextualTargeting", "50": "CustomSegment", "51": "ImpressionBasedRemarketingList"}}}, {"ReportColumnId": "Report.BudgetAssociationStatus", "LocalizationKeys": {"default": {"0": "Ended", "1": "Current"}}}, {"ReportColumnId": "Report.BudgetStatus", "LocalizationKeys": {"default": {"0": "NotPaused", "2": "DailyPaused", "4": "MonthlyPaused", "121": "Active", "123": "Deleted"}}}, {"ReportColumnId": "Report.CampaignType", "LocalizationKeys": {"default": {"1": "SearchAndContent", "3": "Shopping", "4": "DynamicSearch", "6": "Audience", "7": "Smart", "8": "Hotel", "9": "PerformanceMax", "10": "App", "11": "LinkedIn"}}}, {"ReportColumnId": "ReportComputed.DayOfWeek", "LocalizationKeys": {"default": {"1": "Sunday", "2": "Monday", "3": "Tuesday", "4": "Wednesday", "5": "Thursday", "6": "Friday", "7": "Saturday"}}}, {"ReportColumnId": "ReportNullableComputed.DayOfWeek", "LocalizationKeys": {"default": {"1": "Sunday", "2": "Monday", "3": "Tuesday", "4": "Wednesday", "5": "Thursday", "6": "Friday", "7": "Saturday"}}}, {"ReportColumnId": "Report.CheckInDateDayOfWeek", "LocalizationKeys": {"default": {"1": "Sunday", "2": "Monday", "3": "Tuesday", "4": "Wednesday", "5": "Thursday", "6": "Friday", "7": "Saturday"}}}, {"ReportColumnId": "Report.DateType", "LocalizationKeys": {"default": {"0": "DefaultDate", "1": "SelectedDate"}}}, {"ReportColumnId": "ACReport.DeliveredMatchType", "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "2": "Phrase", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}}, {"ReportColumnId": "AdGroupReport.Status", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted", "6": "Draft", "7": "Expired"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3", "Expired": "7"}}, {"ReportColumnId": "Report.SearchQuery", "IsFilterable": true, "MaxCount": 75, "MaxLength": 100}, {"ReportColumnId": "Report.SearchCategory", "IsFilterable": true, "MaxCount": 75, "MaxLength": 100}, {"ReportColumnId": "Report.DynamicAdTargetStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3"}}, {"ReportColumnId": "BSCReport.ClickType", "LocalizationKeys": {"default": {"1": "ShoppingAd", "12": "Unknown", "37": "MerchantPromotion", "43": "ShoppingAdLocal", "44": "ShoppingAdMultiChannelOnline", "45": "ShoppingAdMultiChannelLocal", "55": "BuyNow"}}}, {"ReportColumnId": "Report.PartitionType", "LocalizationKeys": {"default": {"0": "Subdivision", "1": "Unit"}}}, {"ReportColumnId": "ReportPreview.PartitionType", "LocalizationKeys": {"default": {"0": "Subdivision", "1": "Unit"}}}, {"ReportColumnId": "Report.CallStatus", "LocalizationKeys": {"default": {"0": "Missed", "1": "Received"}}}, {"ReportColumnId": "Report.CallTypeName", "LocalizationKeys": {"default": {"0": "ClickCall", "1": "ManualCall"}}}, {"ReportColumnId": "ReportInt.PartitionType", "LocalizationKeys": {"default": {"0": "Subdivision", "1": "Unit"}}}, {"ReportColumnId": "Report.GoalId", "IsFilterable": true, "MaxCount": 75, "MaxLength": 100}, {"ReportColumnId": "Report.GoalType", "LocalizationKeys": {"default": {"0": "DestinationURL", "1": "Duration", "2": "PageVisit", "3": "Custom", "4": "TagSpecific", "5": "AppInstall", "6": "MultiStage", "7": "OfflineConversion", "8": "InStoreTransaction", "10": "ProductConversion", "11": "Smart", "12": "InStoreVisit"}}}, {"ReportColumnId": "Report.GoalCategory", "LocalizationKeys": {"default": {"0": "NotAssign", "1": "Purchase", "2": "AddToCart", "3": "BeginCheckout", "4": "Subscribe", "5": "SubmitLeadForm", "6": "BookAppointment", "7": "Signup", "8": "RequestQuote", "9": "GetDirections", "10": "OutboundClick", "11": "Contact", "12": "<PERSON><PERSON><PERSON><PERSON>", "13": "Download", "14": "Other", "15": "InStoreVisit"}}}, {"ReportColumnId": "Report.Country", "IsFilterable": true, "NameToDbValues": {"AD": "AD", "AE": "AE", "AF": "AF", "AG": "AG", "AI": "AI", "AL": "AL", "AM": "AM", "AN": "AN", "AO": "AO", "AQ": "AQ", "AR": "AR", "AS": "AS", "AT": "AT", "AU": "AU", "AW": "AW", "AZ": "AZ", "BA": "BA", "BB": "BB", "BD": "BD", "BE": "BE", "BF": "BF", "BG": "BG", "BH": "BH", "BI": "BI", "BJ": "BJ", "BM": "BM", "BN": "BN", "BO": "BO", "BR": "BR", "BS": "BS", "BT": "BT", "BW": "BW", "BY": "BY", "BZ": "BZ", "CA": "CA", "CC": "CC", "CD": "CD", "CF": "CF", "CG": "CG", "CH": "CH", "CI": "CI", "CK": "CK", "CL": "CL", "CM": "CM", "CN": "CN", "CO": "CO", "CR": "CR", "CV": "CV", "CX": "CX", "CY": "CY", "CZ": "CZ", "DE": "DE", "DJ": "DJ", "DK": "DK", "DM": "DM", "DO": "DO", "DZ": "DZ", "EC": "EC", "EE": "EE", "EG": "EG", "ER": "ER", "ES": "ES", "ET": "ET", "FI": "FI", "FJ": "FJ", "FK": "FK", "FM": "FM", "FO": "FO", "FR": "FR", "GA": "GA", "GB": "GB", "GD": "GD", "GE": "GE", "GF": "GF", "GG": "GG", "GH": "GH", "GI": "GI", "GL": "GL", "GM": "GM", "GN": "GN", "GP": "GP", "GQ": "GQ", "GR": "GR", "GT": "GT", "GU": "GU", "GW": "GW", "GY": "GY", "GZ": "GZ", "HK": "HK", "HN": "HN", "HR": "HR", "HT": "HT", "HU": "HU", "ID": "ID", "IE": "IE", "IL": "IL", "IN": "IN", "IQ": "IQ", "IS": "IS", "IT": "IT", "JE": "JE", "JM": "JM", "JO": "JO", "JP": "JP", "KE": "KE", "KG": "KG", "KH": "KH", "KI": "KI", "KM": "KM", "KN": "KN", "KR": "KR", "KW": "KW", "KY": "KY", "KZ": "KZ", "LA": "LA", "LB": "LB", "LC": "LC", "LI": "LI", "LK": "LK", "LR": "LR", "LS": "LS", "LT": "LT", "LU": "LU", "LV": "LV", "LY": "LY", "MA": "MA", "MC": "MC", "MD": "MD", "ME": "ME", "MG": "MG", "MH": "MH", "MK": "MK", "ML": "ML", "MM": "MM", "MN": "MN", "MO": "MO", "MP": "MP", "MQ": "MQ", "MR": "MR", "MS": "MS", "MT": "MT", "MU": "MU", "MV": "MV", "MW": "MW", "MX": "MX", "MY": "MY", "MZ": "MZ", "NA": "NA", "NC": "NC", "NE": "NE", "NF": "NF", "NG": "NG", "NI": "NI", "NL": "NL", "NO": "NO", "NP": "NP", "NR": "NR", "NU": "NU", "NZ": "NZ", "OM": "OM", "PA": "PA", "PE": "PE", "PF": "PF", "PG": "PG", "PH": "PH", "PK": "PK", "PL": "PL", "PM": "PM", "PN": "PN", "PR": "PR", "PS": "PS", "PT": "PT", "PW": "PW", "PY": "PY", "QA": "QA", "RE": "RE", "RO": "RO", "RS": "RS", "RU": "RU", "RW": "RW", "SA": "SA", "SB": "SB", "SC": "SC", "SD": "SD", "SE": "SE", "SG": "SG", "SH": "SH", "SI": "SI", "SK": "SK", "SL": "SL", "SM": "SM", "SN": "SN", "SO": "SO", "SR": "SR", "ST": "ST", "SV": "SV", "SZ": "SZ", "TC": "TC", "TD": "TD", "TG": "TG", "TH": "TH", "TJ": "TJ", "TK": "TK", "TL": "TL", "TM": "TM", "TN": "TN", "TO": "TO", "TR": "TR", "TT": "TT", "TV": "TV", "TW": "TW", "TZ": "TZ", "UA": "UA", "UG": "UG", "US": "US", "UY": "UY", "UZ": "UZ", "VA": "VA", "VC": "VC", "VE": "VE", "VG": "VG", "VI": "VI", "VN": "VN", "VU": "VU", "WF": "WF", "WS": "WS", "YE": "YE", "YT": "YT", "ZA": "ZA", "ZM": "ZM", "ZW": "ZW", "NotApplicable": "NotApplicable"}, "LocalizationKeys": {"default": {"AD": "AD", "AE": "AE", "AF": "AF", "AG": "AG", "AI": "AI", "AL": "AL", "AM": "AM", "AN": "AN", "AO": "AO", "AQ": "AQ", "AR": "AR", "AS": "AS", "AT": "AT", "AU": "AU", "AW": "AW", "AZ": "AZ", "BA": "BA", "BB": "BB", "BD": "BD", "BE": "BE", "BF": "BF", "BG": "BG", "BH": "BH", "BI": "BI", "BJ": "BJ", "BM": "BM", "BN": "BN", "BO": "BO", "BR": "BR", "BS": "BS", "BT": "BT", "BV": "BV", "BW": "BW", "BY": "BY", "BZ": "BZ", "CA": "CA", "CC": "CC", "CD": "CD", "CF": "CF", "CG": "CG", "CH": "CH", "CI": "CI", "CK": "CK", "CL": "CL", "CM": "CM", "CN": "CN", "CO": "CO", "CR": "CR", "CV": "CV", "CX": "CX", "CY": "CY", "CZ": "CZ", "DE": "DE", "DJ": "DJ", "DK": "DK", "DM": "DM", "DO": "DO", "DZ": "DZ", "EC": "EC", "EE": "EE", "EG": "EG", "ER": "ER", "ES": "ES", "ET": "ET", "FI": "FI", "FJ": "FJ", "FK": "FK", "FM": "FM", "FO": "FO", "FR": "FR", "GA": "GA", "GB": "GB", "GD": "GD", "GE": "GE", "GF": "GF", "GG": "GG", "GH": "GH", "GI": "GI", "GL": "GL", "GM": "GM", "GN": "GN", "GP": "GP", "GQ": "GQ", "GR": "GR", "GS": "GS", "GT": "GT", "GU": "GU", "GW": "GW", "GY": "GY", "GZ": "GZ", "HK": "HK", "HM": "HM", "HN": "HN", "HR": "HR", "HT": "HT", "HU": "HU", "ID": "ID", "IE": "IE", "IL": "IL", "IN": "IN", "IO": "IO", "IQ": "IQ", "IS": "IS", "IT": "IT", "JE": "JE", "JM": "JM", "JO": "JO", "JP": "JP", "KE": "KE", "KG": "KG", "KH": "KH", "KI": "KI", "KM": "KM", "KN": "KN", "KR": "KR", "KW": "KW", "KY": "KY", "KZ": "KZ", "LA": "LA", "LB": "LB", "LC": "LC", "LI": "LI", "LK": "LK", "LR": "LR", "LS": "LS", "LT": "LT", "LU": "LU", "LV": "LV", "LY": "LY", "MA": "MA", "MC": "MC", "MD": "MD", "ME": "ME", "MG": "MG", "MH": "MH", "MK": "MK", "ML": "ML", "MM": "MM", "MN": "MN", "MO": "MO", "MP": "MP", "MQ": "MQ", "MR": "MR", "MS": "MS", "MT": "MT", "MU": "MU", "MV": "MV", "MW": "MW", "MX": "MX", "MY": "MY", "MZ": "MZ", "NA": "NA", "NC": "NC", "NE": "NE", "NF": "NF", "NG": "NG", "NI": "NI", "NL": "NL", "NO": "NO", "NP": "NP", "NR": "NR", "NU": "NU", "NZ": "NZ", "OM": "OM", "PA": "PA", "PE": "PE", "PF": "PF", "PG": "PG", "PH": "PH", "PK": "PK", "PL": "PL", "PM": "PM", "PN": "PN", "PR": "PR", "PS": "PS", "PT": "PT", "PW": "PW", "PY": "PY", "QA": "QA", "RE": "RE", "RO": "RO", "RS": "RS", "RU": "RU", "RW": "RW", "SA": "SA", "SB": "SB", "SC": "SC", "SD": "SD", "SE": "SE", "SG": "SG", "SH": "SH", "SI": "SI", "SJ": "SJ", "SK": "SK", "SL": "SL", "SM": "SM", "SN": "SN", "SO": "SO", "SR": "SR", "ST": "ST", "SV": "SV", "SZ": "SZ", "TC": "TC", "TD": "TD", "TF": "TF", "TG": "TG", "TH": "TH", "TJ": "TJ", "TK": "TK", "TL": "TL", "TM": "TM", "TN": "TN", "TO": "TO", "TR": "TR", "TT": "TT", "TV": "TV", "TW": "TW", "TZ": "TZ", "UA": "UA", "UG": "UG", "UM": "UM", "US": "US", "UY": "UY", "UZ": "UZ", "VA": "VA", "VC": "VC", "VE": "VE", "VG": "VG", "VI": "VI", "VN": "VN", "VU": "VU", "WF": "WF", "WS": "WS", "YE": "YE", "YT": "YT", "ZA": "ZA", "ZM": "ZM", "ZW": "ZW", "NotApplicable": "NotApplicable"}}}, {"ReportColumnId": "Report.CountryOfSale", "SharedLocalizationColumnId": "Report.Country"}, {"ReportColumnId": "Report.POSCountry", "SharedLocalizationColumnId": "Report.Country"}, {"ReportColumnId": "ReportV12.Language", "SharedLocalizationColumnId": "Report.Language", "IsFilterable": true, "NameToDbValues": {"AA": "AA", "AB": "AB", "AF": "AF", "AM": "AM", "AR": "AR", "AS": "AS", "AY": "AY", "AZ": "AZ", "BA": "BA", "BE": "BE", "BG": "BG", "BH": "BH", "BI": "BI", "BN": "BN", "BO": "BO", "BR": "BR", "CA": "CA", "CO": "CO", "CS": "CS", "CY": "CY", "DA": "DA", "DE": "DE", "DZ": "DZ", "EL": "EL", "EN": "EN", "EO": "EO", "ES": "ES", "ET": "ET", "EU": "EU", "FA": "FA", "FI": "FI", "FJ": "FJ", "FO": "FO", "FR": "FR", "FY": "FY", "GA": "GA", "GD": "GD", "GL": "GL", "GN": "GN", "GU": "GU", "HA": "HA", "HE": "IW", "HI": "HI", "HR": "HR", "HU": "HU", "HY": "HY", "IA": "IA", "IE": "IE", "IK": "IK", "ID": "ID", "IS": "IS", "IT": "IT", "IW": "IW", "JA": "JA", "JI": "JI", "JV": "JV", "KA": "KA", "KK": "KK", "KL": "KL", "KM": "KM", "KN": "KN", "KO": "KO", "KS": "KS", "KU": "KU", "KY": "KY", "LA": "LA", "LN": "LN", "LO": "LO", "LT": "LT", "LV": "LV", "MG": "MG", "MI": "MI", "MK": "MK", "ML": "ML", "MN": "MN", "MO": "MO", "MR": "MR", "MS": "MS", "MT": "MT", "MY": "MY", "NA": "NA", "NE": "NE", "NL": "NL", "NO": "NO", "OC": "OC", "OM": "OM", "OR": "OR", "PA": "PA", "PL": "PL", "PS": "PS", "PT": "PT", "QU": "QU", "RM": "RM", "RN": "RN", "RO": "RO", "RU": "RU", "RW": "RW", "SA": "SA", "SD": "SD", "SG": "SG", "SH": "SH", "SI": "SI", "SK": "SK", "SL": "SL", "SM": "SM", "SN": "SN", "SO": "SO", "SQ": "SQ", "SR": "SR", "SS": "SS", "ST": "ST", "SU": "SU", "SV": "SV", "SW": "SW", "TA": "TA", "TE": "TE", "TG": "TG", "TH": "TH", "TI": "TI", "TK": "TK", "TL": "TL", "TN": "TN", "TO": "TO", "TR": "TR", "TS": "TS", "TT": "TT", "TW": "TW", "UK": "UK", "UR": "UR", "UZ": "UZ", "VI": "VI", "VO": "VO", "WO": "WO", "XH": "XH", "YO": "YO", "ZH": "ZH", "ZU": "ZU", "ZH-HANS": "ZH-HANS", "ZH-HANT": "ZH-HANT"}}, {"ReportColumnId": "Report.OfferLanguage", "SharedLocalizationColumnId": "Report.Language"}, {"ReportColumnId": "BSCReport.TopVsOther", "LocalizationKeys": {"default": {"0": "Unknown", "10": "BingYahooSearchTop", "11": "BingYahooSearchTop", "12": "BingYahooSearchOther", "21": "SyndicatedSearchPartnersTop", "22": "SyndicatedSearchPartnersOther", "31": "AOLSearchTop", "32": "AOLSearchOther", "40": "Content", "50": "Audience", "51": "AudienceTop", "52": "AudienceOther", "61": "InHouseTop", "62": "InHouseOther", "71": "MicrosoftSitesAndSelectTrafficTop", "72": "MicrosoftSitesAndSelectTrafficOther", "254": "Cross-network"}}}, {"ReportColumnId": "Report.AgeGroup", "LocalizationKeys": {"default": {"0": "Unknown", "1": "0_12", "2": "13_17", "3": "18_24", "4": "25_34", "5": "35_49", "6": "50_64", "7": "65+", "99": "0_17"}}}, {"ReportColumnId": "Report.FeedUrl", "LocalizationKeys": {"default": {"0": "False", "1": "True"}}}, {"ReportColumnId": "Report.Gender", "LocalizationKeys": {"default": {"0": "Unknown", "1": "Male", "2": "Female"}}}, {"ReportColumnId": "Report.ConflictLevel", "LocalizationKeys": {"default": {"1": "Account", "2": "Campaign", "3": "AdGroup"}}}, {"ReportColumnId": "Report.NegativeKeywordMatchType", "LocalizationKeys": {"default": {"0": "Unknown", "1": "Exact", "2": "Phrase", "3": "<PERSON>", "5": "Content", "9": "Predictive", "127": "NotApplicable"}}}, {"ReportColumnId": "Report.ConflictType", "LocalizationKeys": {"default": {"0": "Possible", "1": "True"}}}, {"ReportColumnId": "Report.ItemChanged", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Account", "2": "Campaign", "3": "AdGroup", "4": "Ad", "5": "Keyword", "13": "SitelinkExtension", "14": "LocationExtension", "15": "CallExtension", "16": "ProductionExtension", "17": "NegativeKeywordList", "18": "CalloutExtension", "19": "ReviewExtension", "20": "Budget", "21": "Label", "22": "StructuredSnippetExtension", "23": "AutoTarget", "24": "Exclusion", "25": "AppAdExtension", "26": "ActionExtension", "27": "ProductGroup", "28": "PriceExtension", "29": "AutomatedExtensionsReport", "30": "Language", "31": "Feed", "32": "FeedAttribute", "33": "FeedItem", "34": "RemarketingListTargetingAssociation", "35": "RemarketingListExclusionAssociation", "36": "CustomAudienceTargetingAssociation", "37": "CustomAudienceExclusionAssociation", "38": "InmarketAudienceTargetingAssociation", "39": "InmarketAudienceExclusionAssociation", "40": "ProductAudienceTargetingAssociation", "41": "ProductAudienceExclusionAssociation", "42": "SimilarToRemarketingListTargetingAssociation", "43": "SimilarToRemarketingListExclusionAssociation", "44": "CustomerListTargetingAssociation", "45": "CustomerListExclusionAssociation", "46": "ResponsiveSearchAd", "47": "Category", "48": "ProductOrService", "59": "DynamicDataExtension", "60": "CombinedListTargetingAssociation", "61": "CombinedListExclusionAssociation", "62": "PromotionAdExtension", "63": "FilterLinkAdExtension", "64": "FlyerAdExtension", "65": "VideoAdExtension", "66": "LabelMcc", "67": "PortfolioBidStrategy", "68": "SeasonalityAdjustment", "69": "AdCustomizerAttributeAccountValue", "70": "AdCustomizerAttributeCampaignValue", "71": "AdCustomizerAttributeAdGroupValue", "72": "AdCustomizerAttributeKeywordValue", "73": "DataExclusion", "74": "CampaignConversionGoal", "75": "AssetGroupListingGroup", "76": "AssetGroup", "77": "AudienceSignal", "78": "ImageExtension", "79": "ImpressionBasedRemarketingTargetingAssociation", "80": "ImpressionBasedRemarketingExclusionAssociation", "81": "LogoExtension", "83": "PropertyGroup", "84": "SiteExclusionList", "85": "SiteInclusionList"}}, "NameToDbValues": {"Account": "1", "Campaign": "2", "AdGroup": "3", "Ad": "4", "Keyword": "5"}}, {"ReportColumnId": "Report.How<PERSON><PERSON>ed", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Added", "2": "Removed", "3": "Changed"}}, "NameToDbValues": {"Added": "1", "Deleted": "2", "Changed": "3"}}, {"ReportColumnId": "Report.Tool", "LocalizationKeys": {"default": {"1": "Other", "2": "Other", "3": "Other", "4": "Import", "5": "WebClient", "6": "Other", "7": "WebClient", "8": "Other", "9": "Other", "10": "Other", "11": "MicrosoftAdvertisingAPI", "12": "MicrosoftAdvertisingAPI", "13": "MicrosoftAdvertisingAPI", "14": "Other", "15": "Other", "16": "Other", "17": "MicrosoftAdvertisingEditor", "18": "Other", "19": "MicrosoftAdvertisingEditor", "20": "MicrosoftAdvertisingAPI", "21": "MicrosoftAdvertisingAPI", "22": "BulkUpload", "23": "WebClient", "24": "WebClient", "25": "AutomatedRules", "26": "Other", "27": "AutomatedRules", "28": "MicrosoftAdvertisingApp", "29": "Other", "30": "Other", "31": "MicrosoftAdvertisingAPI", "32": "WebClient", "33": "WebClient", "34": "WebClient", "35": "MicrosoftAdvertisingEditor", "36": "Other", "37": "Import", "38": "MicrosoftAdvertisingAPI", "39": "Other", "40": "Other", "41": "Other", "42": "MicrosoftAdvertisingScripts", "43": "MicrosoftAdvertisingAPI", "44": "MicrosoftAdvertisingAPI", "45": "MicrosoftAdvertisingEditor", "46": "MicrosoftAdvertisingEditor", "47": "WebClient", "48": "MicrosoftAdvertisingAPI", "49": "MicrosoftAdsMobileApp", "54": "AdInsightMT", "53": "WebClient", "58": "Import", "60": "Other", "61": "Import", "64": "WebClient", "66": "Other"}}}, {"ReportColumnId": "ReportPreview.Tool", "LocalizationKeys": {"default": {"1": "Other", "2": "Other", "3": "Other", "4": "Import", "5": "WebClient", "6": "Other", "7": "WebClient", "8": "Other", "9": "Other", "10": "Other", "11": "MicrosoftAdvertisingAPI", "12": "MicrosoftAdvertisingAPI", "13": "MicrosoftAdvertisingAPI", "14": "Other", "15": "Other", "16": "Other", "17": "MicrosoftAdvertisingEditor", "18": "Other", "19": "MicrosoftAdvertisingEditor", "20": "MicrosoftAdvertisingAPI", "21": "MicrosoftAdvertisingAPI", "22": "BulkUpload", "23": "WebClient", "24": "WebClient", "25": "AutomatedRules", "26": "Other", "27": "AutomatedRules", "28": "MicrosoftAdvertisingApp", "29": "Other", "30": "Other", "31": "MicrosoftAdvertisingAPI", "32": "WebClient", "33": "WebClient", "34": "WebClient", "35": "MicrosoftAdvertisingEditor", "36": "Other", "37": "Import", "38": "MicrosoftAdvertisingAPI", "39": "Other", "40": "Other", "41": "Other", "42": "MicrosoftAdvertisingScripts", "43": "MicrosoftAdvertisingAPI", "44": "MicrosoftAdvertisingAPI", "45": "MicrosoftAdvertisingEditor", "46": "MicrosoftAdvertisingEditor", "47": "WebClient", "48": "MicrosoftAdvertisingAPI", "49": "MicrosoftAdsMobileApp", "54": "AdInsightMT", "53": "WebClient", "58": "Import", "60": "Other", "61": "Import", "64": "WebClient", "66": "Other"}}}, {"ReportColumnId": "Report.AttributeChanged", "LocalizationKeys": {"default": {"0": "None", "2": "Account<PERSON><PERSON>", "164": "AccountScope", "6": "AdText", "12": "AdTitle", "13": "AdType", "155": "Age", "16": "AgencyContact", "129": "BillToCustomer", "26": "BroadMatchBid", "27": "Monthly<PERSON>udge<PERSON>", "28": "BudgetType", "32": "CampaignName", "34": "Code", "38": "ContentMatchBid", "163": "CustomerScope", "156": "Day", "47": "DefaultBroadMatchBid", "48": "DefaultContentMatchBid", "49": "DefaultExactMatchBid", "50": "DefaultPhraseMatchBid", "51": "DestinationURL", "158": "<PERSON><PERSON>", "52": "DisplayURL", "53": "EditorialStatus", "57": "Email", "58": "EndDate", "59": "ExactMatchBid", "60": "FinancialStatus", "61": "FirstName", "154": "Gender", "157": "Hour", "63": "DailyBudget", "161": "IPExclusion", "70": "AdGroupName", "73": "LastName", "75": "Status", "159": "Location", "160": "LocationExclusion", "77": "LockStatus", "147": "Medium", "81": "NegativeKeyword", "144": "NetworkOption", "98": "PauseStatus", "99": "PaymentOption", "101": "PhraseMatchBid", "103": "PreferredBillToPaymentMethod", "105": "PreferredLanguage", "106": "PreferredUserName", "115": "SalesHouseCustomer", "153": "SiteExclusion", "118": "SoldToPaymentMethod", "131": "LimitAmount", "120": "StartDate", "127": "UserName", "152": "UnicodeDestinationURL", "162": "UserRole", "302": "ShortHeadline", "303": "LongHeadline", "304": "PrivacyStatus", "305": "AgeTargetSetting", "306": "AudienceTargetSetting", "307": "CompanyTargetSetting", "308": "GenderTargetSetting", "309": "JobFunctionTargetSetting", "310": "IndustryTargetSetting", "315": "RemarketingList", "316": "RemarketingListExclusion", "317": "CustomAudience", "318": "CustomAudienceExclusion", "319": "InMarketAudience", "320": "InMarketAudienceExclusion", "321": "ProductAudience", "322": "ProductAudienceExclusion", "323": "CompanyTargeting", "324": "CompanyExclusion", "325": "IndustryTargeting", "326": "IndustryExclusion", "327": "JobFunctionTargeting", "328": "JobFunctionExclusion", "362": "AgeExclusion", "363": "GenderExclusion", "311": "1_91_1Image", "312": "1_1Image", "313": "4_1Image", "314": "1_1Image", "370": "1_69_1Image", "371": "0_93_1Image", "372": "1_5_1Image", "373": "1_55_1Image", "374": "1_33_1Image", "375": "1_78_1Image", "376": "1_72_1Image", "165": "DisplayText", "166": "DestinationURL", "167": "Country_region", "168": "BusinessName", "169": "BusinessPhone", "170": "AddressLine1", "171": "AddressLine2", "172": "City", "173": "StateOrProvince", "174": "ZIPCode", "175": "MapIcon", "176": "BusinessImage", "177": "Latitude", "178": "Longitude", "179": "ZIPOrPostalCode", "180": "PhoneNumber", "181": "Country_region", "182": "ShowWebsiteOrPhone", "183": "AdExtensionID", "184": "AdExtensionItemID", "185": "AdExtensionPropertyID", "186": "AdExtensionPropertyValue", "187": "Association", "92": "Param1", "93": "Param2", "94": "Param3", "188": "AdvancedLocationTargeting", "189": "MatchType", "190": "TargetRadius", "191": "All", "192": "Brand", "193": "Condition", "194": "ProductType", "195": "BingAdsLabel", "196": "BingAdsGrouping", "197": "SKU", "198": "ID", "199": "<PERSON><PERSON><PERSON><PERSON>", "200": "TargetedDays", "201": "StartTime", "202": "EndTime", "205": "NegativeKeywordListName", "206": "AppExtensionOS", "207": "AppStoreId", "208": "DisplayText", "209": "DestinationURL", "210": "DevicePreference", "221": "Distance", "222": "Bid", "223": "CampaignPriority", "227": "ProductFilter", "231": "ProductGroup", "239": "NativeBidAdjustment", "240": "TrackingTemplate", "242": "FinalURL", "243": "MobileURL", "378": "FinalURLSuffix", "379": "CustomParameters", "254": "CalloutText", "255": "IsExact", "256": "Text", "257": "Source", "258": "Url", "259": "BidStrategyType", "264": "EditorialStatus", "261": "BudgetName", "262": "DailyBudgetAmount", "269": "StructuredSnippetCategory", "270": "StructuredSnippetText1", "271": "StructuredSnippetText2", "272": "StructuredSnippetText3", "273": "StructuredSnippetText4", "274": "StructuredSnippetText5", "275": "StructuredSnippetText6", "276": "StructuredSnippetText7", "277": "StructuredSnippetText8", "278": "StructuredSnippetText9", "279": "StructuredSnippetText10", "266": "TitlePart1", "267": "TitlePart2", "333": "TitlePart3", "334": "AdText2", "268": "Path1", "280": "Path2", "265": "BudgetPauseType", "380": "Headline", "381": "Description", "382": "AdAssetPinType", "281": "StartDate", "282": "EndDate", "283": "TimeZone", "285": "DefaultContentBid", "286": "DefaultSearchBid", "287": "TargetCPA", "288": "Header", "289": "Price", "290": "FinalURL1", "291": "FinalURL2", "292": "FinalURL3", "293": "FinalURL4", "294": "FinalURL5", "295": "FinalURL6", "296": "FinalURL7", "297": "FinalURL8", "298": "FinalURL9", "299": "FinalURL10", "300": "MaximumCPC", "301": "AutomatedExtensionsReport", "71": "Language", "332": "TargetingSource", "364": "BidAdjustment", "365": "Status", "366": "Language", "367": "ActionText", "368": "ActionURL", "335": "Unknown", "336": "Int64", "337": "Float", "338": "String", "339": "Boolean", "340": "Url", "341": "DateTime", "342": "Int64List", "343": "FloatList", "344": "StringList", "345": "BooleanList", "346": "UrlList", "347": "DateTimeList", "348": "Price", "350": "DevicePreference", "351": "TargetKeyword", "352": "TargetKeywordText", "353": "TargetKeywordMatchType", "354": "TargetAdGroup", "355": "TargetCampaign", "356": "TargetLocation", "357": "TargetLocationRestriction", "358": "TargetAudienceId", "360": "Attribute", "361": "Scheduling", "383": "AutoApplyAdsByBing", "384": "ExperimentSplit", "385": "ExperimentStatus", "490": "FeedId", "491": "FeedType", "492": "Name", "493": "TargetRoas", "386": "Goal", "387": "BusinessId", "494": "AdScheduleUseSearcherTimeZone", "495": "PromotionItem", "496": "DiscountModifier", "497": "<PERSON><PERSON><PERSON><PERSON>", "498": "MoneyAmountOff", "499": "PromotionCode", "500": "OrdersOverAmount", "501": "Occasion", "502": "PromotionStartDate", "503": "PromotionEndDate", "504": "Language", "505": "CurrencyCode", "506": "MobileFinalUrl1", "507": "MobileFinalUrl2", "508": "MobileFinalUrl3", "509": "MobileFinalUrl4", "510": "MobileFinalUrl5", "511": "MobileFinalUrl6", "512": "MobileFinalUrl7", "513": "MobileFinalUrl8", "514": "MobileFinalUrl9", "515": "MobileFinalUrl10", "516": "Header", "517": "Text", "518": "FinalURL", "519": "MobileURL", "520": "FlyerName", "521": "StoreId", "522": "Description", "523": "ThumbnailIds", "524": "16_9_Video", "525": "5_4_Video", "526": "1_1_Video", "527": "4_5_Video", "528": "9_16_Video", "529": "ThumbnailUrl", "530": "CpvBid", "531": "CpmBid", "532": "ImpressionTrackingUrl", "533": "VideoName", "534": "DisplayText", "535": "AltText", "536": "ActionText", "537": "VideoId", "538": "TargetAdPosition", "539": "TargetImpressionShare", "540": "16_9_VideoThumbnail", "541": "5_4_VideoThumbnail", "542": "1_1_VideoThumbnail", "543": "4_5_VideoThumbnail", "544": "9_16_VideoThumbnail", "545": "OwnerCustomerId", "546": "BidStrategyName", "547": "DynamicDescriptionEnabled", "548": "BusinessAttributes", "549": "MMABidAdjustment", "553": "MultimediaAdDescription", "554": "SeasonalityAdjustmentName", "555": "SeasonalityAdjustmentDescription", "556": "ConversionRateAdjustmentPct", "557": "DeviceTypesFilter", "558": "CampaignTypesFilter", "559": "AssociatedCampaign", "560": "CallToAction", "561": "AttributeValuesJSON", "562": "PredictiveTargeting", "563": "FinalURLExpansion", "564": "AudienceSignal", "566": "Interests", "567": "FirstPartyData", "569": "DemographicsAge", "570": "DemographicsGender", "571": "AssetGroupName", "572": "AssetGroupAudienceAssociation", "29": "BusinessName", "402": "CallToAction", "233": "IsExcluded", "573": "CampaignType", "574": "UpgradeStatus", "575": "ThirdPartyMeasurementSetting", "576": "AutoGeneratedTextOptOut", "577": "AutoGeneratedImageOptOut", "578": "TargetCostPerSale", "579": "HotelAdGroupType", "580": "ImageExtensionImages", "581": "Placement", "582": "PercentMaxCpc", "583": "AdvancedBookingWindow", "584": "CheckInDay", "585": "LengthOfStay", "586": "CheckInDate", "369": "ImageUrl", "588": "DomainName", "589": "PercentBid", "590": "NewCustomerAcquisitionAdditionalValue", "591": "NewCustomerAcquisitionExistingCustomerSegment", "592": "NewCustomerAcquisitionBidMode", "593": "OverrideAdditionalValue", "594": "UseAccountLevelAdditionalValue", "598": "AssetGroupSearchTheme", "599": "ManualCPI", "600": "ManualCPC", "601": "BrandListName", "610": "SiteExclusionListName", "611": "SiteInclusionListName", "613": "SmallImage", "614": "LargeImage", "615": "AppLogo", "616": "Topic"}}}, {"ReportColumnId": "Report.EditorialStatusId", "LocalizationKeys": {"default": {"0": "Unknown", "1": "Eligible", "2": "ApprovedLimited", "3": "Disapproved", "4": "PendingEditorialReview"}}}, {"ReportColumnId": "Report.StatusId", "LocalizationKeys": {"default": {"0": "Unknown", "1": "<PERSON>", "2": "Exact", "3": "Phrase", "10": "Draft", "11": "Active", "12": "Inactive", "13": "Deleted", "14": "ProposalsOnly", "15": "PendingCreditCheck", "16": "NoHold", "17": "SoldToOnly", "18": "CreditHold", "19": "CreditWarning", "20": "Locked", "21": "NoLock", "28": "LockedFORUpdate", "29": "PendingValidation", "30": "Draft", "31": "Active", "32": "Inactive", "33": "Deleted", "34": "Proposed", "35": "PendingCreditCheck", "36": "NoHold", "37": "SoldToOnly", "38": "CreditWarning", "39": "Hold", "40": "Locked", "41": "NoLock", "42": "Pause", "50": "Active", "51": "Inactive", "52": "Deleted", "60": "Approved", "61": "Processed", "62": "ProcessingFailed", "63": "Cancelled", "64": "Paused", "65": "NotPaused", "70": "Inactive", "71": "Active", "73": "Rejected", "75": "Deleted", "90": "Active", "91": "Deleted", "100": "New", "101": "Active", "102": "Inactive", "103": "Deleted", "104": "Suspended", "108": "Active", "109": "Draft", "110": "Deleted", "112": "ExperimentApplied", "114": "ExperimentApplied", "120": "Completed", "121": "Active", "122": "Cancelled", "123": "Deleted", "124": "Paused", "125": "NotPaused", "126": "BudgetPaused", "127": "BudgetAndUserPaused", "128": "Incomplete", "130": "Started", "131": "Failed", "132": "EndedWithErrors", "133": "Ended", "137": "Pending", "138": "Active", "139": "TargetingTooNarrow", "140": "Draft", "141": "Active", "142": "Cancelled", "143": "Deleted", "144": "Paused", "145": "Active", "147": "BudgetPaused", "149": "Reserved", "150": "Expired", "161": "Active", "162": "Paused", "163": "Deleted", "164": "Paused", "165": "NotPaused", "181": "Submitted", "185": "Deleted", "201": "Pending", "202": "PendingManual<PERSON><PERSON>roval", "203": "Approved", "204": "Rejected", "205": "Deleted", "206": "InUseByEditor", "208": "ManuallyApproved", "211": "Rejected", "212": "AbandonedInactive", "213": "PendingInactive", "214": "Active", "215": "PendingActive", "216": "AbandonedActive", "217": "ActiveWithFlags", "221": "Active", "222": "Deleted", "223": "WriteOff", "234": "Pause", "253": "TaxOnHold", "254": "UserHold"}}}, {"ReportColumnId": "Report.BudgetType", "LocalizationKeys": {"default": {"0": "SharedBudget", "1": "Monthly_Smoothed", "2": "Monthly_Open", "5": "DailyStandard", "6": "DailyAccelerated", "7": "LifetimeStandard"}}}, {"ReportColumnId": "Report.BudgetPauseType", "LocalizationKeys": {"default": {"0": "NotPaused", "2": "DailyPaused", "4": "MonthlyPaused", "8": "LifetimePaused"}}}, {"ReportColumnId": "Report.BiddingScheme", "LocalizationKeys": {"default": {"0": "InheritFromParent", "1": "ManualCpc", "2": "MaxClicks", "3": "MaxConversions", "4": "TargetCpa", "5": "EnhancedCpc", "6": "TargetRoas", "8": "MaxConversionValue", "9": "TargetImpressionShare", "10": "PortfolioBidStrategy", "11": "ManualCpv", "12": "ManualCpm", "13": "PercentCpc", "14": "Commission", "15": "CostPerSale", "16": "ManualCpa"}}}, {"ReportColumnId": "Report.DayName", "LocalizationKeys": {"default": {"0": "Unknown", "1": "Sunday", "2": "Monday", "3": "Tuesday", "4": "Wednesday", "5": "Thursday", "6": "Friday", "7": "Saturday"}}}, {"ReportColumnId": "Report.TimePeriod-DayOfWeek", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "0", "1": "1", "2": "2", "3": "3", "4": "4", "5": "5", "6": "6", "7": "7"}, "ReportUIDisplay": {"0": "Unknown", "1": "Sunday", "2": "Monday", "3": "Tuesday", "4": "Wednesday", "5": "Thursday", "6": "Friday", "7": "Saturday"}}}, {"ReportColumnId": "Report.PreferredLanguageId", "LocalizationKeys": {"default": {"0": "usingTheCampaignSettings", "1": "<PERSON><PERSON><PERSON>", "10": "Aymara", "100": "<PERSON><PERSON><PERSON><PERSON>", "101": "Setswana", "102": "<PERSON><PERSON><PERSON>", "103": "Sindhi", "104": "<PERSON><PERSON><PERSON>", "105": "<PERSON><PERSON><PERSON>", "106": "Slovak", "107": "Slovenian", "108": "Somali", "109": "Spanish", "11": "Azerbaijani", "110": "Spanish", "111": "Sundanese", "112": "Swedish", "113": "Tagalog", "114": "Tajik", "115": "Tamil", "116": "Tatar", "117": "Telegu", "118": "Thai", "119": "Tibetan", "12": "Bashkir", "120": "<PERSON><PERSON><PERSON><PERSON>", "121": "Tonga_Nyasa", "122": "Tsonga", "123": "Turkish", "124": "Turkmen", "125": "Twi", "126": "Ukrainian", "127": "Urdu", "128": "Uzbek", "129": "Vietnamese", "13": "Basque", "130": "Volapuk", "131": "Welsh", "132": "<PERSON><PERSON><PERSON>", "133": "Xhosa", "134": "Yiddish", "135": "Yoruba", "136": "Zulu", "137": "SimplifiedChinese", "14": "Bengali", "15": "Bhutani", "16": "Bihari", "17": "B<PERSON>lama", "18": "Breton", "19": "Bulgarian", "2": "Abkhazian", "20": "Burmese", "21": "Byelorussian", "22": "Cambodian", "23": "Catalan", "24": "TraditionalChinese", "25": "Corsican", "26": "Croatian", "27": "Czech", "28": "Danish", "29": "Dutch", "3": "Afar", "30": "English", "31": "Esperanto", "32": "Estonian", "33": "Faeroese", "34": "Fiji", "35": "Finnish", "36": "French", "37": "Frisian", "38": "Galician", "39": "Georgian", "4": "Afrikaans", "40": "German", "41": "Greek", "42": "Greenlandic", "43": "Guarani", "44": "Gujarati", "45": "Hausa", "46": "Hebrew", "47": "Hindi", "48": "Hungarian", "49": "Icelandic", "5": "Albanian", "50": "Indonesian", "51": "Interlingua", "52": "Interlingue", "53": "Inupiak", "54": "Irish", "55": "Italian", "56": "Japanese", "57": "Javanese", "58": "Kannada", "6": "Amharic", "60": "Kazakh", "61": "Kinyarwanda", "62": "Kirghiz", "63": "<PERSON><PERSON><PERSON>", "64": "Korean", "66": "<PERSON><PERSON><PERSON>", "67": "Latin", "68": "Latvian", "69": "Lingala", "7": "Arabic", "70": "Lithuanian", "71": "Macedonian_FYROM", "72": "Malagasy", "73": "<PERSON><PERSON>", "74": "Maylayalam", "75": "Maltese", "76": "<PERSON><PERSON>", "77": "Marathi", "78": "Moldavian", "79": "Mongolian", "8": "Armenian", "80": "Nauru", "81": "Nepali", "82": "Norwegian", "83": "Occitan", "84": "Oriya", "85": "Pashto", "86": "Persian", "87": "Polish", "88": "Portuguese", "89": "Punjabi", "9": "Assamese", "90": "Quechua", "91": "RhaetoRomance", "92": "Romainian", "93": "Russian", "94": "Samoan", "95": "<PERSON><PERSON>", "96": "Sanskrit", "97": "ScotsGaelic", "98": "Serbian", "99": "SerboCroatian", "138": "Bosnian"}}}, {"ReportColumnId": "Report.HourBucket", "LocalizationKeys": {"default": {"0": "Unknown", "1": "3AMTo7AM", "2": "7AMTo11AM", "3": "11AMTo2PM", "4": "2PMTo6PM", "5": "6PMTo11PM", "6": "11PMTo3AM"}}}, {"ReportColumnId": "Report.UseSearcherTimeZoneId", "LocalizationKeys": {"default": {"0": "locationOfAccount", "1": "locationOfPersonViewingTheAd"}}}, {"ReportColumnId": "Report.DevicePreferenceId", "LocalizationKeys": {"default": {"0": "all", "2": "mobile"}}}, {"ReportColumnId": "Report.AdvancedLocationTargeting", "LocalizationKeys": {"default": {"0": "PhysicalLocationOrSearchIntent", "1": "PhysicalLocation", "2": "SearchIntent"}}}, {"ReportColumnId": "Report.TargetingSource", "LocalizationKeys": {"default": {"0": "BingIndexAndPageFeeds", "1": "BingIndex", "2": "PageFeeds"}}}, {"ReportColumnId": "Report.SearchNetworkOption", "LocalizationKeys": {"default": {"0": "AllSearchNetworks", "1": "Bing_AOL_AndYahooSearchOnly", "2": "Bing_AOL_AndYahooSyndicatedSearchPartnersOnly", "3": "InHouse"}}}, {"ReportColumnId": "Report.AdAssetPinType", "LocalizationKeys": {"default": {"0": "None", "1": "Headline1", "2": "Headline2", "3": "Headline3", "4": "Description1", "5": "Description2"}}}, {"ReportColumnId": "Report.DistanceUnit", "LocalizationKeys": {"default": {"0": "<PERSON>", "1": "KM"}}}, {"ReportColumnId": "Report.<PERSON>ce", "LocalizationKeys": {"default": {"0": "Unknown", "1": "DesktopsLaptops", "2": "Smartphones", "4": "Tablets", "8": "DesktopsLaptopsTablets", "16": "CTV", "101": "DesktopsLaptopsOther", "102": "DesktopsLaptopsWindows8", "201": "SmartphoneOther", "202": "SmartphoneWindows", "203": "SmartphoneiOS", "204": "SmartphoneAndroid", "205": "SmartphoneBlackBerry", "206": "SmartphoneWebOS", "401": "TabletOther", "403": "TabletiOS", "404": "TabletAndroid", "405": "TabletBlackBerry", "406": "TabletWebOS", "407": "TabletWindows8"}}}, {"ReportColumnId": "Report.DeviceTypesFilter", "LocalizationKeys": {"default": {"0": "None", "1": "Computers", "2": "Smartphones", "3": "ComputersSmartphones", "4": "Tablets", "5": "ComputersTablets", "6": "SmartphonesTablets", "7": "ComputersSmartphonesTablets"}}}, {"ReportColumnId": "Report.CampaignTypesFilter", "LocalizationKeys": {"default": {"1": "Search", "2": "Shopping", "3": "SearchShopping", "8": "Audience", "9": "SearchAudience", "10": "ShoppingAudience", "11": "SearchShoppingAudience", "64": "Performancemax", "65": "SearchPerformancemax", "66": "ShoppingPerformancemax", "67": "SearchShoppingPerformancemax", "72": "AudiencePerformancemax", "73": "SearchAudiencePerformancemax", "74": "ShoppingAudiencePerformancemax", "75": "SearchShoppingAudiencePerformancemax"}}}, {"ReportColumnId": "Report.AssociationLevel", "LocalizationKeys": {"default": {"0": "Campaign", "1": "AdGroup"}}}, {"ReportColumnId": "Report.FeedType", "LocalizationKeys": {"default": {"0": "None", "1": "PageFeed", "2": "AdCustomizerFeed", "3": "DynamicDataToursAndActivitiesFeed", "4": "DynamicDataAutosListingFeed", "5": "DynamicDataAutosAggregateFeed", "6": "DynamicDataHotelsAndVacationRentalsFeed", "7": "DynamicDataEventsFeed", "8": "DynamicDataRealEstateListingFeed", "9": "DynamicDataRealEstateAggregateFeed"}}}, {"ReportColumnId": "Report.AdScheduleUseSearcherTimeZone", "LocalizationKeys": {"default": {"False": "AccountTimeZone", "True": "AdViewerTimeZone", "0": "AccountTimeZone", "1": "AdViewerTimeZone"}}}, {"ReportColumnId": "Report.AdGroupType", "LocalizationKeys": {"default": {"0": "Undefined", "1": "SearchStandard", "4": "SearchDynamic", "8": "HotelAds"}}}, {"ReportColumnId": "Report.AdGroupTypePreview", "LocalizationKeys": {"default": {"0": "Undefined", "1": "SearchStandard", "4": "SearchDynamic", "8": "HotelAds"}}}, {"ReportColumnId": "Report.AdScenarioType", "LocalizationKeys": {"default": {"0": "None", "6": "TourAndActivity", "7": "Auto", "9": "CreditCard", "10": "HealthInsurance", "11": "InsuranceService", "12": "Cruise", "13": "RealEstateService", "14": "FinancialAdvisor", "15": "TaxService", "16": "DoctorClinic", "17": "MortgageLender", "18": "DebitCard", "19": "HomeService", "20": "JobListing", "21": "LegalService"}}}, {"ReportColumnId": "Report.VerticalAdType", "LocalizationKeys": {"default": {"0": "None", "6": "TourAndActivity", "7": "Auto", "9": "CreditCard", "10": "HealthInsurance", "11": "InsuranceService", "12": "Cruise", "13": "RealEstateService", "14": "FinancialAdvisor", "15": "TaxService", "16": "DoctorClinic", "17": "MortgageLender", "18": "DebitCard", "19": "HomeService", "20": "JobListing", "21": "LegalService"}}}, {"ReportColumnId": "Report.BusinessAttributes", "LocalizationKeys": {"default": {"1": "Vegan", "2": "LGBTQIFriendly", "3": "Unisex", "4": "AllergyFriendly", "5": "<PERSON><PERSON><PERSON><PERSON>", "6": "FamilyFriendly", "7": "<PERSON><PERSON>", "8": "<PERSON><PERSON>", "9": "AlcoholFree", "10": "GlutenFree", "11": "Vegetarian", "12": "EcoFriendly", "13": "CarbonNeutral", "14": "Sustainable", "15": "CarbonNegative", "16": "CrueltyFree", "17": "Nonprofit", "18": "SupportACure", "19": "LocalBusiness", "20": "SmallBusiness", "21": "FamilyOwned", "22": "MinorityOwned", "23": "BlackOwned", "24": "SupportDiseaseResearch", "25": "LGBTQIOwned", "26": "WheelchairAccessible", "27": "VisualAssistance", "28": "HearingAssistance", "29": "MobilityAssistance", "30": "Touchless<PERSON><PERSON><PERSON>", "31": "NoContactDelivery", "32": "WebAccessibility", "33": "DiabetesFriendly", "34": "LatinxOwned", "35": "WomenOwned", "36": "AsianOwned", "37": "VeteranOwned", "38": "EthnicMinorityOwned"}}}, {"ReportColumnId": "Report.AssetGroupStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3"}}, {"ReportColumnId": "ReportComputed.AccountStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "4": "Inactive"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Inactive": "4"}}, {"ReportColumnId": "ReportComputed.CampaignStatus", "IsFilterable": true, "LocalizationKeys": {"default": {"1": "Active", "2": "Paused", "3": "Deleted", "5": "BudgetPaused", "8": "Suspended"}}, "NameToDbValues": {"Active": "1", "Paused": "2", "Deleted": "3", "BudgetPaused": "5", "Suspended": "8"}}, {"ReportColumnId": "ReportComputed.CampaignType", "LocalizationKeys": {"default": {"1": "SearchAndContent", "3": "Shopping", "4": "DynamicSearch", "6": "Audience", "7": "Smart", "8": "Hotel", "9": "PerformanceMax", "10": "App", "11": "LinkedIn"}}}, {"ReportColumnId": "Report.FinalURLExpansion", "LocalizationKeys": {"default": {"0": "On", "1": "Off"}}}, {"ReportColumnId": "Report.IsExcluded", "LocalizationKeys": {"default": {"True": "Exclude", "False": "Include", "1": "Exclude", "0": "Include"}}}, {"ReportColumnId": "Report.SiteType", "LocalizationKeys": {"default": {"0": "LocalUniversal", "1": "MapResults", "2": "PlacePage", "3": "ContentAds", "4": "PropertyPromotionAd"}}}, {"ReportColumnId": "Report.UpgradeStatus", "LocalizationKeys": {"default": {"0": "UpgradingToPerformanceMax", "1": "UpgradedToPerformanceMax", "2": "UpgradeFailed"}}}, {"ReportColumnId": "Report.AdStrength", "LocalizationKeys": {"default": {"Unrated": "Unrated", "Poor": "Poor", "Average": "Average", "Good": "Good", "Excellent": "Excellent", "Pending": "Pending"}}}, {"ReportColumnId": "Report.PlacementName", "LocalizationKeys": {"default": {"0": "NA", "1": "WindowsStartPage", "11726288": "NTPRivercard", "11728888": "NTPCoachmark", "11730596": "WindowsActionCenter", "11730597": "WindowsStartMenu", "11730598": "WindowsSpotlight", "11730599": "AMCDiscoverCard", "11730607": "DONOTSELECT-NTPBanner", "11730608": "Prong1(Win10)", "11730609": "Prong2(Win11)", "198842086": "DONOTSELECT-EdgeMarketingBanner", "344067104": "SurfaceContentHubBanner", "621253890": "WindowsContentHubBanner", "746048347": "WindowsMiniSpotlight", "791969536": "EdgeContentHubBanner", "904629447": "DONOTSELECT-Microsoft.comHomepage4XCards", "1276394720": "CopilotContentHubBanner", "1338830458": "DONOTSELECT-Microsoft.comHomepageHeroCarousel", "1512776859": "DONOTSELECT-MicrosoftOfficeDesktopAppsRightRail", "2019993226": "EdgeWelcomeGetStarted", "2093892908": "DONOTSELECT-StartRecommended", "2142591306": "MicrosoftPlus"}}}, {"ReportColumnId": "Report.AssetType", "LocalizationKeys": {"default": {"1": "Image", "2": "Headline", "3": "Description", "4": "Video", "5": "VideoThumbnail", "6": "CallToAction", "7": "CashbackPercentage", "8": "CashbackAmount", "9": "URL", "10": "ReviewRating", "11": "Callout", "12": "Hotspots", "13": "BusinessName", "14": "Subtitle"}}}, {"ReportColumnId": "Report.AssetSource", "LocalizationKeys": {"default": {"1": "Autogenerated", "2": "AdvertiserProvided"}}}, {"ReportColumnId": "Report.NewCustomerAcquisitionBidMode", "LocalizationKeys": {"default": {"0": "BidHigherForNewCustomer", "1": "BidOnlyForNewCustomer"}}}, {"ReportColumnId": "Report.UseAccountLevelAdditionalValue", "LocalizationKeys": {"default": {"0": "False", "1": "True"}}}, {"ReportColumnId": "Report.L1Vertical", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Other", "10001": "Vehicles", "10002": "FamilyCommunity", "10003": "RealEstate", "10004": "BusinessIndustrial", "10005": "BeautyPersonalCare", "10006": "OccasionsGifts", "10007": "InternetTelecom", "10009": "HomeGarden", "10010": "FoodGroceries", "10011": "Health", "10012": "Finance", "10013": "ArtsEntertainment", "10014": "SportsFitness", "10015": "HobbiesLeisure", "10016": "JobsEducation", "10017": "TravelTourism", "10018": "LawGovernment", "10019": "ComputersConsumerElectronics", "10020": "DiningNightlife", "10021": "<PERSON><PERSON><PERSON>", "10108": "NewsMediaPublications", "13575": "RetailersGeneralMerchandise"}}, "NameToDbValues": {"Vehicles": "10001", "FamilyCommunity": "10002", "RealEstate": "10003", "BusinessIndustrial": "10004", "BeautyPersonalCare": "10005", "OccasionsGifts": "10006", "InternetTelecom": "10007", "HomeGarden": "10009", "FoodGroceries": "10010", "Health": "10011", "Finance": "10012", "ArtsEntertainment": "10013", "SportsFitness": "10014", "HobbiesLeisure": "10015", "JobsEducation": "10016", "TravelTourism": "10017", "LawGovernment": "10018", "ComputersConsumerElectronics": "10019", "DiningNightlife": "10020", "Apparel": "10021", "NewsMediaPublications": "10108", "RetailersGeneralMerchandise": "13575"}}, {"ReportColumnId": "Report.L2Vertical", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Other", "10022": "PersonalAirplanesAircraft", "10024": "MotorVehicles", "10026": "VehiclePartsAccessories", "10027": "BoatsWatercraft", "10188": "VehicleWarrantiesProtectionPlans", "10190": "VehicleAuctions", "10191": "DrivingInstructionDriverEducation", "10193": "VehicleRepairMaintenance", "10194": "VehicleTowing", "10195": "VehicleEmissions", "10196": "VehicleSpecsReviewsComparisons", "10198": "VehicleDonationRemoval", "10978": "VehicleHistoryReports", "10980": "VehicleWindowTinting", "13655": "VehicleDealers", "10028": "CommunityServiceSocialOrganizations", "10029": "DomesticPersonalResources", "10030": "BabyParentingFamily", "10031": "RomanceRelationships", "10231": "<PERSON><PERSON><PERSON><PERSON>", "10033": "PropertyManagement", "10034": "PropertyInspectionsAppraisals", "10035": "RelocationHouseholdMoving", "10036": "PrivateCommunities", "10037": "HousePlansFloorPlans", "10038": "VacationPropertiesSecondHomes", "10039": "PropertyDevelopment", "10040": "RealEstateAgentsBrokerages", "10041": "EscrowRealEstateTitling", "10042": "RealEstateListings", "10043": "CommercialInvestmentRealEstate", "13649": "RealEstateAuctions", "13784": "HousingMarketRealEstatePrices", "10273": "DesignEngineering", "10276": "BusinessManagement", "10282": "BuildingConstructionMaintenance", "10290": "RetailTrade", "10294": "ScientificEquipmentServices", "10295": "EnergyIndustry", "10299": "SecurityEquipmentServices", "10300": "Office", "10301": "FoodServiceIndustry", "10305": "ShippingPacking", "10307": "CommercialIndustrialPrinting", "10308": "JanitorialProductsServices", "11235": "VeterinaryEquipmentSupplies", "13460": "Agriculture", "13464": "IndustrialGoodsManufacturing", "13491": "ChemicalIndustry", "13676": "FishingIndustry", "13804": "Manufacturing", "13841": "ImportExport", "13844": "FoodProduction", "13864": "AerospaceDefense", "10047": "SkinCare", "10048": "TanningSunCare", "10049": "SpaMedicalSpa", "10050": "AntiAging", "10051": "NailCare", "10053": "LipCare", "10054": "MakeUpCosmetics", "10055": "HairCare", "10057": "PerfumesFragrances", "10058": "ShavingGrooming", "10059": "OralCare", "10060": "HygieneToiletries", "13422": "BodyArt", "13636": "FashionStyle", "13866": "InsectRepellent", "10362": "FlowerArrangements", "10364": "CardsGreetings", "10365": "Gifts", "10368": "PartiesPartySupplies", "10372": "SingingTelegrams", "13493": "SpecialOccasions", "13509": "HolidaysSeasonalEvents", "10878": "Telephony", "11506": "VOIP", "11507": "Teleconferencing", "11509": "CallingCards", "13418": "Internet", "13420": "CableServices", "13421": "SatelliteServices", "13662": "Telegrams", "10399": "ResidentialCleaning", "10400": "WatchClockRepair", "10404": "HomeImprovementMaintenance", "10405": "HomeDecorInteriorDecorating", "10406": "YardGardenPatio", "10407": "WaterFilters", "10408": "HomeStorageOrganization", "10410": "KitchenDining", "10411": "HomeSafetySecurity", "10412": "HomeAppliances", "10413": "HomeFurniture", "10414": "HomeGardenMediaPublications", "10415": "Bathroom", "10417": "HomeLaundry", "10418": "HomeHeatingCooling", "10419": "LightsLighting", "11282": "KitchenBathroomCabinets", "11589": "BeddingLinens", "13650": "DoorbellsDoorKnockers", "10081": "HouseholdSupplies", "10082": "Food", "10083": "Beverages", "10084": "OnlineGroceryShoppingGroceryDelivery", "10085": "HealthConditionsConcerns", "10086": "NutritionDieting", "10087": "MedicalDevicesEquipmentSupplies", "10088": "BiotechPharmaceutical", "10089": "Pharmacy", "10090": "ProfessionalMedicalResources", "10091": "HealthCareServices", "10092": "FinancialPlanningManagement", "10093": "Banking", "10094": "MoneyTransferWireServices", "10095": "Investing", "10096": "BusinessFinance", "10097": "CreditLending", "10098": "BusinessNewsMedia", "10099": "GrantsScholarshipsFinancialAid", "10100": "ATMSalesProcessing", "10102": "Insurance", "11093": "AccountingAuditing", "10103": "TVVideo", "10104": "EventEntertainment", "10105": "EntertainmentIndustry", "10106": "HumorJokes", "10109": "MusicAudio", "10110": "SportsEntertainment", "10111": "EventsShowsCulturalAttractions", "10723": "ComicsGraphicNovels", "13388": "VisualArtDesign", "13417": "MoviesFilms", "13425": "FunTrivia", "13530": "ArtsEntertainmentAwards", "13566": "Offbeat", "13670": "EntertainmentMediaRetailers", "13760": "Cartoons", "10114": "SportsNewsMedia", "10115": "SportsFitnessApparel", "10116": "SportsFanGearApparel", "10117": "BoatingWaterRecreation", "10119": "SportsProgramsCamps", "10120": "SportsEquipmentRentalServices", "10121": "SportingGoods", "10122": "SportsInstructionCoaching", "10123": "Fitness", "13605": "Sports", "10113": "CampingOutdoorRecreation", "10125": "ToysGames", "10126": "WineBeerCollectingBrewing", "10128": "Cooking", "10129": "ScaleModelsModelBuilding", "10130": "PetsAnimals", "10131": "ArtsCrafts", "10132": "PhotoVideo", "10133": "AncestryGenealogy", "10134": "Birding", "10135": "AntiquesCollectibles", "10136": "RecreationalActivityEducation", "13405": "PrizesCompetitions", "13569": "Astronomy", "13658": "Gardening", "10139": "JobsCareers", "10140": "AlumniClassmateResources", "10141": "EducationTraining", "10142": "Accommodations", "10143": "LuggageTravelAccessories", "10144": "AirTravel", "10145": "TouristAttractionsDestinations", "10146": "TransportationExcursions", "10147": "LastMinuteTravel", "10148": "TourOperators", "10150": "SpecialtyTravel", "10151": "TravelBookingServices", "10152": "LuggageServices", "10153": "CruisesCruiseServices", "10154": "VacationPackages", "10578": "TravelMediaPublications", "10843": "TravelDocuments", "13594": "LuxuryTravel", "10156": "Politics", "10158": "EmergencyServices", "10159": "Military", "10160": "PublicServices", "10162": "LawEnforcementProtectiveServices", "10163": "Legal", "13343": "GovernmentConsultingContracting", "13414": "Government", "13661": "CensusServices", "10167": "ConsumerElectronics", "10168": "Computers", "10169": "Restaurants", "11870": "DiningNightlifeReviewsGuidesListings", "13439": "NightclubsBarsMusicClubs", "10171": "Jewelry", "10176": "Clothing", "10177": "Footwear", "10178": "ApparelAccessories", "10907": "DryCleaningAlterations", "13355": "RainGear", "10023": "VehicleMediaPublications", "10112": "BooksLiterature", "10573": "MensInterestsMediaPublications", "10576": "HealthMediaPublications", "10581": "WomensInterestsMediaPublications", "10584": "CelebritiesEntertainmentNews", "10586": "ApparelMediaPublications", "10756": "ReferenceMaterialsResources", "11866": "MagazinesMagazineSubscriptions", "11867": "Publishing", "13426": "Weather", "13451": "WorldNewsMedia", "13647": "Newspapers", "13648": "PoliticalNewsMedia", "13691": "OnlineMedia", "13700": "LocalNewsMediaPublications", "13777": "TeenMediaPublications", "13441": "CouponsRebates", "13719": "WholesalersLiquidators", "13810": "InformalSellingExchanging", "13842": "RentalServices", "13860": "ShoppingPortalsSearchEngines"}}, "NameToDbValues": {"Vehicles/PersonalAirplanesAircraft": "10022", "Vehicles/MotorVehicles": "10024", "Vehicles/VehiclePartsAccessories": "10026", "Vehicles/BoatsWatercraft": "10027", "Vehicles/VehicleWarrantiesProtectionPlans": "10188", "Vehicles/VehicleAuctions": "10190", "Vehicles/DrivingInstructionDriverEducation": "10191", "Vehicles/VehicleRepairMaintenance": "10193", "Vehicles/VehicleTowing": "10194", "Vehicles/VehicleEmissions": "10195", "Vehicles/VehicleSpecsReviewsComparisons": "10196", "Vehicles/VehicleDonationRemoval": "10198", "Vehicles/VehicleHistoryReports": "10978", "Vehicles/VehicleWindowTinting": "10980", "Vehicles/VehicleDealers": "13655", "FamilyCommunity/CommunityServiceSocialOrganizations": "10028", "FamilyCommunity/DomesticPersonalResources": "10029", "FamilyCommunity/BabyParentingFamily": "10030", "FamilyCommunity/RomanceRelationships": "10031", "FamilyCommunity/FaithBelief": "10231", "RealEstate/PropertyManagement": "10033", "RealEstate/PropertyInspectionsAppraisals": "10034", "RealEstate/RelocationHouseholdMoving": "10035", "RealEstate/PrivateCommunities": "10036", "RealEstate/HousePlansFloorPlans": "10037", "RealEstate/VacationPropertiesSecondHomes": "10038", "RealEstate/PropertyDevelopment": "10039", "RealEstate/RealEstateAgentsBrokerages": "10040", "RealEstate/EscrowRealEstateTitling": "10041", "RealEstate/RealEstateListings": "10042", "RealEstate/CommercialInvestmentRealEstate": "10043", "RealEstate/RealEstateAuctions": "13649", "RealEstate/HousingMarketRealEstatePrices": "13784", "BusinessIndustrial/DesignEngineering": "10273", "BusinessIndustrial/BusinessManagement": "10276", "BusinessIndustrial/BuildingConstructionMaintenance": "10282", "BusinessIndustrial/RetailTrade": "10290", "BusinessIndustrial/ScientificEquipmentServices": "10294", "BusinessIndustrial/EnergyIndustry": "10295", "BusinessIndustrial/SecurityEquipmentServices": "10299", "BusinessIndustrial/Office": "10300", "BusinessIndustrial/FoodServiceIndustry": "10301", "BusinessIndustrial/ShippingPacking": "10305", "BusinessIndustrial/CommercialIndustrialPrinting": "10307", "BusinessIndustrial/JanitorialProductsServices": "10308", "BusinessIndustrial/VeterinaryEquipmentSupplies": "11235", "BusinessIndustrial/Agriculture": "13460", "BusinessIndustrial/IndustrialGoodsManufacturing": "13464", "BusinessIndustrial/ChemicalIndustry": "13491", "BusinessIndustrial/FishingIndustry": "13676", "BusinessIndustrial/Manufacturing": "13804", "BusinessIndustrial/ImportExport": "13841", "BusinessIndustrial/FoodProduction": "13844", "BusinessIndustrial/AerospaceDefense": "13864", "BeautyPersonalCare/SkinCare": "10047", "BeautyPersonalCare/TanningSunCare": "10048", "BeautyPersonalCare/SpaMedicalSpa": "10049", "BeautyPersonalCare/AntiAging": "10050", "BeautyPersonalCare/NailCare": "10051", "BeautyPersonalCare/LipCare": "10053", "BeautyPersonalCare/MakeUpCosmetics": "10054", "BeautyPersonalCare/HairCare": "10055", "BeautyPersonalCare/PerfumesFragrances": "10057", "BeautyPersonalCare/ShavingGrooming": "10058", "BeautyPersonalCare/OralCare": "10059", "BeautyPersonalCare/HygieneToiletries": "10060", "BeautyPersonalCare/BodyArt": "13422", "BeautyPersonalCare/FashionStyle": "13636", "BeautyPersonalCare/InsectRepellent": "13866", "OccasionsGifts/FlowerArrangements": "10362", "OccasionsGifts/CardsGreetings": "10364", "OccasionsGifts/Gifts": "10365", "OccasionsGifts/PartiesPartySupplies": "10368", "OccasionsGifts/SingingTelegrams": "10372", "OccasionsGifts/SpecialOccasions": "13493", "OccasionsGifts/HolidaysSeasonalEvents": "13509", "InternetTelecom/Telephony": "10878", "InternetTelecom/VOIP": "11506", "InternetTelecom/Teleconferencing": "11507", "InternetTelecom/CallingCards": "11509", "InternetTelecom/Internet": "13418", "InternetTelecom/CableServices": "13420", "InternetTelecom/SatelliteServices": "13421", "InternetTelecom/Telegrams": "13662", "HomeGarden/ResidentialCleaning": "10399", "HomeGarden/WatchClockRepair": "10400", "HomeGarden/HomeImprovementMaintenance": "10404", "HomeGarden/HomeDecorInteriorDecorating": "10405", "HomeGarden/YardGardenPatio": "10406", "HomeGarden/WaterFilters": "10407", "HomeGarden/HomeStorageOrganization": "10408", "HomeGarden/KitchenDining": "10410", "HomeGarden/HomeSafetySecurity": "10411", "HomeGarden/HomeAppliances": "10412", "HomeGarden/HomeFurniture": "10413", "HomeGarden/HomeGardenMediaPublications": "10414", "HomeGarden/Bathroom": "10415", "HomeGarden/HomeLaundry": "10417", "HomeGarden/HomeHeatingCooling": "10418", "HomeGarden/LightsLighting": "10419", "HomeGarden/KitchenBathroomCabinets": "11282", "HomeGarden/BeddingLinens": "11589", "HomeGarden/DoorbellsDoorKnockers": "13650", "FoodGroceries/HouseholdSupplies": "10081", "FoodGroceries/Food": "10082", "FoodGroceries/Beverages": "10083", "FoodGroceries/OnlineGroceryShoppingGroceryDelivery": "10084", "Health/HealthConditionsConcerns": "10085", "Health/NutritionDieting": "10086", "Health/MedicalDevicesEquipmentSupplies": "10087", "Health/BiotechPharmaceutical": "10088", "Health/Pharmacy": "10089", "Health/ProfessionalMedicalResources": "10090", "Health/HealthCareServices": "10091", "Finance/FinancialPlanningManagement": "10092", "Finance/Banking": "10093", "Finance/MoneyTransferWireServices": "10094", "Finance/Investing": "10095", "Finance/BusinessFinance": "10096", "Finance/CreditLending": "10097", "Finance/BusinessNewsMedia": "10098", "Finance/GrantsScholarshipsFinancialAid": "10099", "Finance/ATMSalesProcessing": "10100", "Finance/Insurance": "10102", "Finance/AccountingAuditing": "11093", "ArtsEntertainment/TVVideo": "10103", "ArtsEntertainment/EventEntertainment": "10104", "ArtsEntertainment/EntertainmentIndustry": "10105", "ArtsEntertainment/HumorJokes": "10106", "ArtsEntertainment/MusicAudio": "10109", "ArtsEntertainment/SportsEntertainment": "10110", "ArtsEntertainment/EventsShowsCulturalAttractions": "10111", "ArtsEntertainment/ComicsGraphicNovels": "10723", "ArtsEntertainment/VisualArtDesign": "13388", "ArtsEntertainment/MoviesFilms": "13417", "ArtsEntertainment/FunTrivia": "13425", "ArtsEntertainment/ArtsEntertainmentAwards": "13530", "ArtsEntertainment/Offbeat": "13566", "ArtsEntertainment/EntertainmentMediaRetailers": "13670", "ArtsEntertainment/Cartoons": "13760", "SportsFitness/SportsNewsMedia": "10114", "SportsFitness/SportsFitnessApparel": "10115", "SportsFitness/SportsFanGearApparel": "10116", "SportsFitness/BoatingWaterRecreation": "10117", "SportsFitness/SportsProgramsCamps": "10119", "SportsFitness/SportsEquipmentRentalServices": "10120", "SportsFitness/SportingGoods": "10121", "SportsFitness/SportsInstructionCoaching": "10122", "SportsFitness/Fitness": "10123", "SportsFitness/Sports": "13605", "HobbiesLeisure/CampingOutdoorRecreation": "10113", "HobbiesLeisure/ToysGames": "10125", "HobbiesLeisure/WineBeerCollectingBrewing": "10126", "HobbiesLeisure/Cooking": "10128", "HobbiesLeisure/ScaleModelsModelBuilding": "10129", "HobbiesLeisure/PetsAnimals": "10130", "HobbiesLeisure/ArtsCrafts": "10131", "HobbiesLeisure/PhotoVideo": "10132", "HobbiesLeisure/AncestryGenealogy": "10133", "HobbiesLeisure/Birding": "10134", "HobbiesLeisure/AntiquesCollectibles": "10135", "HobbiesLeisure/RecreationalActivityEducation": "10136", "HobbiesLeisure/PrizesCompetitions": "13405", "HobbiesLeisure/Astronomy": "13569", "HobbiesLeisure/Gardening": "13658", "JobsEducation/JobsCareers": "10139", "JobsEducation/AlumniClassmateResources": "10140", "JobsEducation/EducationTraining": "10141", "TravelTourism/Accommodations": "10142", "TravelTourism/LuggageTravelAccessories": "10143", "TravelTourism/AirTravel": "10144", "TravelTourism/TouristAttractionsDestinations": "10145", "TravelTourism/TransportationExcursions": "10146", "TravelTourism/LastMinuteTravel": "10147", "TravelTourism/TourOperators": "10148", "TravelTourism/SpecialtyTravel": "10150", "TravelTourism/TravelBookingServices": "10151", "TravelTourism/LuggageServices": "10152", "TravelTourism/CruisesCruiseServices": "10153", "TravelTourism/VacationPackages": "10154", "TravelTourism/TravelMediaPublications": "10578", "TravelTourism/TravelDocuments": "10843", "TravelTourism/LuxuryTravel": "13594", "LawGovernment/Politics": "10156", "LawGovernment/EmergencyServices": "10158", "LawGovernment/Military": "10159", "LawGovernment/PublicServices": "10160", "LawGovernment/LawEnforcementProtectiveServices": "10162", "LawGovernment/Legal": "10163", "LawGovernment/GovernmentConsultingContracting": "13343", "LawGovernment/Government": "13414", "LawGovernment/CensusServices": "13661", "ComputersConsumerElectronics/ConsumerElectronics": "10167", "ComputersConsumerElectronics/Computers": "10168", "DiningNightlife/Restaurants": "10169", "DiningNightlife/DiningNightlifeReviewsGuidesListings": "11870", "DiningNightlife/NightclubsBarsMusicClubs": "13439", "Apparel/Jewelry": "10171", "Apparel/Clothing": "10176", "Apparel/Footwear": "10177", "Apparel/ApparelAccessories": "10178", "Apparel/DryCleaningAlterations": "10907", "Apparel/RainGear": "13355", "NewsMediaPublications/VehicleMediaPublications": "10023", "NewsMediaPublications/BooksLiterature": "10112", "NewsMediaPublications/MensInterestsMediaPublications": "10573", "NewsMediaPublications/HealthMediaPublications": "10576", "NewsMediaPublications/WomensInterestsMediaPublications": "10581", "NewsMediaPublications/CelebritiesEntertainmentNews": "10584", "NewsMediaPublications/ApparelMediaPublications": "10586", "NewsMediaPublications/ReferenceMaterialsResources": "10756", "NewsMediaPublications/MagazinesMagazineSubscriptions": "11866", "NewsMediaPublications/Publishing": "11867", "NewsMediaPublications/Weather": "13426", "NewsMediaPublications/WorldNewsMedia": "13451", "NewsMediaPublications/Newspapers": "13647", "NewsMediaPublications/PoliticalNewsMedia": "13648", "NewsMediaPublications/OnlineMedia": "13691", "NewsMediaPublications/LocalNewsMediaPublications": "13700", "NewsMediaPublications/TeenMediaPublications": "13777", "RetailersGeneralMerchandise/CouponsRebates": "13441", "RetailersGeneralMerchandise/WholesalersLiquidators": "13719", "RetailersGeneralMerchandise/InformalSellingExchanging": "13810", "RetailersGeneralMerchandise/RentalServices": "13842", "RetailersGeneralMerchandise/ShoppingPortalsSearchEngines": "13860"}}, {"ReportColumnId": "Report.L3Vertical", "IsFilterable": true, "LocalizationKeys": {"default": {"0": "Other", "10181": "OffRoadVehicles", "10182": "Motorcycles", "10183": "CampersRVs", "10184": "VehicleTrailers", "10185": "CarsTrucks", "10186": "CommercialVehicles", "10187": "ScootersMopeds", "10189": "FlyingInstruction", "10200": "PersonalAircraftRepairMaintenance", "10201": "PersonalAirplaneAircraftPartsAccessories", "10202": "OffRoadVehiclePartsAccessories", "10203": "TruckPartsAccessories", "10204": "MotorcyclePartsAccessories", "10205": "VehicleCleansersCareSupplies", "10206": "VehicleTiresTireParts", "10207": "VehicleRacks", "10208": "ScooterMopedPartsAccessories", "10209": "CarPartsAccessories", "10210": "Yachts", "10211": "FishingBoats", "10212": "PersonalWatercraft", "10213": "Sailboats", "10214": "Rafts", "10215": "RowBoatsCanoes", "10216": "<PERSON><PERSON>", "10217": "SpeedboatsMotorboats", "10967": "BrakeRepair", "10968": "WindshieldRepair", "10969": "OilChanges", "10971": "TuneUps", "10972": "TransmissionsTransmissionRepair", "10973": "CollisionAutoBodyRepair", "10974": "BoatTowing", "10979": "VehicleWashingDetailing", "10981": "VehiclePainting", "13656": "CarDealers", "13657": "BoatYachtSalesBrokers", "13714": "UsedMotorVehicles", "10127": "OccultParanormal", "10218": "VolunteerOpportunities", "10219": "DisasterReliefEmergencyManagement", "10220": "DisabilityResources", "10221": "EthnicIdentityResources", "10222": "ConsumerResources", "10223": "AIDSHIVSupport", "10224": "AlcoholSubstanceAbuse", "10225": "TeenAdviceSupport", "10226": "GLBTResources", "10227": "SocialIssuesAdvocacy", "10228": "YouthOrganizationsResources", "10229": "CommunityOutreach", "10230": "NonprofitServices", "10233": "PersonalErrandConciergeServices", "10234": "PersonalChefs", "10235": "Baby", "10236": "ParentingFamily", "10237": "RelationshipAdvice", "10238": "DivorceSeparation", "10240": "DatingMarriageMatching", "10242": "TroubledRelationships", "11063": "SelfDefenseTraining", "11077": "Environmentalism", "13392": "SocialInsuranceProgramsEntitlements", "13393": "SelectiveService", "13434": "PlacesofWorshipWorshipServices", "13435": "ReligiousInstruction", "13436": "ReligiousMediaPublications", "13469": "AstrologyHoroscopes", "13472": "Christianity", "13473": "Islam", "13474": "Buddhism", "13475": "Hinduism", "13476": "Judaism", "13477": "WiccaEsotericReligions", "13479": "Scientology", "13599": "MultilateralNonGovernmentalOrganizations", "13703": "SelfHelpMotivational", "13709": "SeniorResources", "13807": "Prayers", "13836": "Meditation", "10032": "RentalListings", "10247": "LandSurveying", "10248": "HomeAppraisals", "10249": "TemporaryHousingServices", "10250": "MovingTruckVanRental", "10251": "MovingSupplies", "10252": "StorageRentalServices", "10253": "ConsumerVehicleShippingServices", "10254": "SharedInterestLivingCommunities", "10255": "GolfCommunities", "10256": "Timeshares", "10257": "CommercialPropertyDevelopment", "10258": "NewHomesCustomHomes", "10259": "RealEstateTitlingLandRegistry", "10260": "EscrowServices", "10261": "SeniorLivingListings", "10262": "RentToOwnRealEstateListings", "10264": "CommercialRealEstateListings", "10265": "GovernmentAssistanceHomeListingServices", "10266": "LandAcreageListings", "10267": "ForSaleByOwnerListings", "13688": "CondoCoopListings", "13689": "MobileManufacturedHomeListings", "13690": "BankOwnedForeclosedListings", "10271": "ConsumerGoodsManufacturing", "10281": "IndustrialManufacturing", "10298": "TextilesTextileServices", "10302": "IndustrialElectronics", "10306": "PlantFacility", "11047": "Bioremediation", "11054": "MaterialsTesting", "11062": "BodyGuards", "11065": "SecurityGuards", "11066": "SecurityAlarmServices", "11067": "OccupationalHealthSafety", "11068": "GraphicDesign", "11069": "DraftingServices", "11073": "AquacultureFisheryServices", "11074": "AgriculturalResearch", "11076": "AgriculturalTestingAnalysis", "11078": "Horticulture", "11083": "Forestry", "11084": "HumanResources", "11085": "MailOrderServices", "11086": "BusinessStrategyPlanning", "11088": "AdvertisingMarketing", "11089": "BusinessProcessAnalysis", "11090": "CorporateEvents", "11092": "CustomerSupportOutreach", "11094": "TechnologyConsulting", "11095": "PhysicalAssetManagement", "11096": "BusinessSecurityInvestigation", "11097": "SupplyChainManagement", "11098": "ManagementConsulting", "11099": "OfficeFacilitiesManagement", "11100": "Transcription", "11102": "Warehousing", "11103": "Freighting", "11104": "ExpressExpeditedShipping", "11130": "Architecture", "11132": "ExcavationServices", "11133": "ChimneyServices", "11138": "BuildingPaintingServices", "11141": "CommercialGeneralContracting", "11143": "ElectricianServices", "11145": "ConstructionEstimation", "11148": "DemolitionServices", "11149": "PropertyInspectionCoding", "11150": "LandscapingLandscapeDesign", "11151": "BuildingRestorationPreservation", "11153": "LocksLocksmiths", "11154": "ConstructionConsulting", "11155": "RestaurantSuppliers", "11156": "FoodPackingBottling", "11159": "FoodImportExport", "11160": "FoodConsulting", "11161": "FoodMarketing", "11162": "FoodDistribution", "11163": "FoodScience", "11168": "CheckPrinting", "11169": "BookPrinting", "11171": "ScreenPrinting", "11173": "EnergyManagement", "11174": "EnergyPriceComparison", "11175": "EnergyResearch", "11176": "EnergyConsulting", "11178": "LossPrevention", "11179": "Vending", "11180": "MerchantServicesPaymentSystems", "11181": "Inventorying", "11188": "Crops", "11190": "Irrigation", "11192": "Livestock", "11194": "AgriculturalEquipment", "11198": "Metals", "11202": "Minerals", "11205": "RetailSignsBanners", "11206": "HandTrucksUtilityCarts", "11207": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "11208": "BarCodeScanners", "11209": "TurnstilesTrafficControlDevices", "11210": "ApparelRetailSupplies", "11211": "PaperPlasticBags", "11214": "PegboardSlatwall", "11216": "FloorMats", "11217": "ShrinkWrappingSystems", "11219": "RetailDisplaySupplies", "11220": "TimeClocksTimeCards", "11224": "CounterfeitMoneyDetectors", "11227": "AshTrayReceptacles", "11229": "TaggingGuns", "11230": "JewelryBagsBoxesDisplays", "11231": "ShoppingBasketsCarts", "11232": "MerchantPointofSaleSystems", "11233": "CashRegistersCashRegisterSupplies", "11250": "ChemicalsChemicalProducts", "11252": "BiotechnologyEquipment", "11253": "ScientificBooksJournals", "11254": "LaboratoryEquipmentSupplies", "11255": "ScientificGlasswareAccessories", "11259": "IndustrialMagnets", "11263": "Generators", "11275": "EarthMoversConstructionTractors", "11276": "ConstructionTrailers", "11277": "Handrails", "11278": "KitchenBathroomCounters", "11279": "PrefabricatedBuildings", "11280": "HousePaintsStains", "11284": "HVACClimateControl", "11285": "Roofing", "11287": "HardwareToolsAccessories", "11292": "Masonry", "11293": "InstallationLighting", "11297": "DeckingProductsEquipment", "11298": "RebarRebarAccessories", "11301": "NaturalFibers", "11305": "Paper", "11309": "Safes", "11310": "NightVisionGogglesScopes", "11311": "ElectronicVoiceChangers", "11313": "SurveillanceEquipment", "11316": "Intercoms", "11320": "WeatherAlertRadios", "11321": "SecurityWeapons", "11322": "CarAlarmSystems", "11323": "OfficeFurniture", "11324": "LanyardsBadgeHolders", "11325": "BusinessCardsStationeryForms", "11326": "PaperClipsRubberBands", "11327": "StampsEnvelopes", "11328": "Calculators", "11329": "GluePaste", "11330": "StaplersScissorsPunchers", "11332": "DeskOrganizers", "11333": "PortfoliosPresentationCases", "11334": "CalendarsPlanners", "11335": "PrinterAccessories", "11336": "LabelsLabeling", "11337": "MicrocassetteRecorders", "11338": "BindersBindingLaminating", "11339": "OfficeAppliances", "11340": "BeverageBarSupplies", "11341": "DishDispensers", "11342": "SteamTablesFoodWarmers", "11343": "IndustrialRefrigeration", "11344": "SaladColdFoodBars", "11345": "CommercialFoodPreparationEquipment", "11346": "ConcessionEquipment", "11347": "ChafingDishes", "11348": "IceMachines", "11349": "WorkDishTables", "11352": "FoodDisplayCases", "11353": "FoodTransportCarts", "11354": "FoodScales", "11355": "IndustrialFoodStorage", "11367": "DraftingBoardsTables", "11369": "DraftingEquipment", "11370": "PensWritingInstruments", "11373": "TapeTapeDispensers", "11374": "CargoContainers", "11376": "MailingTubes", "11377": "StrappingEquipmentDevices", "11380": "CDDVDJewelboxesCases", "11381": "PackingCushioningPackingPaper", "11382": "ShippingBoxes", "11388": "WasteManagement", "11392": "CDDVDDuplicationPrinting", "11394": "UVCuring", "11396": "PadPrinting", "11397": "LargeFormatPrinters", "11398": "JanitorialSigns", "11399": "TouchFreeDevices", "11400": "FloorCleaning", "11401": "ElectricHandDryers", "11402": "JanitorialCarts", "11403": "UrinalSupplies", "11404": "WasteReceptacles", "11406": "OdorControlDevices", "11407": "TowelDispensers", "11408": "SoapDispensers", "11485": "CateringBartending", "12052": "CulinarySchoolsCourses", "12110": "RenewableAlternativeEnergy", "12113": "OilGas", "12345": "Biochemistry", "12552": "Agrochemicals", "12597": "TilesTiling", "12708": "PrintingPresses", "13411": "BusinessOpportunities", "13462": "Plumbing", "13604": "CouriersMessengers", "13677": "CommercialFishing", "13724": "MailPackageDelivery", "13731": "EcoFriendlyBuildingConstruction", "13732": "IndustrialSystemsEngineering", "13733": "IndustrialProductDesign", "13734": "InteriorDesign", "13797": "BuildingMaterialsSupplies", "13848": "UrbanRegionalPlanning", "13854": "ECommerce", "13868": "CivilEngineering", "10061": "FaceCare", "10309": "BodyCleansing", "10310": "BodyLotionsOils", "10311": "SunLotionsSunscreen", "10312": "TanningServices", "10313": "TanningBeds", "10314": "AtHomeSpaEquipment", "10315": "TattooRemoval", "10316": "AromatherapyEssentialOils", "10317": "CosmeticProcedures", "10319": "NailPolishNailPolishAccessories", "10320": "NailCareTools", "10321": "NailFungusProducts", "10322": "ManicuresPedicures", "10323": "LipsticksLipGlosses", "10324": "EyeMakeUp", "10325": "<PERSON><PERSON>", "10327": "FacePowder", "10329": "Concealer", "10330": "Foundation", "10331": "MakeUpBrushesTools", "10332": "WigsWigAccessories", "10335": "DamagedHair", "10336": "HairStraightenersRelaxers", "10337": "HairDyesColoring", "10338": "ShampoosConditioners", "10339": "HairStyling", "10342": "PersonalGroomingKitsEquipment", "10343": "UnwantedBodyFacialHairRemoval", "10345": "CankerSoreTreatments", "10346": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "10347": "DentalFlossGumHealth", "10348": "Mouthwash", "10349": "DentalCareGum", "10350": "Toothpaste", "10351": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "10352": "ToothbrushesToothbrushAccessories", "10353": "DentureCare", "10354": "CottonSwabsBallsPads", "10355": "AntiperspirantsDeodorantsBodySpray", "10356": "FeminineHygieneProducts", "10477": "<PERSON><PERSON><PERSON>", "10898": "BodyJewelryPiercings", "11414": "VirtualMakeovers", "11425": "RazorsShavers", "11434": "AntiAgingCreamsMoisturizers", "13446": "Saunas", "13637": "FashionDesignersCollections", "13638": "FashionModeling", "13704": "DaySpasSalonSpas", "13705": "DestinationResortSpas", "13850": "ExfoliantsScrubs", "10361": "StationeryStationerySets", "10363": "HolidaySeasonalDecorations", "10366": "FuneralsBereavement", "10367": "GiftWrapRibbons", "10369": "Weddings", "10370": "GiftRegistries", "10373": "GiftDelivery", "11443": "PhotoCards", "11448": "ECards", "11458": "GourmetFoodGifts", "11461": "GiftBaskets", "11466": "PartyDecorations", "11467": "PartyFavors", "11468": "PaperPlatesNapkinsDisposableCutlery", "11469": "PartyInvitations", "11483": "WeddingSpecialOccasionPhotoVideo", "12966": "GiftCards", "13468": "EngravingJewelryEngraving", "13501": "Anniversaries", "13502": "BirthdaysNameDays", "13503": "BabyShowersNewBaby", "13504": "Graduation", "13505": "ReligiousOccasions", "13506": "GetWell", "13507": "MothersDayFathersDay", "13510": "ValentinesDay", "13738": "Christmas", "13739": "Easter", "13740": "HalloweenOctober31st", "13741": "JewishHolidays", "13742": "Thanksgiving", "13786": "CarnivalMardiGras", "13787": "NewYears", "11491": "InternetServicePlans", "11492": "OnlinePhotoVideoSharing", "11493": "WebDesignDevelopment", "11494": "BloggingResourcesServices", "11495": "DirectoriesListings", "11496": "SocialNetworksOnlineCommunities", "11498": "SearchEngines", "11499": "ForumChatServices", "11500": "OnlineMaps", "11502": "FileSharingHosting", "11503": "OnlineAuctioningServices", "11504": "WebHosting", "11505": "PhoneRentalServices", "11508": "TelephonyEquipmentDonation", "12155": "TelephoneMaintenance", "12168": "VOIPTelephones", "12169": "AnsweringMachines", "12171": "LandlinePhonesAccessories", "12766": "CableTV", "12767": "SatelliteTV", "13088": "NetworkSecurity", "13094": "EmailMessaging", "13381": "MobilePhonesAccessories", "13415": "VoicemailServicesCallingFeatures", "13419": "PhonePlans", "13725": "WebPortals", "13768": "DomainNamesDomainNameRegistration", "10402": "FurnitureRefinishingRepairReupholstery", "10409": "ResidentialCeilingFans", "10416": "WindowTreatments", "11522": "UpholsteryCleaning", "11523": "HouseCleaningServices", "11524": "RugCarpetCleaning", "11525": "WindowCoveringCleaning", "11527": "HomeApplianceServiceRepair", "11528": "WallPaper", "11529": "CeramicsPorcelainVases", "11530": "DecorativeFountains", "11531": "Clocks", "11532": "FlowerPotsPlanters", "11533": "CandlesIncense", "11534": "Mirrors", "11535": "DecorativeThrowsPillows", "11536": "ArtGlass", "11537": "Fencing", "11538": "SwimmingPoolsSpas", "11540": "YardPatioFurnitureAccessories", "11541": "OutdoorCookingEquipmentAccessories", "11543": "YardGardenEquipmentSupplies", "11546": "RetainingWalls", "11547": "ClosetOrganizingComponentsSystems", "11549": "WineRacksStorageSystems", "11550": "LaundryStorageOrganization", "11551": "CanningPreserving", "11552": "PlatesServingDishes", "11554": "Silverware", "11555": "KitchenUtensilsGadgets", "11556": "CookwareCookwareSets", "11557": "Bakeware", "11558": "KitchenLinens", "11560": "CutleryCuttingAccessories", "11561": "LockboxesHideAKeys", "11562": "InHomeHazardDetectors", "11563": "FireExtinguishers", "11564": "LaundryRoomAppliances", "11565": "FloorCleanersAccessories", "11566": "SmallKitchenAppliances", "11567": "MajorKitchenAppliances", "11568": "KitchenDiningRoomFurniture", "11569": "PlayroomChildrensFurniture", "11570": "BathroomFurniture", "11571": "BedroomFurniture", "11572": "HomeOfficeFurniture", "11573": "AntiqueFurniture", "11574": "LivingRoomFurniture", "11575": "BathroomTowels", "11576": "ToiletAccessories", "11577": "TowelBarsHooks", "11578": "ShowerCurtainsAccessories", "11579": "BathMatsRugs", "11580": "BathroomAccessories", "11584": "IroningBoardsIroningCenters", "11585": "DeskFansStandFans", "11586": "Thermostats", "11587": "LampsLampShades", "11588": "LightBulbs", "12823": "DuvetCoversDuvets", "12824": "Pillows", "12825": "BlanketsBedspreads", "12826": "SheetsPillowcases", "13139": "NightLights", "13250": "PestWeedControl", "13363": "MedicineCabinets", "13370": "KitchenAccessories", "13386": "KitchenStorage", "13387": "CookieJarsBreadBoxes", "13408": "Mailboxes", "13447": "Quilts", "13673": "PictureFrames", "13708": "FireplacesWoodStovesPelletStoves", "10421": "PaperTowelsHouseholdPaperProducts", "10422": "FoodWrapsFoodStorage", "10423": "HouseholdCleaningProducts", "10424": "CharcoalBarbecuingSupplies", "10425": "TrashBags", "10426": "GrainsPastaSideDishes", "10427": "JamsJelliesPreserves", "10428": "WeightLossFoodsNutritionBars", "10429": "PreparedFoods", "10430": "CondimentsDressings", "10431": "<PERSON><PERSON><PERSON>", "10432": "BakedGoods", "10433": "CookingOilsSpray", "10434": "BakingIngredients", "10435": "SnackFoods", "10436": "Desserts", "10437": "MeatPoultry", "10438": "CandyGumMints", "10439": "BreakfastFoods", "10440": "DairyNonDairyAlternatives", "10441": "Produce", "10442": "SpecialRestrictedDietFoods", "10443": "FishSeafood", "10444": "NonAlcoholicBeverages", "10445": "AlcoholicBeverages", "11613": "HerbsSpices", "13695": "OrganicNaturalFoods", "13747": "<PERSON>ut<PERSON><PERSON><PERSON>", "13758": "GourmetSpecialtyFoods", "13843": "SoupsStewsBeans", "10446": "AllergySinus", "10448": "RespiratoryConditions", "10449": "EatingDisorders", "10450": "Obesity", "10451": "CysticFibrosis", "10452": "ColdSores", "10453": "SmokingSmokingCessation", "10456": "DigestiveHealthDisorders", "10457": "WeightLoss", "10458": "TraumaStressDisorder", "10459": "Anemia", "10460": "Vision", "10461": "InjuryWoundCare", "10462": "SexualReproductiveHealth", "10463": "UrinaryBladderHealth", "10464": "CancerCancerTreatment", "10465": "GeriatricsAging", "10466": "Hemorrhoids", "10467": "Hepatitis", "10468": "InfectiousDiseasesViruses", "10469": "LearningDevelopmentalDisabilities", "10470": "WomensHealthOBGYN", "10471": "Epilepsy", "10472": "EarNoseThroat", "10473": "SkinConditionsSkinHealth", "10474": "NeurologicalConditions", "10475": "HeartHealth", "10476": "AIDSHIV", "10478": "InsomniaSleepDisorders", "10479": "Arthritis", "10480": "MensHealth", "10482": "<PERSON><PERSON>", "10483": "BloodSugarDiabetes", "10484": "MusculoskeletalConditions", "10485": "FootHealth", "10486": "HeadachePainManagement", "10487": "DentalHealth", "10488": "MentalHealth", "10491": "DietsDietPrograms", "10492": "NutritionCounseling", "10493": "FirstAidSupplies", "10494": "MobilityEquipmentAccessories", "10495": "MedicalNeedlesSyringesInjectionSupplies", "10496": "MedicationAccessories", "10499": "PharmaceuticalManufacturing", "10501": "ProteinIdentificationProteomics", "10504": "MedicalResearch", "10505": "ProfessionalHealthCareEducation", "10507": "AlternativeNaturalMedicine", "10508": "PlasticSurgery", "10509": "LaboratoryTestingMedicalDiagnosticServices", "10510": "AbortionServices", "10511": "PhysicalRehabilitation", "10512": "WomensHealthCareServices", "10513": "GeneralHealthCareServices", "10514": "DentistsDentalServices", "10515": "VisionServices", "10516": "MedicalSkinCareServices", "10517": "GeriatricAgingCareServices", "10518": "MentalHealthServices", "10519": "OrthopedicsSportsMedicine", "10520": "HospitalsHealthClinics", "10521": "HospiceHomeNursingCare", "11026": "PediatricsChildrensHealth", "11046": "ImmunologyServices", "11048": "ToxicologyServices", "11234": "ProfessionalDentalEquipmentSupplies", "11765": "BathroomSafetyEquipment", "11766": "HospitalBedsStretchersAccessories", "11767": "MedicalCompressionGarmentsStockings", "11768": "ReachersGrab<PERSON>", "11769": "ProstheticsArtificialLimbs", "11771": "OrthopedicBracesSupports", "11772": "VitaminsSupplements", "11773": "DrugsMedications", "11793": "WellnessTherapyMassage", "12106": "BloodOrganDonationsTransplants", "12481": "OphthalmologicalInstruments", "12487": "PatientGownsGarments", "12488": "ExamTablesSurgeryTables", "12490": "SurgicalInstruments", "12491": "MedicalScrubs", "12492": "MedicalEquipmentPartsAccessories", "12494": "MedicalDiagnosticEquipmentMonitoringDevices", "12495": "UtilityMedicalEquipment", "13167": "Defibrillators", "13169": "InfusionPumps", "13358": "BleedingDisorders", "13362": "GeneticDisorders", "13369": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "13452": "SexReassignmentTherapy", "13745": "ThyroidConditions", "10303": "BusinessCommercialInsurance", "10524": "MoneyManagement", "10525": "InheritanceEstatePlanning", "10526": "WealthManagement", "10527": "RetirementPensionPlanning", "10531": "RealEstateInvestments", "10535": "StudyGrantsScholarships", "10536": "StudentLoans", "10538": "Annuities", "10539": "LifeInsurance", "10540": "HomeWarranties", "10541": "DisabilityInsurance", "10542": "PetInsurance", "10544": "HealthInsurance", "10545": "LiabilityInsurance", "10546": "TravelInsurance", "10547": "PropertyInsurance", "10548": "DisasterInsurance", "10549": "CreditInsurance", "10550": "VehicleInsurance", "10551": "AccidentCasualtyInsurance", "11811": "BillPaymentServices", "11812": "OffshoreBanking", "11813": "PrivateBanking", "11814": "OnlineBanking", "11815": "MobileBanking", "11816": "DebitCheckCards", "11817": "BankAccounts", "11818": "FixedDepositAccountsCertificates", "11819": "BrokeragesDayTrading", "11820": "InvestorRelationsVentureCapital", "11823": "Derivatives", "11824": "RetirementInvestments", "11825": "SpreadBetting", "11826": "ExchangeTradedFunds", "11827": "Securities", "11828": "MutualFunds", "11831": "CurrenciesForeignExchange", "11832": "CommoditiesFuturesTrading", "11833": "InterestFreeTaxDeferredSavingsAccounts", "11834": "PreciousMetalsTrading", "11835": "HedgeFunds", "11836": "IdentityTheftCreditMonitoring", "11837": "CreditCounseling", "11838": "CreditReportsReportingServices", "11839": "DebtManagementConsolidation", "11841": "Loans", "11842": "CreditCards", "12387": "TaxPreparationPlanning", "12388": "BillingServices", "12389": "SarbanesOxleyComplianceManagement", "12391": "Bookkeeping", "12392": "CollectionServices", "12393": "CashFlowManagement", "12394": "RiskManagement", "12948": "StudentBanking", "13437": "GovernmentGrants", "13717": "InvestmentBanking", "13720": "MergersAcquisitions", "10553": "MovieTVFanSites", "10554": "MovieTickets", "10556": "VideoClips", "10558": "EntertainmentDVDs", "10561": "DVDBluRayRetailers", "10562": "VideoOnDemand", "10563": "BachelorBacheloretteParties", "10564": "ChildrensParties", "10565": "TalentPromotion", "10570": "Radio", "10588": "RecordLabels", "10589": "SongLyricsTabs", "10590": "MusicVideos", "10591": "MusicStreamsDownloads", "10592": "MusicalInstrumentsEquipment", "10593": "ConcertsMusicFestivals", "10595": "MusicAudioRetailers", "10596": "MusicAudioCDs", "10597": "SportScoresStatistics", "10598": "FantasySports", "10599": "SportsFanSitesBlogs", "10602": "ExpoEventsShows", "10603": "TicketSalesExchanges", "10606": "EventShowGuidesListings", "11883": "SportsEventTicketsTicketingServices", "11885": "SpecialExhibits", "11886": "TheaterTheaterTickets", "12001": "IndependentAlternativeComicBooks", "12002": "SuperheroComicBooks", "13427": "MusicEducationInstruction", "13443": "ComicStrips", "13489": "FunTestsSillySurveys", "13492": "WeddingEntertainment", "13498": "FilmFestivals", "13499": "ArtFairsFestivals", "13500": "FoodBeverageEvents", "13511": "BluRayDiscs", "13515": "ActionAdventureFilms", "13518": "AnimatedFilms", "13519": "BollywoodSouthAsianFilm", "13520": "ClassicFilms", "13521": "CultIndieFilms", "13522": "DocumentaryFilms", "13523": "ComedyFilms", "13524": "DramaFilms", "13525": "HorrorFilms", "13526": "ScienceFictionFantasyFilms", "13527": "MusicalFilms", "13528": "MovieTheaters", "13529": "WarFilms", "13531": "FilmNoir", "13532": "FamilyChildrensFilms", "13533": "ThrillerCrimeMysteryFilms", "13534": "RomanceFilms", "13535": "SportsFilms", "13536": "FilmTVIndustry", "13538": "ClassicalMusic", "13539": "CountryMusic", "13540": "DanceElectronicMusic", "13541": "ExperimentalIndustrialMusic", "13542": "FolkTraditionalMusic", "13543": "JazzBlues", "13546": "PopMusic", "13547": "ReligiousMusic", "13549": "RockMusic", "13552": "IndieAlternativeMusic", "13555": "Soundtracks", "13556": "UrbanHipHop", "13559": "VocalsShowTunes", "13560": "WorldMusic", "13567": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "13568": "SongwritersComposers", "13570": "Manga", "13572": "MovieTVReference", "13574": "MusicRecordingIndustry", "13595": "Painting", "13635": "ProfessionalWrestling", "13672": "LatinMusic", "13729": "Filmmaking", "13751": "Fireworks", "13799": "ChildrensMusic", "13824": "DVDVideoRentals", "13832": "VinylRecords", "13852": "LiveComedy", "10617": "FitnessMediaPublications", "10618": "WinterSportsApparel", "10621": "RunningApparel", "10622": "IceSkatingApparel", "10623": "CyclingApparel", "10624": "GymnasticsApparel", "10626": "SurfingWindsurfingGear", "10627": "BoatWatercraftPartsAccessories", "10628": "Fishing", "10629": "ScubaDiving", "10630": "WaterSkiingEquipmentGear", "10631": "SkiWinterSportsEquipmentRentalServices", "10632": "BoatMarineVehicleRentalServices", "10633": "IceSkatingEquipment", "10634": "WinterSportsEquipment", "10635": "CricketEquipment", "10636": "RollerSkatingRollerbladingEquipment", "10637": "BasketballEquipment", "10638": "SportsUniforms", "10639": "TableTennisEquipment", "10640": "PaintballEquipment", "10641": "HuntingShootingEquipment", "10642": "SquashRacquetballEquipment", "10643": "BackyardGamesEquipment", "10644": "BaseballEquipment", "10645": "BicyclesAccessories", "10646": "LacrosseEquipment", "10647": "BowlingEquipment", "10648": "AirsoftEquipment", "10649": "RugbyEquipment", "10651": "TennisEquipment", "10652": "SwimmingAquaticSportsEquipment", "10653": "GolfEquipment", "10655": "TrackFieldEquipment", "10656": "ArcheryEquipment", "10657": "RunningWalkingEquipment", "10658": "SoccerEquipment", "10659": "SkateboardingEquipment", "10660": "GymnasticsEquipment", "10661": "AmericanFootballEquipment", "10663": "VolleyballEquipment", "10664": "EquestrianEquipmentTack", "10665": "HockeyRollerHockeyEquipment", "10666": "FitnessClassesInstruction", "10667": "FitnessEquipmentAccessories", "10668": "GymsGymMemberships", "10669": "PersonalTraining", "11901": "SportsTeamJerseys", "11902": "SportsTeamHatsCaps", "13366": "SwimLessonsAquaticFitnessInstruction", "13367": "HorsebackRidingLessons", "13482": "GymnasticsLessonsClasses", "13483": "DanceClassesLessons", "13486": "YogaPilates", "13571": "ExtremeSportsEquipment", "13606": "AmericanFootball", "13607": "Cricket", "13608": "Soccer", "13609": "Basketball", "13610": "Baseball", "13611": "Hockey", "13612": "HuntingShooting", "13613": "Rugby", "13615": "Cycling", "13616": "RunningWalking", "13617": "SkateSports", "13618": "SurfingWindsurfing", "13619": "SwimmingAquaticSports", "13620": "WinterSports", "13621": "CollegeSports", "13623": "ExtremeSports", "13624": "MotorSports", "13625": "WorldSportsEvents", "13626": "Tennis", "13627": "Golf", "13628": "Bowling", "13629": "Gymnastics", "13630": "Volleyball", "13632": "CombatSportsEquipment", "13633": "CombatSports", "13664": "EquestrianSports", "13833": "<PERSON><PERSON>", "13839": "Cheerleading", "13870": "Handball", "13871": "TrackField", "10612": "FlashlightsLanterns", "10614": "PortableIceChestsPicnicCoolers", "10615": "Climbing", "10616": "Hiking", "10671": "Toys", "10672": "Games", "10674": "WineCollecting", "10675": "HomeBrewingWineMaking", "10679": "Recipes", "10683": "PetFoodSupplies", "10684": "PetsByBreed", "10689": "PhotoVideoPublications", "10691": "DigitalCameras", "10692": "VideoCamerasCamcorders", "10693": "PhotoVideoAccessories", "10694": "FilmCameras", "10699": "Figurines", "10701": "Memorabilia", "10702": "AntiqueClocksWatches", "10703": "FolkArtEthnographicAntiques", "10704": "GameRoomCollectibles", "10705": "EuropeanAntiques", "10706": "CollectibleTextilesLinen", "10707": "VintageAdvertisingMedia", "10708": "AntiqueMapsGeographicalArtifacts", "10711": "VintageAntiqueButtons", "10712": "CollectibleMetalWare", "10713": "RocksMineralsFossils", "10714": "CollectibleTelephonesTypewritersElectronics", "10715": "GardenAntiques", "10716": "AntiqueRugsCarpets", "10717": "Militaria", "10718": "CollectiblePins", "10719": "AsianAntiques", "10720": "CollectibleToysGames", "10722": "CollectibleBricABrac", "10724": "CollectibleHardwareTools", "10725": "AntiqueCeramicsPottery", "10727": "AntiqueLampsLighting", "10728": "HolidaySeasonalCollectibles", "10729": "TradingCards", "10730": "AntiqueCoinsCollectibleMoney", "10733": "CollectiblePostcards", "10734": "CollectibleAutographs", "10736": "FantasyMagicalCollectibles", "10737": "VintageAntiqueTransportation", "10738": "AntiqueBooksManuscripts", "10739": "AntiqueGlasswareArtGlass", "10740": "RecreationalActivityLessonsClasses", "10889": "<PERSON><PERSON><PERSON><PERSON>", "11312": "Binoculars", "11526": "AntiqueArtRefinishingRestoration", "11542": "GardenOrnamentsStatuary", "11544": "GardeningBooksPublications", "11545": "YardGardenAccessories", "11559": "CookingMediaPublications", "11887": "Tents", "11888": "CampCookingEquipment", "11889": "BackpackingCampingFood", "11890": "OutdoorSleepingGear", "11977": "PetGrooming", "11978": "PetSittingPetBoardingPetDayCare", "11979": "Veterinary", "11980": "PetBreeding", "11981": "PetAnimalTraining", "11982": "AnimalAdoptionRescue", "11983": "BeadingBeadwork", "11984": "CeramicGlassEquipmentSupplies", "11986": "Scrapbooking", "11987": "FiberCraft", "11994": "WildBirdSupplies", "12007": "AntiqueAppraisals", "12008": "AntiqueCollectibleAuctions", "12011": "PhotographyVideographyClasses", "12776": "GardenFountainsWaterFeatures", "12777": "GardenStructures", "13015": "DogWalking", "13374": "TreesPlantsShrubs", "13428": "MultitoolsPocketKnives", "13596": "PhotographicDigitalArts", "13680": "SweepstakesProductPromotionsGiveaways", "13681": "ContestsPageantsMeritPrizes", "13698": "FleaMarketsSwapMeets", "13752": "WaterContainersBottlesHydrationPacks", "13796": "NameAStar", "13802": "PhotoPrinting", "13820": "AntiqueCollectibleStamps", "13825": "SwordsKnivesDaggers", "13865": "PaperCrafts", "13878": "Wildlife", "10743": "ResumeWriting", "10744": "CareerAssessments", "10745": "CareerEvents", "10746": "JobListings", "10747": "CareerCounselingCoaching", "10748": "ProfessionalNetworkingResources", "10749": "ProfessionalTradeAssociations", "10752": "OnlineEducationDegreePrograms", "10753": "PreschoolNurserySchoolPrograms", "10754": "StandardizedAdmissionsTests", "10755": "WritingCoursesResources", "10757": "LanguageEducation", "10758": "TrainingCertification", "10759": "VocationalTrainingTradeSchools", "10760": "CPRTrainingCertification", "10761": "TutoringServices", "10762": "StudyAbroadPrograms", "10763": "CollegesUniversitiesPostSecondaryEducation", "10766": "CommunityContinuingEducation", "10767": "TeachingClassroomResources", "11029": "PrimarySecondarySchoolingK12", "11871": "Textbooks", "13438": "SpecialEducation", "13828": "AcademicConferencesPublications", "13849": "PublicSpeakingResources", "10149": "CityLocalGuides", "10768": "HostelAccommodations", "10769": "HotelsMotelsResorts", "10770": "VacationRentals", "10771": "CampingCaravanRVAccommodations", "10772": "BedBreakfasts", "10773": "SkiAccommodations", "10774": "LastMinuteAccommodationDeals", "10775": "AccommodationPackages", "10776": "Luggage", "10777": "TravelAccessories", "10778": "AirlineTicketsFaresFlights", "10779": "AirCharterCharterJetServices", "10782": "CarRentalServices", "10783": "BusRailServices", "10784": "TaxiServices", "10785": "AirportTransportationServices", "10786": "Ferries", "10787": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "10788": "TripPlannersRouteFinders", "10789": "LimousineServices", "10790": "LastMinuteAirfaresFlights", "10791": "CharterBusServices", "10792": "SightseeingTours", "10793": "AdventureTravel", "10794": "Ecotourism", "10795": "FamilyVacationsTravel", "10800": "LastMinuteCruises", "10802": "ActivityThemeBasedCruises", "11872": "RoadMaps", "11874": "TravelBooksGuides", "12080": "ZoosAquariums", "12081": "RegionalParksGardens", "12091": "SinglesTravel", "12092": "SeniorCitizenTravel", "12093": "CouplesTravelHoneymoons", "12095": "GLBTTravel", "12096": "BargeRiverCruises", "12098": "WinterTravelDestinations", "12117": "PassportsPassportServices", "12118": "TravelVisasVisaServices", "12119": "TravelConsentForms", "13410": "ParkingServices", "13496": "ThemeParks", "13497": "Museums", "13582": "BeachesIslands", "13806": "HouseSwapsHomeExchanges", "13831": "CarpoolingRidesharing", "13847": "Agritourism", "13857": "HistoricalSitesBuildings", "13858": "LakesRivers", "13862": "Libraries", "10161": "LegalFormsKits", "10165": "PrivateInvestigation", "10805": "ConservativePolitics", "10806": "LobbyingServices", "10807": "PoliticalActivism", "10808": "CampaignsElections", "10809": "LiberalPolitics", "10810": "EmergencyAlertServices", "10811": "RoadsideAssistanceEmergencyRoadServices", "10813": "EmergencyMedicalServices", "10814": "SearchRescueServices", "10815": "FireFightingServices", "10816": "EmergencyTrainingServices", "10817": "PoisonControl", "10818": "Army", "10819": "CoastGuard", "10820": "Navy", "10821": "AirForce", "10822": "Marines", "10823": "NationalGuard", "10825": "AnimalControl", "10826": "PublicBroadcasting", "10827": "PublicHousing", "10828": "PublicNotaries", "10829": "PublicUtilities", "10849": "AirportSecurity", "10850": "TrafficSafety", "10851": "Police", "10852": "ClassActionLaw", "10853": "RealEstateLaw", "10855": "MalpracticeSuitsLaw", "10856": "LaborEmploymentLaw", "10857": "ImmigrationLaw", "10858": "CourtReporting", "10859": "LegalAid", "10860": "FinanceLaw", "10862": "BusinessCorporateLaw", "10863": "MilitaryLegalServices", "10864": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "10865": "LegalInsurance", "10866": "AccidentPersonalInjuryLaw", "10867": "LawsuitFunding", "10868": "FamilyLaw", "10869": "BankruptcyLaw", "10871": "IntellectualProperty", "13345": "MilitaryEducationTraining", "13350": "MilitarySupportServices", "13403": "PublicHealth", "13444": "Royalty", "13508": "GovernmentMinistries", "13644": "LemonLaws", "13646": "VehicleCodesDrivingLaws", "13686": "AttorneysLawFirms", "13736": "CriminalLaw", "13875": "EmbassiesConsulates", "13876": "StateLocalGovernment", "10872": "ConsumerElectronicAccessories", "10873": "HomeAudioVideo", "10876": "WirelessDevices", "10877": "PortableMediaDevices", "10880": "GPSNavigation", "10881": "CarAudioVideo", "10882": "TechnologyNewsPublications", "10883": "ComputerHardware", "10885": "Software", "10886": "ComputerAccessories", "11057": "ComputerManufacturing", "11318": "RadarDetectors", "12148": "ConsumerElectronicWarrantyPlans", "12149": "ConsumerElectronicDonation", "12150": "ConsumerElectronicServiceRepair", "12153": "ElectronicWasteDisposal", "12156": "HostedDataStorage", "12196": "ComputerRentals", "12199": "TechnologySpecsReviewsComparisons", "12201": "ComputerRepair", "12202": "ComputerTechSupport", "12203": "ComputerConsulting", "10887": "RestaurantTakeoutDelivery", "10888": "RestaurantReservationsBooking", "10890": "DineInRestaurants", "10891": "FastFoodRestaurants", "12228": "PizzeriasPizzaDelivery", "12974": "BarClubNightlifeReviewsGuidesListings", "12975": "RestaurantReviewsGuidesListings", "13693": "Bakeries", "10172": "FootwearAccessories", "10173": "ShoeRepairFootwearServices", "10619": "AthleticShoes", "10721": "AntiqueJewelry", "10892": "Rings", "10893": "PreciousSemiPreciousGemsGemstoneJewelry", "10894": "Bracelets", "10895": "PreciousMetalJewelry", "10896": "Cufflinks", "10897": "PearlsPearlJewelry", "10899": "WatchesWatchAccessories", "10901": "Earrings", "10902": "Anklets", "10903": "Necklaces", "10904": "BroochesPins", "10906": "CustomClothing", "10910": "JewelryCleaningRepair", "10912": "PetiteClothing", "10913": "Underwear", "10914": "SuitsBusinessAttire", "10916": "Dancewear", "10917": "Outerwear", "10918": "UtilityClothing", "10919": "CulturallySpecificClothing", "10920": "FormalWear", "10921": "SocksHosiery", "10923": "PlusSizeClothing", "10924": "CostumesCostumeRental", "10925": "Sleepwear", "10926": "Uniforms", "10927": "Sandals", "10928": "Slippers", "10929": "RollerShoes", "10930": "CasualShoes", "10931": "Boots", "10932": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "10933": "Mocca<PERSON>s", "10934": "SpecialWidthOrthopedicShoes", "10935": "BeltsSuspenders", "10936": "Eyewear", "10937": "BagsPacks", "10938": "BillfoldsWallets", "10939": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "10940": "Ties", "10941": "Headwear", "10942": "HairAccessories", "10943": "ScarvesShawls", "11917": "Swimwear", "12237": "PulloverSweatersCardigans", "12238": "SweatshirtsHoodies", "12239": "Vests", "12240": "ShirtsTopsBlouses", "12241": "PantsJeansTrousers", "12242": "Shorts", "12243": "SportCoatsJackets", "13354": "RainUmbrellas", "13409": "ParasolsPersonalSunUmbrellas", "13701": "KeyChainsKeyRings", "13778": "MensClothing", "13779": "WomensClothing", "13780": "ChildrensClothing", "10568": "Podcasts", "10569": "Webcasts", "10608": "BookReviews", "10609": "BookRetailers", "10611": "EBooks", "11868": "MensInterestsMediaPublicationsMature", "11875": "FashionStylePublications", "11876": "ApparelTradePublications", "12972": "EBookPublishing", "12973": "SelfPublishing", "13429": "Dictionaries", "13430": "Encyclopedias", "13432": "Translation", "13445": "ChildrensBooks", "13600": "GeographicReference", "13660": "Poetry", "13692": "OnlineImageGalleries", "13716": "PublicRecords", "13785": "FlashBasedEntertainment", "13801": "AudioBooks", "13861": "Quotations", "13811": "GarageEstateYardSales"}}, "NameToDbValues": {"Vehicles/MotorVehicles/OffRoadVehicles": "10181", "Vehicles/MotorVehicles/Motorcycles": "10182", "Vehicles/MotorVehicles/CampersRVs": "10183", "Vehicles/MotorVehicles/VehicleTrailers": "10184", "Vehicles/MotorVehicles/CarsTrucks": "10185", "Vehicles/MotorVehicles/CommercialVehicles": "10186", "Vehicles/MotorVehicles/ScootersMopeds": "10187", "Vehicles/PersonalAirplanesAircraft/FlyingInstruction": "10189", "Vehicles/PersonalAirplanesAircraft/PersonalAircraftRepairMaintenance": "10200", "Vehicles/VehiclePartsAccessories/PersonalAirplaneAircraftPartsAccessories": "10201", "Vehicles/VehiclePartsAccessories/OffRoadVehiclePartsAccessories": "10202", "Vehicles/VehiclePartsAccessories/TruckPartsAccessories": "10203", "Vehicles/VehiclePartsAccessories/MotorcyclePartsAccessories": "10204", "Vehicles/VehiclePartsAccessories/VehicleCleansersCareSupplies": "10205", "Vehicles/VehiclePartsAccessories/VehicleTiresTireParts": "10206", "Vehicles/VehiclePartsAccessories/VehicleRacks": "10207", "Vehicles/VehiclePartsAccessories/ScooterMopedPartsAccessories": "10208", "Vehicles/VehiclePartsAccessories/CarPartsAccessories": "10209", "Vehicles/BoatsWatercraft/Yachts": "10210", "Vehicles/BoatsWatercraft/FishingBoats": "10211", "Vehicles/BoatsWatercraft/PersonalWatercraft": "10212", "Vehicles/BoatsWatercraft/Sailboats": "10213", "Vehicles/BoatsWatercraft/Rafts": "10214", "Vehicles/BoatsWatercraft/RowBoatsCanoes": "10215", "Vehicles/BoatsWatercraft/Kayaks": "10216", "Vehicles/BoatsWatercraft/SpeedboatsMotorboats": "10217", "Vehicles/VehicleRepairMaintenance/BrakeRepair": "10967", "Vehicles/VehicleRepairMaintenance/WindshieldRepair": "10968", "Vehicles/VehicleRepairMaintenance/OilChanges": "10969", "Vehicles/VehicleRepairMaintenance/TuneUps": "10971", "Vehicles/VehicleRepairMaintenance/TransmissionsTransmissionRepair": "10972", "Vehicles/VehicleRepairMaintenance/CollisionAutoBodyRepair": "10973", "Vehicles/VehicleTowing/BoatTowing": "10974", "Vehicles/VehicleRepairMaintenance/VehicleWashingDetailing": "10979", "Vehicles/VehicleRepairMaintenance/VehiclePainting": "10981", "Vehicles/VehicleDealers/CarDealers": "13656", "Vehicles/VehicleDealers/BoatYachtSalesBrokers": "13657", "Vehicles/MotorVehicles/UsedMotorVehicles": "13714", "FamilyCommunity/FaithBelief/OccultParanormal": "10127", "FamilyCommunity/CommunityServiceSocialOrganizations/VolunteerOpportunities": "10218", "FamilyCommunity/CommunityServiceSocialOrganizations/DisasterReliefEmergencyManagement": "10219", "FamilyCommunity/CommunityServiceSocialOrganizations/DisabilityResources": "10220", "FamilyCommunity/CommunityServiceSocialOrganizations/EthnicIdentityResources": "10221", "FamilyCommunity/CommunityServiceSocialOrganizations/ConsumerResources": "10222", "FamilyCommunity/CommunityServiceSocialOrganizations/AIDSHIVSupport": "10223", "FamilyCommunity/CommunityServiceSocialOrganizations/AlcoholSubstanceAbuse": "10224", "FamilyCommunity/CommunityServiceSocialOrganizations/TeenAdviceSupport": "10225", "FamilyCommunity/CommunityServiceSocialOrganizations/GLBTResources": "10226", "FamilyCommunity/CommunityServiceSocialOrganizations/SocialIssuesAdvocacy": "10227", "FamilyCommunity/CommunityServiceSocialOrganizations/YouthOrganizationsResources": "10228", "FamilyCommunity/CommunityServiceSocialOrganizations/CommunityOutreach": "10229", "FamilyCommunity/CommunityServiceSocialOrganizations/NonprofitServices": "10230", "FamilyCommunity/DomesticPersonalResources/PersonalErrandConciergeServices": "10233", "FamilyCommunity/DomesticPersonalResources/PersonalChefs": "10234", "FamilyCommunity/BabyParentingFamily/Baby": "10235", "FamilyCommunity/BabyParentingFamily/ParentingFamily": "10236", "FamilyCommunity/RomanceRelationships/RelationshipAdvice": "10237", "FamilyCommunity/RomanceRelationships/DivorceSeparation": "10238", "FamilyCommunity/RomanceRelationships/DatingMarriageMatching": "10240", "FamilyCommunity/RomanceRelationships/TroubledRelationships": "10242", "FamilyCommunity/CommunityServiceSocialOrganizations/SelfDefenseTraining": "11063", "FamilyCommunity/CommunityServiceSocialOrganizations/Environmentalism": "11077", "FamilyCommunity/CommunityServiceSocialOrganizations/SocialInsuranceProgramsEntitlements": "13392", "FamilyCommunity/CommunityServiceSocialOrganizations/SelectiveService": "13393", "FamilyCommunity/FaithBelief/PlacesofWorshipWorshipServices": "13434", "FamilyCommunity/FaithBelief/ReligiousInstruction": "13435", "FamilyCommunity/FaithBelief/ReligiousMediaPublications": "13436", "FamilyCommunity/FaithBelief/AstrologyHoroscopes": "13469", "FamilyCommunity/FaithBelief/Christianity": "13472", "FamilyCommunity/FaithBelief/Islam": "13473", "FamilyCommunity/FaithBelief/Buddhism": "13474", "FamilyCommunity/FaithBelief/Hinduism": "13475", "FamilyCommunity/FaithBelief/Judaism": "13476", "FamilyCommunity/FaithBelief/WiccaEsotericReligions": "13477", "FamilyCommunity/FaithBelief/Scientology": "13479", "FamilyCommunity/CommunityServiceSocialOrganizations/MultilateralNonGovernmentalOrganizations": "13599", "FamilyCommunity/FaithBelief/SelfHelpMotivational": "13703", "FamilyCommunity/CommunityServiceSocialOrganizations/SeniorResources": "13709", "FamilyCommunity/FaithBelief/Prayers": "13807", "FamilyCommunity/FaithBelief/Meditation": "13836", "RealEstate/RealEstateListings/RentalListings": "10032", "RealEstate/PropertyInspectionsAppraisals/LandSurveying": "10247", "RealEstate/PropertyInspectionsAppraisals/HomeAppraisals": "10248", "RealEstate/RelocationHouseholdMoving/TemporaryHousingServices": "10249", "RealEstate/RelocationHouseholdMoving/MovingTruckVanRental": "10250", "RealEstate/RelocationHouseholdMoving/MovingSupplies": "10251", "RealEstate/RelocationHouseholdMoving/StorageRentalServices": "10252", "RealEstate/RelocationHouseholdMoving/ConsumerVehicleShippingServices": "10253", "RealEstate/PrivateCommunities/SharedInterestLivingCommunities": "10254", "RealEstate/PrivateCommunities/GolfCommunities": "10255", "RealEstate/VacationPropertiesSecondHomes/Timeshares": "10256", "RealEstate/PropertyDevelopment/CommercialPropertyDevelopment": "10257", "RealEstate/PropertyDevelopment/NewHomesCustomHomes": "10258", "RealEstate/EscrowRealEstateTitling/RealEstateTitlingLandRegistry": "10259", "RealEstate/EscrowRealEstateTitling/EscrowServices": "10260", "RealEstate/RealEstateListings/SeniorLivingListings": "10261", "RealEstate/RealEstateListings/RentToOwnRealEstateListings": "10262", "RealEstate/RealEstateListings/CommercialRealEstateListings": "10264", "RealEstate/RealEstateListings/GovernmentAssistanceHomeListingServices": "10265", "RealEstate/RealEstateListings/LandAcreageListings": "10266", "RealEstate/RealEstateListings/ForSaleByOwnerListings": "10267", "RealEstate/RealEstateListings/CondoCoopListings": "13688", "RealEstate/RealEstateListings/MobileManufacturedHomeListings": "13689", "RealEstate/RealEstateListings/BankOwnedForeclosedListings": "13690", "BusinessIndustrial/Manufacturing/ConsumerGoodsManufacturing": "10271", "BusinessIndustrial/IndustrialGoodsManufacturing/IndustrialManufacturing": "10281", "BusinessIndustrial/IndustrialGoodsManufacturing/TextilesTextileServices": "10298", "BusinessIndustrial/IndustrialGoodsManufacturing/IndustrialElectronics": "10302", "BusinessIndustrial/IndustrialGoodsManufacturing/PlantFacility": "10306", "BusinessIndustrial/ScientificEquipmentServices/Bioremediation": "11047", "BusinessIndustrial/ScientificEquipmentServices/MaterialsTesting": "11054", "BusinessIndustrial/SecurityEquipmentServices/BodyGuards": "11062", "BusinessIndustrial/SecurityEquipmentServices/SecurityGuards": "11065", "BusinessIndustrial/SecurityEquipmentServices/SecurityAlarmServices": "11066", "BusinessIndustrial/IndustrialGoodsManufacturing/OccupationalHealthSafety": "11067", "BusinessIndustrial/DesignEngineering/GraphicDesign": "11068", "BusinessIndustrial/DesignEngineering/DraftingServices": "11069", "BusinessIndustrial/Agriculture/AquacultureFisheryServices": "11073", "BusinessIndustrial/Agriculture/AgriculturalResearch": "11074", "BusinessIndustrial/Agriculture/AgriculturalTestingAnalysis": "11076", "BusinessIndustrial/Agriculture/Horticulture": "11078", "BusinessIndustrial/Agriculture/Forestry": "11083", "BusinessIndustrial/BusinessManagement/HumanResources": "11084", "BusinessIndustrial/BusinessManagement/MailOrderServices": "11085", "BusinessIndustrial/BusinessManagement/BusinessStrategyPlanning": "11086", "BusinessIndustrial/BusinessManagement/AdvertisingMarketing": "11088", "BusinessIndustrial/BusinessManagement/BusinessProcessAnalysis": "11089", "BusinessIndustrial/BusinessManagement/CorporateEvents": "11090", "BusinessIndustrial/BusinessManagement/CustomerSupportOutreach": "11092", "BusinessIndustrial/BusinessManagement/TechnologyConsulting": "11094", "BusinessIndustrial/BusinessManagement/PhysicalAssetManagement": "11095", "BusinessIndustrial/BusinessManagement/BusinessSecurityInvestigation": "11096", "BusinessIndustrial/BusinessManagement/SupplyChainManagement": "11097", "BusinessIndustrial/BusinessManagement/ManagementConsulting": "11098", "BusinessIndustrial/BusinessManagement/OfficeFacilitiesManagement": "11099", "BusinessIndustrial/BusinessManagement/Transcription": "11100", "BusinessIndustrial/ShippingPacking/Warehousing": "11102", "BusinessIndustrial/ShippingPacking/Freighting": "11103", "BusinessIndustrial/ShippingPacking/ExpressExpeditedShipping": "11104", "BusinessIndustrial/DesignEngineering/Architecture": "11130", "BusinessIndustrial/BuildingConstructionMaintenance/ExcavationServices": "11132", "BusinessIndustrial/BuildingConstructionMaintenance/ChimneyServices": "11133", "BusinessIndustrial/BuildingConstructionMaintenance/BuildingPaintingServices": "11138", "BusinessIndustrial/BuildingConstructionMaintenance/CommercialGeneralContracting": "11141", "BusinessIndustrial/BuildingConstructionMaintenance/ElectricianServices": "11143", "BusinessIndustrial/BuildingConstructionMaintenance/ConstructionEstimation": "11145", "BusinessIndustrial/BuildingConstructionMaintenance/DemolitionServices": "11148", "BusinessIndustrial/BuildingConstructionMaintenance/PropertyInspectionCoding": "11149", "BusinessIndustrial/BuildingConstructionMaintenance/LandscapingLandscapeDesign": "11150", "BusinessIndustrial/BuildingConstructionMaintenance/BuildingRestorationPreservation": "11151", "BusinessIndustrial/BuildingConstructionMaintenance/LocksLocksmiths": "11153", "BusinessIndustrial/BuildingConstructionMaintenance/ConstructionConsulting": "11154", "BusinessIndustrial/FoodServiceIndustry/RestaurantSuppliers": "11155", "BusinessIndustrial/FoodServiceIndustry/FoodPackingBottling": "11156", "BusinessIndustrial/FoodServiceIndustry/FoodImportExport": "11159", "BusinessIndustrial/FoodServiceIndustry/FoodConsulting": "11160", "BusinessIndustrial/FoodServiceIndustry/FoodMarketing": "11161", "BusinessIndustrial/FoodServiceIndustry/FoodDistribution": "11162", "BusinessIndustrial/ChemicalIndustry/FoodScience": "11163", "BusinessIndustrial/CommercialIndustrialPrinting/CheckPrinting": "11168", "BusinessIndustrial/CommercialIndustrialPrinting/BookPrinting": "11169", "BusinessIndustrial/CommercialIndustrialPrinting/ScreenPrinting": "11171", "BusinessIndustrial/EnergyIndustry/EnergyManagement": "11173", "BusinessIndustrial/EnergyIndustry/EnergyPriceComparison": "11174", "BusinessIndustrial/EnergyIndustry/EnergyResearch": "11175", "BusinessIndustrial/EnergyIndustry/EnergyConsulting": "11176", "BusinessIndustrial/RetailTrade/LossPrevention": "11178", "BusinessIndustrial/RetailTrade/Vending": "11179", "BusinessIndustrial/RetailTrade/MerchantServicesPaymentSystems": "11180", "BusinessIndustrial/RetailTrade/Inventorying": "11181", "BusinessIndustrial/Agriculture/Crops": "11188", "BusinessIndustrial/Agriculture/Irrigation": "11190", "BusinessIndustrial/Agriculture/Livestock": "11192", "BusinessIndustrial/Agriculture/AgriculturalEquipment": "11194", "BusinessIndustrial/IndustrialGoodsManufacturing/Metals": "11198", "BusinessIndustrial/IndustrialGoodsManufacturing/Minerals": "11202", "BusinessIndustrial/RetailTrade/RetailSignsBanners": "11205", "BusinessIndustrial/RetailTrade/HandTrucksUtilityCarts": "11206", "BusinessIndustrial/RetailTrade/BillCoinCounters": "11207", "BusinessIndustrial/RetailTrade/BarCodeScanners": "11208", "BusinessIndustrial/RetailTrade/TurnstilesTrafficControlDevices": "11209", "BusinessIndustrial/RetailTrade/ApparelRetailSupplies": "11210", "BusinessIndustrial/RetailTrade/PaperPlasticBags": "11211", "BusinessIndustrial/RetailTrade/PegboardSlatwall": "11214", "BusinessIndustrial/RetailTrade/FloorMats": "11216", "BusinessIndustrial/RetailTrade/ShrinkWrappingSystems": "11217", "BusinessIndustrial/RetailTrade/RetailDisplaySupplies": "11219", "BusinessIndustrial/RetailTrade/TimeClocksTimeCards": "11220", "BusinessIndustrial/RetailTrade/CounterfeitMoneyDetectors": "11224", "BusinessIndustrial/RetailTrade/AshTrayReceptacles": "11227", "BusinessIndustrial/RetailTrade/TaggingGuns": "11229", "BusinessIndustrial/RetailTrade/JewelryBagsBoxesDisplays": "11230", "BusinessIndustrial/RetailTrade/ShoppingBasketsCarts": "11231", "BusinessIndustrial/RetailTrade/MerchantPointofSaleSystems": "11232", "BusinessIndustrial/RetailTrade/CashRegistersCashRegisterSupplies": "11233", "BusinessIndustrial/ChemicalIndustry/ChemicalsChemicalProducts": "11250", "BusinessIndustrial/ScientificEquipmentServices/BiotechnologyEquipment": "11252", "BusinessIndustrial/ScientificEquipmentServices/ScientificBooksJournals": "11253", "BusinessIndustrial/ScientificEquipmentServices/LaboratoryEquipmentSupplies": "11254", "BusinessIndustrial/ScientificEquipmentServices/ScientificGlasswareAccessories": "11255", "BusinessIndustrial/EnergyIndustry/IndustrialMagnets": "11259", "BusinessIndustrial/EnergyIndustry/Generators": "11263", "BusinessIndustrial/BuildingConstructionMaintenance/EarthMoversConstructionTractors": "11275", "BusinessIndustrial/BuildingConstructionMaintenance/ConstructionTrailers": "11276", "BusinessIndustrial/BuildingConstructionMaintenance/Handrails": "11277", "BusinessIndustrial/BuildingConstructionMaintenance/KitchenBathroomCounters": "11278", "BusinessIndustrial/BuildingConstructionMaintenance/PrefabricatedBuildings": "11279", "BusinessIndustrial/BuildingConstructionMaintenance/HousePaintsStains": "11280", "BusinessIndustrial/BuildingConstructionMaintenance/HVACClimateControl": "11284", "BusinessIndustrial/BuildingConstructionMaintenance/Roofing": "11285", "BusinessIndustrial/BuildingConstructionMaintenance/HardwareToolsAccessories": "11287", "BusinessIndustrial/BuildingConstructionMaintenance/Masonry": "11292", "BusinessIndustrial/BuildingConstructionMaintenance/InstallationLighting": "11293", "BusinessIndustrial/BuildingConstructionMaintenance/DeckingProductsEquipment": "11297", "BusinessIndustrial/BuildingConstructionMaintenance/RebarRebarAccessories": "11298", "BusinessIndustrial/Agriculture/NaturalFibers": "11301", "BusinessIndustrial/Office/Paper": "11305", "BusinessIndustrial/SecurityEquipmentServices/Safes": "11309", "BusinessIndustrial/SecurityEquipmentServices/NightVisionGogglesScopes": "11310", "BusinessIndustrial/SecurityEquipmentServices/ElectronicVoiceChangers": "11311", "BusinessIndustrial/SecurityEquipmentServices/SurveillanceEquipment": "11313", "BusinessIndustrial/SecurityEquipmentServices/Intercoms": "11316", "BusinessIndustrial/SecurityEquipmentServices/WeatherAlertRadios": "11320", "BusinessIndustrial/SecurityEquipmentServices/SecurityWeapons": "11321", "BusinessIndustrial/SecurityEquipmentServices/CarAlarmSystems": "11322", "BusinessIndustrial/Office/OfficeFurniture": "11323", "BusinessIndustrial/Office/LanyardsBadgeHolders": "11324", "BusinessIndustrial/Office/BusinessCardsStationeryForms": "11325", "BusinessIndustrial/Office/PaperClipsRubberBands": "11326", "BusinessIndustrial/ShippingPacking/StampsEnvelopes": "11327", "BusinessIndustrial/Office/Calculators": "11328", "BusinessIndustrial/Office/GluePaste": "11329", "BusinessIndustrial/Office/StaplersScissorsPunchers": "11330", "BusinessIndustrial/Office/DeskOrganizers": "11332", "BusinessIndustrial/Office/PortfoliosPresentationCases": "11333", "BusinessIndustrial/Office/CalendarsPlanners": "11334", "BusinessIndustrial/Office/PrinterAccessories": "11335", "BusinessIndustrial/Office/LabelsLabeling": "11336", "BusinessIndustrial/Office/MicrocassetteRecorders": "11337", "BusinessIndustrial/Office/BindersBindingLaminating": "11338", "BusinessIndustrial/Office/OfficeAppliances": "11339", "BusinessIndustrial/FoodServiceIndustry/BeverageBarSupplies": "11340", "BusinessIndustrial/FoodServiceIndustry/DishDispensers": "11341", "BusinessIndustrial/FoodServiceIndustry/SteamTablesFoodWarmers": "11342", "BusinessIndustrial/FoodServiceIndustry/IndustrialRefrigeration": "11343", "BusinessIndustrial/FoodServiceIndustry/SaladColdFoodBars": "11344", "BusinessIndustrial/FoodServiceIndustry/CommercialFoodPreparationEquipment": "11345", "BusinessIndustrial/FoodServiceIndustry/ConcessionEquipment": "11346", "BusinessIndustrial/FoodServiceIndustry/ChafingDishes": "11347", "BusinessIndustrial/FoodServiceIndustry/IceMachines": "11348", "BusinessIndustrial/FoodServiceIndustry/WorkDishTables": "11349", "BusinessIndustrial/FoodServiceIndustry/FoodDisplayCases": "11352", "BusinessIndustrial/FoodServiceIndustry/FoodTransportCarts": "11353", "BusinessIndustrial/FoodServiceIndustry/FoodScales": "11354", "BusinessIndustrial/FoodServiceIndustry/IndustrialFoodStorage": "11355", "BusinessIndustrial/DesignEngineering/DraftingBoardsTables": "11367", "BusinessIndustrial/DesignEngineering/DraftingEquipment": "11369", "BusinessIndustrial/Office/PensWritingInstruments": "11370", "BusinessIndustrial/ShippingPacking/TapeTapeDispensers": "11373", "BusinessIndustrial/ShippingPacking/CargoContainers": "11374", "BusinessIndustrial/ShippingPacking/MailingTubes": "11376", "BusinessIndustrial/ShippingPacking/StrappingEquipmentDevices": "11377", "BusinessIndustrial/RetailTrade/CDDVDJewelboxesCases": "11380", "BusinessIndustrial/ShippingPacking/PackingCushioningPackingPaper": "11381", "BusinessIndustrial/ShippingPacking/ShippingBoxes": "11382", "BusinessIndustrial/EnergyIndustry/WasteManagement": "11388", "BusinessIndustrial/CommercialIndustrialPrinting/CDDVDDuplicationPrinting": "11392", "BusinessIndustrial/CommercialIndustrialPrinting/UVCuring": "11394", "BusinessIndustrial/CommercialIndustrialPrinting/PadPrinting": "11396", "BusinessIndustrial/CommercialIndustrialPrinting/LargeFormatPrinters": "11397", "BusinessIndustrial/JanitorialProductsServices/JanitorialSigns": "11398", "BusinessIndustrial/JanitorialProductsServices/TouchFreeDevices": "11399", "BusinessIndustrial/JanitorialProductsServices/FloorCleaning": "11400", "BusinessIndustrial/JanitorialProductsServices/ElectricHandDryers": "11401", "BusinessIndustrial/JanitorialProductsServices/JanitorialCarts": "11402", "BusinessIndustrial/JanitorialProductsServices/UrinalSupplies": "11403", "BusinessIndustrial/JanitorialProductsServices/WasteReceptacles": "11404", "BusinessIndustrial/JanitorialProductsServices/OdorControlDevices": "11406", "BusinessIndustrial/JanitorialProductsServices/TowelDispensers": "11407", "BusinessIndustrial/JanitorialProductsServices/SoapDispensers": "11408", "BusinessIndustrial/FoodServiceIndustry/CateringBartending": "11485", "BusinessIndustrial/FoodServiceIndustry/CulinarySchoolsCourses": "12052", "BusinessIndustrial/EnergyIndustry/RenewableAlternativeEnergy": "12110", "BusinessIndustrial/EnergyIndustry/OilGas": "12113", "BusinessIndustrial/ChemicalIndustry/Biochemistry": "12345", "BusinessIndustrial/Agriculture/Agrochemicals": "12552", "BusinessIndustrial/BuildingConstructionMaintenance/TilesTiling": "12597", "BusinessIndustrial/CommercialIndustrialPrinting/PrintingPresses": "12708", "BusinessIndustrial/BusinessManagement/BusinessOpportunities": "13411", "BusinessIndustrial/BuildingConstructionMaintenance/Plumbing": "13462", "BusinessIndustrial/ShippingPacking/CouriersMessengers": "13604", "BusinessIndustrial/FishingIndustry/CommercialFishing": "13677", "BusinessIndustrial/ShippingPacking/MailPackageDelivery": "13724", "BusinessIndustrial/BuildingConstructionMaintenance/EcoFriendlyBuildingConstruction": "13731", "BusinessIndustrial/DesignEngineering/IndustrialSystemsEngineering": "13732", "BusinessIndustrial/DesignEngineering/IndustrialProductDesign": "13733", "BusinessIndustrial/DesignEngineering/InteriorDesign": "13734", "BusinessIndustrial/BuildingConstructionMaintenance/BuildingMaterialsSupplies": "13797", "BusinessIndustrial/BuildingConstructionMaintenance/UrbanRegionalPlanning": "13848", "BusinessIndustrial/BusinessManagement/ECommerce": "13854", "BusinessIndustrial/DesignEngineering/CivilEngineering": "13868", "BeautyPersonalCare/SkinCare/FaceCare": "10061", "BeautyPersonalCare/SkinCare/BodyCleansing": "10309", "BeautyPersonalCare/SkinCare/BodyLotionsOils": "10310", "BeautyPersonalCare/TanningSunCare/SunLotionsSunscreen": "10311", "BeautyPersonalCare/TanningSunCare/TanningServices": "10312", "BeautyPersonalCare/TanningSunCare/TanningBeds": "10313", "BeautyPersonalCare/SpaMedicalSpa/AtHomeSpaEquipment": "10314", "BeautyPersonalCare/BodyArt/TattooRemoval": "10315", "BeautyPersonalCare/SpaMedicalSpa/AromatherapyEssentialOils": "10316", "BeautyPersonalCare/SpaMedicalSpa/CosmeticProcedures": "10317", "BeautyPersonalCare/NailCare/NailPolishNailPolishAccessories": "10319", "BeautyPersonalCare/NailCare/NailCareTools": "10320", "BeautyPersonalCare/NailCare/NailFungusProducts": "10321", "BeautyPersonalCare/NailCare/ManicuresPedicures": "10322", "BeautyPersonalCare/MakeUpCosmetics/LipsticksLipGlosses": "10323", "BeautyPersonalCare/MakeUpCosmetics/EyeMakeUp": "10324", "BeautyPersonalCare/MakeUpCosmetics/Blush": "10325", "BeautyPersonalCare/MakeUpCosmetics/FacePowder": "10327", "BeautyPersonalCare/MakeUpCosmetics/Concealer": "10329", "BeautyPersonalCare/MakeUpCosmetics/Foundation": "10330", "BeautyPersonalCare/MakeUpCosmetics/MakeUpBrushesTools": "10331", "BeautyPersonalCare/HairCare/WigsWigAccessories": "10332", "BeautyPersonalCare/HairCare/DamagedHair": "10335", "BeautyPersonalCare/HairCare/HairStraightenersRelaxers": "10336", "BeautyPersonalCare/HairCare/HairDyesColoring": "10337", "BeautyPersonalCare/HairCare/ShampoosConditioners": "10338", "BeautyPersonalCare/HairCare/HairStyling": "10339", "BeautyPersonalCare/ShavingGrooming/PersonalGroomingKitsEquipment": "10342", "BeautyPersonalCare/ShavingGrooming/UnwantedBodyFacialHairRemoval": "10343", "BeautyPersonalCare/OralCare/CankerSoreTreatments": "10345", "BeautyPersonalCare/OralCare/TeethWhitening": "10346", "BeautyPersonalCare/OralCare/DentalFlossGumHealth": "10347", "BeautyPersonalCare/OralCare/Mouthwash": "10348", "BeautyPersonalCare/OralCare/DentalCareGum": "10349", "BeautyPersonalCare/OralCare/Toothpaste": "10350", "BeautyPersonalCare/OralCare/ToothacheRelief": "10351", "BeautyPersonalCare/OralCare/ToothbrushesToothbrushAccessories": "10352", "BeautyPersonalCare/OralCare/DentureCare": "10353", "BeautyPersonalCare/HygieneToiletries/CottonSwabsBallsPads": "10354", "BeautyPersonalCare/HygieneToiletries/AntiperspirantsDeodorantsBodySpray": "10355", "BeautyPersonalCare/HygieneToiletries/FeminineHygieneProducts": "10356", "BeautyPersonalCare/HairCare/HairLoss": "10477", "BeautyPersonalCare/BodyArt/BodyJewelryPiercings": "10898", "BeautyPersonalCare/FashionStyle/VirtualMakeovers": "11414", "BeautyPersonalCare/ShavingGrooming/RazorsShavers": "11425", "BeautyPersonalCare/AntiAging/AntiAgingCreamsMoisturizers": "11434", "BeautyPersonalCare/SpaMedicalSpa/Saunas": "13446", "BeautyPersonalCare/FashionStyle/FashionDesignersCollections": "13637", "BeautyPersonalCare/FashionStyle/FashionModeling": "13638", "BeautyPersonalCare/SpaMedicalSpa/DaySpasSalonSpas": "13704", "BeautyPersonalCare/SpaMedicalSpa/DestinationResortSpas": "13705", "BeautyPersonalCare/SkinCare/ExfoliantsScrubs": "13850", "OccasionsGifts/CardsGreetings/StationeryStationerySets": "10361", "OccasionsGifts/HolidaysSeasonalEvents/HolidaySeasonalDecorations": "10363", "OccasionsGifts/SpecialOccasions/FuneralsBereavement": "10366", "OccasionsGifts/Gifts/GiftWrapRibbons": "10367", "OccasionsGifts/SpecialOccasions/Weddings": "10369", "OccasionsGifts/Gifts/GiftRegistries": "10370", "OccasionsGifts/Gifts/GiftDelivery": "10373", "OccasionsGifts/CardsGreetings/PhotoCards": "11443", "OccasionsGifts/CardsGreetings/ECards": "11448", "OccasionsGifts/Gifts/GourmetFoodGifts": "11458", "OccasionsGifts/Gifts/GiftBaskets": "11461", "OccasionsGifts/PartiesPartySupplies/PartyDecorations": "11466", "OccasionsGifts/PartiesPartySupplies/PartyFavors": "11467", "OccasionsGifts/PartiesPartySupplies/PaperPlatesNapkinsDisposableCutlery": "11468", "OccasionsGifts/PartiesPartySupplies/PartyInvitations": "11469", "OccasionsGifts/SpecialOccasions/WeddingSpecialOccasionPhotoVideo": "11483", "OccasionsGifts/Gifts/GiftCards": "12966", "OccasionsGifts/Gifts/EngravingJewelryEngraving": "13468", "OccasionsGifts/SpecialOccasions/Anniversaries": "13501", "OccasionsGifts/SpecialOccasions/BirthdaysNameDays": "13502", "OccasionsGifts/SpecialOccasions/BabyShowersNewBaby": "13503", "OccasionsGifts/SpecialOccasions/Graduation": "13504", "OccasionsGifts/SpecialOccasions/ReligiousOccasions": "13505", "OccasionsGifts/SpecialOccasions/GetWell": "13506", "OccasionsGifts/HolidaysSeasonalEvents/MothersDayFathersDay": "13507", "OccasionsGifts/HolidaysSeasonalEvents/ValentinesDay": "13510", "OccasionsGifts/HolidaysSeasonalEvents/Christmas": "13738", "OccasionsGifts/HolidaysSeasonalEvents/Easter": "13739", "OccasionsGifts/HolidaysSeasonalEvents/HalloweenOctober31st": "13740", "OccasionsGifts/HolidaysSeasonalEvents/JewishHolidays": "13741", "OccasionsGifts/HolidaysSeasonalEvents/Thanksgiving": "13742", "OccasionsGifts/HolidaysSeasonalEvents/CarnivalMardiGras": "13786", "OccasionsGifts/HolidaysSeasonalEvents/NewYears": "13787", "InternetTelecom/Internet/InternetServicePlans": "11491", "InternetTelecom/Internet/OnlinePhotoVideoSharing": "11492", "InternetTelecom/Internet/WebDesignDevelopment": "11493", "InternetTelecom/Internet/BloggingResourcesServices": "11494", "InternetTelecom/Internet/DirectoriesListings": "11495", "InternetTelecom/Internet/SocialNetworksOnlineCommunities": "11496", "InternetTelecom/Internet/SearchEngines": "11498", "InternetTelecom/Internet/ForumChatServices": "11499", "InternetTelecom/Internet/OnlineMaps": "11500", "InternetTelecom/Internet/FileSharingHosting": "11502", "InternetTelecom/Internet/OnlineAuctioningServices": "11503", "InternetTelecom/Internet/WebHosting": "11504", "InternetTelecom/Telephony/PhoneRentalServices": "11505", "InternetTelecom/Telephony/TelephonyEquipmentDonation": "11508", "InternetTelecom/Telephony/TelephoneMaintenance": "12155", "InternetTelecom/VOIP/VOIPTelephones": "12168", "InternetTelecom/Telephony/AnsweringMachines": "12169", "InternetTelecom/Telephony/LandlinePhonesAccessories": "12171", "InternetTelecom/CableServices/CableTV": "12766", "InternetTelecom/SatelliteServices/SatelliteTV": "12767", "InternetTelecom/Internet/NetworkSecurity": "13088", "InternetTelecom/Internet/EmailMessaging": "13094", "InternetTelecom/Telephony/MobilePhonesAccessories": "13381", "InternetTelecom/Telephony/VoicemailServicesCallingFeatures": "13415", "InternetTelecom/Telephony/PhonePlans": "13419", "InternetTelecom/Internet/WebPortals": "13725", "InternetTelecom/Internet/DomainNamesDomainNameRegistration": "13768", "HomeGarden/HomeFurniture/FurnitureRefinishingRepairReupholstery": "10402", "HomeGarden/HomeDecorInteriorDecorating/ResidentialCeilingFans": "10409", "HomeGarden/HomeDecorInteriorDecorating/WindowTreatments": "10416", "HomeGarden/ResidentialCleaning/UpholsteryCleaning": "11522", "HomeGarden/ResidentialCleaning/HouseCleaningServices": "11523", "HomeGarden/ResidentialCleaning/RugCarpetCleaning": "11524", "HomeGarden/ResidentialCleaning/WindowCoveringCleaning": "11525", "HomeGarden/HomeAppliances/HomeApplianceServiceRepair": "11527", "HomeGarden/HomeDecorInteriorDecorating/WallPaper": "11528", "HomeGarden/HomeDecorInteriorDecorating/CeramicsPorcelainVases": "11529", "HomeGarden/HomeDecorInteriorDecorating/DecorativeFountains": "11530", "HomeGarden/HomeDecorInteriorDecorating/Clocks": "11531", "HomeGarden/HomeDecorInteriorDecorating/FlowerPotsPlanters": "11532", "HomeGarden/HomeDecorInteriorDecorating/CandlesIncense": "11533", "HomeGarden/HomeDecorInteriorDecorating/Mirrors": "11534", "HomeGarden/HomeDecorInteriorDecorating/DecorativeThrowsPillows": "11535", "HomeGarden/HomeDecorInteriorDecorating/ArtGlass": "11536", "HomeGarden/YardGardenPatio/Fencing": "11537", "HomeGarden/YardGardenPatio/SwimmingPoolsSpas": "11538", "HomeGarden/YardGardenPatio/YardPatioFurnitureAccessories": "11540", "HomeGarden/YardGardenPatio/OutdoorCookingEquipmentAccessories": "11541", "HomeGarden/YardGardenPatio/YardGardenEquipmentSupplies": "11543", "HomeGarden/YardGardenPatio/RetainingWalls": "11546", "HomeGarden/HomeStorageOrganization/ClosetOrganizingComponentsSystems": "11547", "HomeGarden/HomeStorageOrganization/WineRacksStorageSystems": "11549", "HomeGarden/HomeStorageOrganization/LaundryStorageOrganization": "11550", "HomeGarden/KitchenDining/CanningPreserving": "11551", "HomeGarden/KitchenDining/PlatesServingDishes": "11552", "HomeGarden/KitchenDining/Silverware": "11554", "HomeGarden/KitchenDining/KitchenUtensilsGadgets": "11555", "HomeGarden/KitchenDining/CookwareCookwareSets": "11556", "HomeGarden/KitchenDining/Bakeware": "11557", "HomeGarden/KitchenDining/KitchenLinens": "11558", "HomeGarden/KitchenDining/CutleryCuttingAccessories": "11560", "HomeGarden/HomeSafetySecurity/LockboxesHideAKeys": "11561", "HomeGarden/HomeSafetySecurity/InHomeHazardDetectors": "11562", "HomeGarden/HomeSafetySecurity/FireExtinguishers": "11563", "HomeGarden/HomeAppliances/LaundryRoomAppliances": "11564", "HomeGarden/HomeAppliances/FloorCleanersAccessories": "11565", "HomeGarden/KitchenDining/SmallKitchenAppliances": "11566", "HomeGarden/HomeAppliances/MajorKitchenAppliances": "11567", "HomeGarden/HomeFurniture/KitchenDiningRoomFurniture": "11568", "HomeGarden/HomeFurniture/PlayroomChildrensFurniture": "11569", "HomeGarden/HomeFurniture/BathroomFurniture": "11570", "HomeGarden/HomeFurniture/BedroomFurniture": "11571", "HomeGarden/HomeFurniture/HomeOfficeFurniture": "11572", "HomeGarden/HomeFurniture/AntiqueFurniture": "11573", "HomeGarden/HomeFurniture/LivingRoomFurniture": "11574", "HomeGarden/Bathroom/BathroomTowels": "11575", "HomeGarden/Bathroom/ToiletAccessories": "11576", "HomeGarden/Bathroom/TowelBarsHooks": "11577", "HomeGarden/Bathroom/ShowerCurtainsAccessories": "11578", "HomeGarden/Bathroom/BathMatsRugs": "11579", "HomeGarden/Bathroom/BathroomAccessories": "11580", "HomeGarden/HomeLaundry/IroningBoardsIroningCenters": "11584", "HomeGarden/HomeHeatingCooling/DeskFansStandFans": "11585", "HomeGarden/HomeHeatingCooling/Thermostats": "11586", "HomeGarden/LightsLighting/LampsLampShades": "11587", "HomeGarden/LightsLighting/LightBulbs": "11588", "HomeGarden/BeddingLinens/DuvetCoversDuvets": "12823", "HomeGarden/BeddingLinens/Pillows": "12824", "HomeGarden/BeddingLinens/BlanketsBedspreads": "12825", "HomeGarden/BeddingLinens/SheetsPillowcases": "12826", "HomeGarden/LightsLighting/NightLights": "13139", "HomeGarden/YardGardenPatio/PestWeedControl": "13250", "HomeGarden/Bathroom/MedicineCabinets": "13363", "HomeGarden/KitchenDining/KitchenAccessories": "13370", "HomeGarden/KitchenDining/KitchenStorage": "13386", "HomeGarden/KitchenDining/CookieJarsBreadBoxes": "13387", "HomeGarden/YardGardenPatio/Mailboxes": "13408", "HomeGarden/BeddingLinens/Quilts": "13447", "HomeGarden/HomeDecorInteriorDecorating/PictureFrames": "13673", "HomeGarden/HomeHeatingCooling/FireplacesWoodStovesPelletStoves": "13708", "FoodGroceries/HouseholdSupplies/PaperTowelsHouseholdPaperProducts": "10421", "FoodGroceries/HouseholdSupplies/FoodWrapsFoodStorage": "10422", "FoodGroceries/HouseholdSupplies/HouseholdCleaningProducts": "10423", "FoodGroceries/HouseholdSupplies/CharcoalBarbecuingSupplies": "10424", "FoodGroceries/HouseholdSupplies/TrashBags": "10425", "FoodGroceries/Food/GrainsPastaSideDishes": "10426", "FoodGroceries/Food/JamsJelliesPreserves": "10427", "FoodGroceries/Food/WeightLossFoodsNutritionBars": "10428", "FoodGroceries/Food/PreparedFoods": "10429", "FoodGroceries/Food/CondimentsDressings": "10430", "FoodGroceries/Food/Sauces": "10431", "FoodGroceries/Food/BakedGoods": "10432", "FoodGroceries/Food/CookingOilsSpray": "10433", "FoodGroceries/Food/BakingIngredients": "10434", "FoodGroceries/Food/SnackFoods": "10435", "FoodGroceries/Food/Desserts": "10436", "FoodGroceries/Food/MeatPoultry": "10437", "FoodGroceries/Food/CandyGumMints": "10438", "FoodGroceries/Food/BreakfastFoods": "10439", "FoodGroceries/Food/DairyNonDairyAlternatives": "10440", "FoodGroceries/Food/Produce": "10441", "FoodGroceries/Food/SpecialRestrictedDietFoods": "10442", "FoodGroceries/Food/FishSeafood": "10443", "FoodGroceries/Beverages/NonAlcoholicBeverages": "10444", "FoodGroceries/Beverages/AlcoholicBeverages": "10445", "FoodGroceries/Food/HerbsSpices": "11613", "FoodGroceries/Food/OrganicNaturalFoods": "13695", "FoodGroceries/Food/NutButters": "13747", "FoodGroceries/Food/GourmetSpecialtyFoods": "13758", "FoodGroceries/Food/SoupsStewsBeans": "13843", "Health/HealthConditionsConcerns/AllergySinus": "10446", "Health/HealthConditionsConcerns/RespiratoryConditions": "10448", "Health/HealthConditionsConcerns/EatingDisorders": "10449", "Health/HealthConditionsConcerns/Obesity": "10450", "Health/HealthConditionsConcerns/CysticFibrosis": "10451", "Health/HealthConditionsConcerns/ColdSores": "10452", "Health/HealthConditionsConcerns/SmokingSmokingCessation": "10453", "Health/HealthConditionsConcerns/DigestiveHealthDisorders": "10456", "Health/HealthConditionsConcerns/WeightLoss": "10457", "Health/HealthConditionsConcerns/TraumaStressDisorder": "10458", "Health/HealthConditionsConcerns/Anemia": "10459", "Health/HealthConditionsConcerns/Vision": "10460", "Health/HealthConditionsConcerns/InjuryWoundCare": "10461", "Health/HealthConditionsConcerns/SexualReproductiveHealth": "10462", "Health/HealthConditionsConcerns/UrinaryBladderHealth": "10463", "Health/HealthConditionsConcerns/CancerCancerTreatment": "10464", "Health/HealthConditionsConcerns/GeriatricsAging": "10465", "Health/HealthConditionsConcerns/Hemorrhoids": "10466", "Health/HealthConditionsConcerns/Hepatitis": "10467", "Health/HealthConditionsConcerns/InfectiousDiseasesViruses": "10468", "Health/HealthConditionsConcerns/LearningDevelopmentalDisabilities": "10469", "Health/HealthConditionsConcerns/WomensHealthOBGYN": "10470", "Health/HealthConditionsConcerns/Epilepsy": "10471", "Health/HealthConditionsConcerns/EarNoseThroat": "10472", "Health/HealthConditionsConcerns/SkinConditionsSkinHealth": "10473", "Health/HealthConditionsConcerns/NeurologicalConditions": "10474", "Health/HealthConditionsConcerns/HeartHealth": "10475", "Health/HealthConditionsConcerns/AIDSHIV": "10476", "Health/HealthConditionsConcerns/InsomniaSleepDisorders": "10478", "Health/HealthConditionsConcerns/Arthritis": "10479", "Health/HealthConditionsConcerns/MensHealth": "10480", "Health/HealthConditionsConcerns/Lupus": "10482", "Health/HealthConditionsConcerns/BloodSugarDiabetes": "10483", "Health/HealthConditionsConcerns/MusculoskeletalConditions": "10484", "Health/HealthConditionsConcerns/FootHealth": "10485", "Health/HealthConditionsConcerns/HeadachePainManagement": "10486", "Health/HealthConditionsConcerns/DentalHealth": "10487", "Health/HealthConditionsConcerns/MentalHealth": "10488", "Health/NutritionDieting/DietsDietPrograms": "10491", "Health/NutritionDieting/NutritionCounseling": "10492", "Health/MedicalDevicesEquipmentSupplies/FirstAidSupplies": "10493", "Health/MedicalDevicesEquipmentSupplies/MobilityEquipmentAccessories": "10494", "Health/MedicalDevicesEquipmentSupplies/MedicalNeedlesSyringesInjectionSupplies": "10495", "Health/MedicalDevicesEquipmentSupplies/MedicationAccessories": "10496", "Health/BiotechPharmaceutical/PharmaceuticalManufacturing": "10499", "Health/BiotechPharmaceutical/ProteinIdentificationProteomics": "10501", "Health/ProfessionalMedicalResources/MedicalResearch": "10504", "Health/ProfessionalMedicalResources/ProfessionalHealthCareEducation": "10505", "Health/HealthCareServices/AlternativeNaturalMedicine": "10507", "Health/HealthCareServices/PlasticSurgery": "10508", "Health/HealthCareServices/LaboratoryTestingMedicalDiagnosticServices": "10509", "Health/HealthCareServices/AbortionServices": "10510", "Health/HealthCareServices/PhysicalRehabilitation": "10511", "Health/HealthCareServices/WomensHealthCareServices": "10512", "Health/HealthCareServices/GeneralHealthCareServices": "10513", "Health/HealthCareServices/DentistsDentalServices": "10514", "Health/HealthCareServices/VisionServices": "10515", "Health/HealthCareServices/MedicalSkinCareServices": "10516", "Health/HealthCareServices/GeriatricAgingCareServices": "10517", "Health/HealthCareServices/MentalHealthServices": "10518", "Health/HealthCareServices/OrthopedicsSportsMedicine": "10519", "Health/HealthCareServices/HospitalsHealthClinics": "10520", "Health/HealthCareServices/HospiceHomeNursingCare": "10521", "Health/HealthConditionsConcerns/PediatricsChildrensHealth": "11026", "Health/HealthCareServices/ImmunologyServices": "11046", "Health/BiotechPharmaceutical/ToxicologyServices": "11048", "Health/MedicalDevicesEquipmentSupplies/ProfessionalDentalEquipmentSupplies": "11234", "Health/MedicalDevicesEquipmentSupplies/BathroomSafetyEquipment": "11765", "Health/MedicalDevicesEquipmentSupplies/HospitalBedsStretchersAccessories": "11766", "Health/MedicalDevicesEquipmentSupplies/MedicalCompressionGarmentsStockings": "11767", "Health/MedicalDevicesEquipmentSupplies/ReachersGrabbers": "11768", "Health/MedicalDevicesEquipmentSupplies/ProstheticsArtificialLimbs": "11769", "Health/MedicalDevicesEquipmentSupplies/OrthopedicBracesSupports": "11771", "Health/Pharmacy/VitaminsSupplements": "11772", "Health/Pharmacy/DrugsMedications": "11773", "Health/HealthCareServices/WellnessTherapyMassage": "11793", "Health/HealthCareServices/BloodOrganDonationsTransplants": "12106", "Health/MedicalDevicesEquipmentSupplies/OphthalmologicalInstruments": "12481", "Health/MedicalDevicesEquipmentSupplies/PatientGownsGarments": "12487", "Health/MedicalDevicesEquipmentSupplies/ExamTablesSurgeryTables": "12488", "Health/MedicalDevicesEquipmentSupplies/SurgicalInstruments": "12490", "Health/MedicalDevicesEquipmentSupplies/MedicalScrubs": "12491", "Health/MedicalDevicesEquipmentSupplies/MedicalEquipmentPartsAccessories": "12492", "Health/MedicalDevicesEquipmentSupplies/MedicalDiagnosticEquipmentMonitoringDevices": "12494", "Health/MedicalDevicesEquipmentSupplies/UtilityMedicalEquipment": "12495", "Health/MedicalDevicesEquipmentSupplies/Defibrillators": "13167", "Health/MedicalDevicesEquipmentSupplies/InfusionPumps": "13169", "Health/HealthConditionsConcerns/BleedingDisorders": "13358", "Health/HealthConditionsConcerns/GeneticDisorders": "13362", "Health/BiotechPharmaceutical/GeneResearch": "13369", "Health/HealthCareServices/SexReassignmentTherapy": "13452", "Health/HealthConditionsConcerns/ThyroidConditions": "13745", "Finance/Insurance/BusinessCommercialInsurance": "10303", "Finance/FinancialPlanningManagement/MoneyManagement": "10524", "Finance/FinancialPlanningManagement/InheritanceEstatePlanning": "10525", "Finance/FinancialPlanningManagement/WealthManagement": "10526", "Finance/FinancialPlanningManagement/RetirementPensionPlanning": "10527", "Finance/Investing/RealEstateInvestments": "10531", "Finance/GrantsScholarshipsFinancialAid/StudyGrantsScholarships": "10535", "Finance/GrantsScholarshipsFinancialAid/StudentLoans": "10536", "Finance/Insurance/Annuities": "10538", "Finance/Insurance/LifeInsurance": "10539", "Finance/Insurance/HomeWarranties": "10540", "Finance/Insurance/DisabilityInsurance": "10541", "Finance/Insurance/PetInsurance": "10542", "Finance/Insurance/HealthInsurance": "10544", "Finance/Insurance/LiabilityInsurance": "10545", "Finance/Insurance/TravelInsurance": "10546", "Finance/Insurance/PropertyInsurance": "10547", "Finance/Insurance/DisasterInsurance": "10548", "Finance/Insurance/CreditInsurance": "10549", "Finance/Insurance/VehicleInsurance": "10550", "Finance/Insurance/AccidentCasualtyInsurance": "10551", "Finance/Banking/BillPaymentServices": "11811", "Finance/Banking/OffshoreBanking": "11812", "Finance/Banking/PrivateBanking": "11813", "Finance/Banking/OnlineBanking": "11814", "Finance/Banking/MobileBanking": "11815", "Finance/Banking/DebitCheckCards": "11816", "Finance/Banking/BankAccounts": "11817", "Finance/Banking/FixedDepositAccountsCertificates": "11818", "Finance/Investing/BrokeragesDayTrading": "11819", "Finance/Investing/InvestorRelationsVentureCapital": "11820", "Finance/Investing/Derivatives": "11823", "Finance/Investing/RetirementInvestments": "11824", "Finance/Investing/SpreadBetting": "11825", "Finance/Investing/ExchangeTradedFunds": "11826", "Finance/Investing/Securities": "11827", "Finance/Investing/MutualFunds": "11828", "Finance/Investing/CurrenciesForeignExchange": "11831", "Finance/Investing/CommoditiesFuturesTrading": "11832", "Finance/Investing/InterestFreeTaxDeferredSavingsAccounts": "11833", "Finance/Investing/PreciousMetalsTrading": "11834", "Finance/Investing/HedgeFunds": "11835", "Finance/CreditLending/IdentityTheftCreditMonitoring": "11836", "Finance/CreditLending/CreditCounseling": "11837", "Finance/CreditLending/CreditReportsReportingServices": "11838", "Finance/CreditLending/DebtManagementConsolidation": "11839", "Finance/CreditLending/Loans": "11841", "Finance/CreditLending/CreditCards": "11842", "Finance/AccountingAuditing/TaxPreparationPlanning": "12387", "Finance/AccountingAuditing/BillingServices": "12388", "Finance/AccountingAuditing/SarbanesOxleyComplianceManagement": "12389", "Finance/AccountingAuditing/Bookkeeping": "12391", "Finance/AccountingAuditing/CollectionServices": "12392", "Finance/AccountingAuditing/CashFlowManagement": "12393", "Finance/Insurance/RiskManagement": "12394", "Finance/Banking/StudentBanking": "12948", "Finance/GrantsScholarshipsFinancialAid/GovernmentGrants": "13437", "Finance/BusinessFinance/InvestmentBanking": "13717", "Finance/BusinessFinance/MergersAcquisitions": "13720", "ArtsEntertainment/TVVideo/MovieTVFanSites": "10553", "ArtsEntertainment/MoviesFilms/MovieTickets": "10554", "ArtsEntertainment/TVVideo/VideoClips": "10556", "ArtsEntertainment/TVVideo/EntertainmentDVDs": "10558", "ArtsEntertainment/TVVideo/DVDBluRayRetailers": "10561", "ArtsEntertainment/TVVideo/VideoOnDemand": "10562", "ArtsEntertainment/EventEntertainment/BachelorBacheloretteParties": "10563", "ArtsEntertainment/EventEntertainment/ChildrensParties": "10564", "ArtsEntertainment/EntertainmentIndustry/TalentPromotion": "10565", "ArtsEntertainment/MusicAudio/Radio": "10570", "ArtsEntertainment/MusicAudio/RecordLabels": "10588", "ArtsEntertainment/MusicAudio/SongLyricsTabs": "10589", "ArtsEntertainment/MusicAudio/MusicVideos": "10590", "ArtsEntertainment/MusicAudio/MusicStreamsDownloads": "10591", "ArtsEntertainment/MusicAudio/MusicalInstrumentsEquipment": "10592", "ArtsEntertainment/MusicAudio/ConcertsMusicFestivals": "10593", "ArtsEntertainment/MusicAudio/MusicAudioRetailers": "10595", "ArtsEntertainment/MusicAudio/MusicAudioCDs": "10596", "ArtsEntertainment/SportsEntertainment/SportScoresStatistics": "10597", "ArtsEntertainment/SportsEntertainment/FantasySports": "10598", "ArtsEntertainment/SportsEntertainment/SportsFanSitesBlogs": "10599", "ArtsEntertainment/EventsShowsCulturalAttractions/ExpoEventsShows": "10602", "ArtsEntertainment/EventsShowsCulturalAttractions/TicketSalesExchanges": "10603", "ArtsEntertainment/EventsShowsCulturalAttractions/EventShowGuidesListings": "10606", "ArtsEntertainment/SportsEntertainment/SportsEventTicketsTicketingServices": "11883", "ArtsEntertainment/EventsShowsCulturalAttractions/SpecialExhibits": "11885", "ArtsEntertainment/EventsShowsCulturalAttractions/TheaterTheaterTickets": "11886", "ArtsEntertainment/ComicsGraphicNovels/IndependentAlternativeComicBooks": "12001", "ArtsEntertainment/ComicsGraphicNovels/SuperheroComicBooks": "12002", "ArtsEntertainment/MusicAudio/MusicEducationInstruction": "13427", "ArtsEntertainment/ComicsGraphicNovels/ComicStrips": "13443", "ArtsEntertainment/FunTrivia/FunTestsSillySurveys": "13489", "ArtsEntertainment/EventEntertainment/WeddingEntertainment": "13492", "ArtsEntertainment/EventsShowsCulturalAttractions/FilmFestivals": "13498", "ArtsEntertainment/EventsShowsCulturalAttractions/ArtFairsFestivals": "13499", "ArtsEntertainment/EventsShowsCulturalAttractions/FoodBeverageEvents": "13500", "ArtsEntertainment/TVVideo/BluRayDiscs": "13511", "ArtsEntertainment/MoviesFilms/ActionAdventureFilms": "13515", "ArtsEntertainment/MoviesFilms/AnimatedFilms": "13518", "ArtsEntertainment/MoviesFilms/BollywoodSouthAsianFilm": "13519", "ArtsEntertainment/MoviesFilms/ClassicFilms": "13520", "ArtsEntertainment/MoviesFilms/CultIndieFilms": "13521", "ArtsEntertainment/MoviesFilms/DocumentaryFilms": "13522", "ArtsEntertainment/MoviesFilms/ComedyFilms": "13523", "ArtsEntertainment/MoviesFilms/DramaFilms": "13524", "ArtsEntertainment/MoviesFilms/HorrorFilms": "13525", "ArtsEntertainment/MoviesFilms/ScienceFictionFantasyFilms": "13526", "ArtsEntertainment/MoviesFilms/MusicalFilms": "13527", "ArtsEntertainment/MoviesFilms/MovieTheaters": "13528", "ArtsEntertainment/MoviesFilms/WarFilms": "13529", "ArtsEntertainment/MoviesFilms/FilmNoir": "13531", "ArtsEntertainment/MoviesFilms/FamilyChildrensFilms": "13532", "ArtsEntertainment/MoviesFilms/ThrillerCrimeMysteryFilms": "13533", "ArtsEntertainment/MoviesFilms/RomanceFilms": "13534", "ArtsEntertainment/MoviesFilms/SportsFilms": "13535", "ArtsEntertainment/EntertainmentIndustry/FilmTVIndustry": "13536", "ArtsEntertainment/MusicAudio/ClassicalMusic": "13538", "ArtsEntertainment/MusicAudio/CountryMusic": "13539", "ArtsEntertainment/MusicAudio/DanceElectronicMusic": "13540", "ArtsEntertainment/MusicAudio/ExperimentalIndustrialMusic": "13541", "ArtsEntertainment/MusicAudio/FolkTraditionalMusic": "13542", "ArtsEntertainment/MusicAudio/JazzBlues": "13543", "ArtsEntertainment/MusicAudio/PopMusic": "13546", "ArtsEntertainment/MusicAudio/ReligiousMusic": "13547", "ArtsEntertainment/MusicAudio/RockMusic": "13549", "ArtsEntertainment/MusicAudio/IndieAlternativeMusic": "13552", "ArtsEntertainment/MusicAudio/Soundtracks": "13555", "ArtsEntertainment/MusicAudio/UrbanHipHop": "13556", "ArtsEntertainment/MusicAudio/VocalsShowTunes": "13559", "ArtsEntertainment/MusicAudio/WorldMusic": "13560", "ArtsEntertainment/Offbeat/EdgyBizarre": "13567", "ArtsEntertainment/MusicAudio/SongwritersComposers": "13568", "ArtsEntertainment/ComicsGraphicNovels/Manga": "13570", "ArtsEntertainment/TVVideo/MovieTVReference": "13572", "ArtsEntertainment/EntertainmentIndustry/MusicRecordingIndustry": "13574", "ArtsEntertainment/VisualArtDesign/Painting": "13595", "ArtsEntertainment/SportsEntertainment/ProfessionalWrestling": "13635", "ArtsEntertainment/MusicAudio/LatinMusic": "13672", "ArtsEntertainment/MoviesFilms/Filmmaking": "13729", "ArtsEntertainment/EventEntertainment/Fireworks": "13751", "ArtsEntertainment/MusicAudio/ChildrensMusic": "13799", "ArtsEntertainment/TVVideo/DVDVideoRentals": "13824", "ArtsEntertainment/MusicAudio/VinylRecords": "13832", "ArtsEntertainment/EventsShowsCulturalAttractions/LiveComedy": "13852", "SportsFitness/SportsNewsMedia/FitnessMediaPublications": "10617", "SportsFitness/SportsFitnessApparel/WinterSportsApparel": "10618", "SportsFitness/SportsFitnessApparel/RunningApparel": "10621", "SportsFitness/SportsFitnessApparel/IceSkatingApparel": "10622", "SportsFitness/SportsFitnessApparel/CyclingApparel": "10623", "SportsFitness/SportsFitnessApparel/GymnasticsApparel": "10624", "SportsFitness/BoatingWaterRecreation/SurfingWindsurfingGear": "10626", "SportsFitness/BoatingWaterRecreation/BoatWatercraftPartsAccessories": "10627", "SportsFitness/BoatingWaterRecreation/Fishing": "10628", "SportsFitness/BoatingWaterRecreation/ScubaDiving": "10629", "SportsFitness/BoatingWaterRecreation/WaterSkiingEquipmentGear": "10630", "SportsFitness/SportsEquipmentRentalServices/SkiWinterSportsEquipmentRentalServices": "10631", "SportsFitness/SportsEquipmentRentalServices/BoatMarineVehicleRentalServices": "10632", "SportsFitness/SportingGoods/IceSkatingEquipment": "10633", "SportsFitness/SportingGoods/WinterSportsEquipment": "10634", "SportsFitness/SportingGoods/CricketEquipment": "10635", "SportsFitness/SportingGoods/RollerSkatingRollerbladingEquipment": "10636", "SportsFitness/SportingGoods/BasketballEquipment": "10637", "SportsFitness/SportingGoods/SportsUniforms": "10638", "SportsFitness/SportingGoods/TableTennisEquipment": "10639", "SportsFitness/SportingGoods/PaintballEquipment": "10640", "SportsFitness/SportingGoods/HuntingShootingEquipment": "10641", "SportsFitness/SportingGoods/SquashRacquetballEquipment": "10642", "SportsFitness/SportingGoods/BackyardGamesEquipment": "10643", "SportsFitness/SportingGoods/BaseballEquipment": "10644", "SportsFitness/SportingGoods/BicyclesAccessories": "10645", "SportsFitness/SportingGoods/LacrosseEquipment": "10646", "SportsFitness/SportingGoods/BowlingEquipment": "10647", "SportsFitness/SportingGoods/AirsoftEquipment": "10648", "SportsFitness/SportingGoods/RugbyEquipment": "10649", "SportsFitness/SportingGoods/TennisEquipment": "10651", "SportsFitness/SportingGoods/SwimmingAquaticSportsEquipment": "10652", "SportsFitness/SportingGoods/GolfEquipment": "10653", "SportsFitness/SportingGoods/TrackFieldEquipment": "10655", "SportsFitness/SportingGoods/ArcheryEquipment": "10656", "SportsFitness/SportingGoods/RunningWalkingEquipment": "10657", "SportsFitness/SportingGoods/SoccerEquipment": "10658", "SportsFitness/SportingGoods/SkateboardingEquipment": "10659", "SportsFitness/SportingGoods/GymnasticsEquipment": "10660", "SportsFitness/SportingGoods/AmericanFootballEquipment": "10661", "SportsFitness/SportingGoods/VolleyballEquipment": "10663", "SportsFitness/SportingGoods/EquestrianEquipmentTack": "10664", "SportsFitness/SportingGoods/HockeyRollerHockeyEquipment": "10665", "SportsFitness/Fitness/FitnessClassesInstruction": "10666", "SportsFitness/Fitness/FitnessEquipmentAccessories": "10667", "SportsFitness/Fitness/GymsGymMemberships": "10668", "SportsFitness/Fitness/PersonalTraining": "10669", "SportsFitness/SportsFanGearApparel/SportsTeamJerseys": "11901", "SportsFitness/SportsFanGearApparel/SportsTeamHatsCaps": "11902", "SportsFitness/SportsInstructionCoaching/SwimLessonsAquaticFitnessInstruction": "13366", "SportsFitness/SportsInstructionCoaching/HorsebackRidingLessons": "13367", "SportsFitness/SportsInstructionCoaching/GymnasticsLessonsClasses": "13482", "SportsFitness/SportsInstructionCoaching/DanceClassesLessons": "13483", "SportsFitness/Fitness/YogaPilates": "13486", "SportsFitness/SportingGoods/ExtremeSportsEquipment": "13571", "SportsFitness/Sports/AmericanFootball": "13606", "SportsFitness/Sports/Cricket": "13607", "SportsFitness/Sports/Soccer": "13608", "SportsFitness/Sports/Basketball": "13609", "SportsFitness/Sports/Baseball": "13610", "SportsFitness/Sports/Hockey": "13611", "SportsFitness/Sports/HuntingShooting": "13612", "SportsFitness/Sports/Rugby": "13613", "SportsFitness/Sports/Cycling": "13615", "SportsFitness/Sports/RunningWalking": "13616", "SportsFitness/Sports/SkateSports": "13617", "SportsFitness/Sports/SurfingWindsurfing": "13618", "SportsFitness/Sports/SwimmingAquaticSports": "13619", "SportsFitness/Sports/WinterSports": "13620", "SportsFitness/Sports/CollegeSports": "13621", "SportsFitness/Sports/ExtremeSports": "13623", "SportsFitness/Sports/MotorSports": "13624", "SportsFitness/Sports/WorldSportsEvents": "13625", "SportsFitness/Sports/Tennis": "13626", "SportsFitness/Sports/Golf": "13627", "SportsFitness/Sports/Bowling": "13628", "SportsFitness/Sports/Gymnastics": "13629", "SportsFitness/Sports/Volleyball": "13630", "SportsFitness/SportingGoods/CombatSportsEquipment": "13632", "SportsFitness/Sports/CombatSports": "13633", "SportsFitness/Sports/EquestrianSports": "13664", "SportsFitness/Sports/Rodeo": "13833", "SportsFitness/Sports/Cheerleading": "13839", "SportsFitness/Sports/Handball": "13870", "SportsFitness/Sports/TrackField": "13871", "HobbiesLeisure/CampingOutdoorRecreation/FlashlightsLanterns": "10612", "HobbiesLeisure/CampingOutdoorRecreation/PortableIceChestsPicnicCoolers": "10614", "HobbiesLeisure/CampingOutdoorRecreation/Climbing": "10615", "HobbiesLeisure/CampingOutdoorRecreation/Hiking": "10616", "HobbiesLeisure/ToysGames/Toys": "10671", "HobbiesLeisure/ToysGames/Games": "10672", "HobbiesLeisure/WineBeerCollectingBrewing/WineCollecting": "10674", "HobbiesLeisure/WineBeerCollectingBrewing/HomeBrewingWineMaking": "10675", "HobbiesLeisure/Cooking/Recipes": "10679", "HobbiesLeisure/PetsAnimals/PetFoodSupplies": "10683", "HobbiesLeisure/PetsAnimals/PetsByBreed": "10684", "HobbiesLeisure/PhotoVideo/PhotoVideoPublications": "10689", "HobbiesLeisure/PhotoVideo/DigitalCameras": "10691", "HobbiesLeisure/PhotoVideo/VideoCamerasCamcorders": "10692", "HobbiesLeisure/PhotoVideo/PhotoVideoAccessories": "10693", "HobbiesLeisure/PhotoVideo/FilmCameras": "10694", "HobbiesLeisure/AntiquesCollectibles/Figurines": "10699", "HobbiesLeisure/AntiquesCollectibles/Memorabilia": "10701", "HobbiesLeisure/AntiquesCollectibles/AntiqueClocksWatches": "10702", "HobbiesLeisure/AntiquesCollectibles/FolkArtEthnographicAntiques": "10703", "HobbiesLeisure/AntiquesCollectibles/GameRoomCollectibles": "10704", "HobbiesLeisure/AntiquesCollectibles/EuropeanAntiques": "10705", "HobbiesLeisure/AntiquesCollectibles/CollectibleTextilesLinen": "10706", "HobbiesLeisure/AntiquesCollectibles/VintageAdvertisingMedia": "10707", "HobbiesLeisure/AntiquesCollectibles/AntiqueMapsGeographicalArtifacts": "10708", "HobbiesLeisure/AntiquesCollectibles/VintageAntiqueButtons": "10711", "HobbiesLeisure/AntiquesCollectibles/CollectibleMetalWare": "10712", "HobbiesLeisure/AntiquesCollectibles/RocksMineralsFossils": "10713", "HobbiesLeisure/AntiquesCollectibles/CollectibleTelephonesTypewritersElectronics": "10714", "HobbiesLeisure/AntiquesCollectibles/GardenAntiques": "10715", "HobbiesLeisure/AntiquesCollectibles/AntiqueRugsCarpets": "10716", "HobbiesLeisure/AntiquesCollectibles/Militaria": "10717", "HobbiesLeisure/AntiquesCollectibles/CollectiblePins": "10718", "HobbiesLeisure/AntiquesCollectibles/AsianAntiques": "10719", "HobbiesLeisure/AntiquesCollectibles/CollectibleToysGames": "10720", "HobbiesLeisure/AntiquesCollectibles/CollectibleBricABrac": "10722", "HobbiesLeisure/AntiquesCollectibles/CollectibleHardwareTools": "10724", "HobbiesLeisure/AntiquesCollectibles/AntiqueCeramicsPottery": "10725", "HobbiesLeisure/AntiquesCollectibles/AntiqueLampsLighting": "10727", "HobbiesLeisure/AntiquesCollectibles/HolidaySeasonalCollectibles": "10728", "HobbiesLeisure/AntiquesCollectibles/TradingCards": "10729", "HobbiesLeisure/AntiquesCollectibles/AntiqueCoinsCollectibleMoney": "10730", "HobbiesLeisure/AntiquesCollectibles/CollectiblePostcards": "10733", "HobbiesLeisure/AntiquesCollectibles/CollectibleAutographs": "10734", "HobbiesLeisure/AntiquesCollectibles/FantasyMagicalCollectibles": "10736", "HobbiesLeisure/AntiquesCollectibles/VintageAntiqueTransportation": "10737", "HobbiesLeisure/AntiquesCollectibles/AntiqueBooksManuscripts": "10738", "HobbiesLeisure/AntiquesCollectibles/AntiqueGlasswareArtGlass": "10739", "HobbiesLeisure/RecreationalActivityEducation/RecreationalActivityLessonsClasses": "10740", "HobbiesLeisure/Cooking/Cuisines": "10889", "HobbiesLeisure/CampingOutdoorRecreation/Binoculars": "11312", "HobbiesLeisure/AntiquesCollectibles/AntiqueArtRefinishingRestoration": "11526", "HobbiesLeisure/Gardening/GardenOrnamentsStatuary": "11542", "HobbiesLeisure/Gardening/GardeningBooksPublications": "11544", "HobbiesLeisure/Gardening/YardGardenAccessories": "11545", "HobbiesLeisure/Cooking/CookingMediaPublications": "11559", "HobbiesLeisure/CampingOutdoorRecreation/Tents": "11887", "HobbiesLeisure/CampingOutdoorRecreation/CampCookingEquipment": "11888", "HobbiesLeisure/CampingOutdoorRecreation/BackpackingCampingFood": "11889", "HobbiesLeisure/CampingOutdoorRecreation/OutdoorSleepingGear": "11890", "HobbiesLeisure/PetsAnimals/PetGrooming": "11977", "HobbiesLeisure/PetsAnimals/PetSittingPetBoardingPetDayCare": "11978", "HobbiesLeisure/PetsAnimals/Veterinary": "11979", "HobbiesLeisure/PetsAnimals/PetBreeding": "11980", "HobbiesLeisure/PetsAnimals/PetAnimalTraining": "11981", "HobbiesLeisure/PetsAnimals/AnimalAdoptionRescue": "11982", "HobbiesLeisure/ArtsCrafts/BeadingBeadwork": "11983", "HobbiesLeisure/ArtsCrafts/CeramicGlassEquipmentSupplies": "11984", "HobbiesLeisure/ArtsCrafts/Scrapbooking": "11986", "HobbiesLeisure/ArtsCrafts/FiberCraft": "11987", "HobbiesLeisure/Birding/WildBirdSupplies": "11994", "HobbiesLeisure/AntiquesCollectibles/AntiqueAppraisals": "12007", "HobbiesLeisure/AntiquesCollectibles/AntiqueCollectibleAuctions": "12008", "HobbiesLeisure/PhotoVideo/PhotographyVideographyClasses": "12011", "HobbiesLeisure/Gardening/GardenFountainsWaterFeatures": "12776", "HobbiesLeisure/Gardening/GardenStructures": "12777", "HobbiesLeisure/PetsAnimals/DogWalking": "13015", "HobbiesLeisure/Gardening/TreesPlantsShrubs": "13374", "HobbiesLeisure/CampingOutdoorRecreation/MultitoolsPocketKnives": "13428", "HobbiesLeisure/PhotoVideo/PhotographicDigitalArts": "13596", "HobbiesLeisure/PrizesCompetitions/SweepstakesProductPromotionsGiveaways": "13680", "HobbiesLeisure/PrizesCompetitions/ContestsPageantsMeritPrizes": "13681", "HobbiesLeisure/AntiquesCollectibles/FleaMarketsSwapMeets": "13698", "HobbiesLeisure/CampingOutdoorRecreation/WaterContainersBottlesHydrationPacks": "13752", "HobbiesLeisure/Astronomy/NameAStar": "13796", "HobbiesLeisure/PhotoVideo/PhotoPrinting": "13802", "HobbiesLeisure/AntiquesCollectibles/AntiqueCollectibleStamps": "13820", "HobbiesLeisure/AntiquesCollectibles/SwordsKnivesDaggers": "13825", "HobbiesLeisure/ArtsCrafts/PaperCrafts": "13865", "HobbiesLeisure/PetsAnimals/Wildlife": "13878", "JobsEducation/JobsCareers/ResumeWriting": "10743", "JobsEducation/JobsCareers/CareerAssessments": "10744", "JobsEducation/JobsCareers/CareerEvents": "10745", "JobsEducation/JobsCareers/JobListings": "10746", "JobsEducation/JobsCareers/CareerCounselingCoaching": "10747", "JobsEducation/JobsCareers/ProfessionalNetworkingResources": "10748", "JobsEducation/JobsCareers/ProfessionalTradeAssociations": "10749", "JobsEducation/EducationTraining/OnlineEducationDegreePrograms": "10752", "JobsEducation/EducationTraining/PreschoolNurserySchoolPrograms": "10753", "JobsEducation/EducationTraining/StandardizedAdmissionsTests": "10754", "JobsEducation/EducationTraining/WritingCoursesResources": "10755", "JobsEducation/EducationTraining/LanguageEducation": "10757", "JobsEducation/EducationTraining/TrainingCertification": "10758", "JobsEducation/EducationTraining/VocationalTrainingTradeSchools": "10759", "JobsEducation/EducationTraining/CPRTrainingCertification": "10760", "JobsEducation/EducationTraining/TutoringServices": "10761", "JobsEducation/EducationTraining/StudyAbroadPrograms": "10762", "JobsEducation/EducationTraining/CollegesUniversitiesPostSecondaryEducation": "10763", "JobsEducation/EducationTraining/CommunityContinuingEducation": "10766", "JobsEducation/EducationTraining/TeachingClassroomResources": "10767", "JobsEducation/EducationTraining/PrimarySecondarySchoolingK12": "11029", "JobsEducation/EducationTraining/Textbooks": "11871", "JobsEducation/EducationTraining/SpecialEducation": "13438", "JobsEducation/EducationTraining/AcademicConferencesPublications": "13828", "JobsEducation/EducationTraining/PublicSpeakingResources": "13849", "TravelTourism/TravelMediaPublications/CityLocalGuides": "10149", "TravelTourism/Accommodations/HostelAccommodations": "10768", "TravelTourism/Accommodations/HotelsMotelsResorts": "10769", "TravelTourism/Accommodations/VacationRentals": "10770", "TravelTourism/Accommodations/CampingCaravanRVAccommodations": "10771", "TravelTourism/Accommodations/BedBreakfasts": "10772", "TravelTourism/Accommodations/SkiAccommodations": "10773", "TravelTourism/Accommodations/LastMinuteAccommodationDeals": "10774", "TravelTourism/Accommodations/AccommodationPackages": "10775", "TravelTourism/LuggageTravelAccessories/Luggage": "10776", "TravelTourism/LuggageTravelAccessories/TravelAccessories": "10777", "TravelTourism/AirTravel/AirlineTicketsFaresFlights": "10778", "TravelTourism/AirTravel/AirCharterCharterJetServices": "10779", "TravelTourism/TransportationExcursions/CarRentalServices": "10782", "TravelTourism/TransportationExcursions/BusRailServices": "10783", "TravelTourism/TransportationExcursions/TaxiServices": "10784", "TravelTourism/TransportationExcursions/AirportTransportationServices": "10785", "TravelTourism/TransportationExcursions/Ferries": "10786", "TravelTourism/TransportationExcursions/BoatYachtCharter": "10787", "TravelTourism/TransportationExcursions/TripPlannersRouteFinders": "10788", "TravelTourism/TransportationExcursions/LimousineServices": "10789", "TravelTourism/LastMinuteTravel/LastMinuteAirfaresFlights": "10790", "TravelTourism/TourOperators/CharterBusServices": "10791", "TravelTourism/TourOperators/SightseeingTours": "10792", "TravelTourism/SpecialtyTravel/AdventureTravel": "10793", "TravelTourism/SpecialtyTravel/Ecotourism": "10794", "TravelTourism/SpecialtyTravel/FamilyVacationsTravel": "10795", "TravelTourism/CruisesCruiseServices/LastMinuteCruises": "10800", "TravelTourism/CruisesCruiseServices/ActivityThemeBasedCruises": "10802", "TravelTourism/TravelMediaPublications/RoadMaps": "11872", "TravelTourism/TravelMediaPublications/TravelBooksGuides": "11874", "TravelTourism/TouristAttractionsDestinations/ZoosAquariums": "12080", "TravelTourism/TouristAttractionsDestinations/RegionalParksGardens": "12081", "TravelTourism/SpecialtyTravel/SinglesTravel": "12091", "TravelTourism/SpecialtyTravel/SeniorCitizenTravel": "12092", "TravelTourism/SpecialtyTravel/CouplesTravelHoneymoons": "12093", "TravelTourism/SpecialtyTravel/GLBTTravel": "12095", "TravelTourism/CruisesCruiseServices/BargeRiverCruises": "12096", "TravelTourism/TouristAttractionsDestinations/WinterTravelDestinations": "12098", "TravelTourism/TravelDocuments/PassportsPassportServices": "12117", "TravelTourism/TravelDocuments/TravelVisasVisaServices": "12118", "TravelTourism/TravelDocuments/TravelConsentForms": "12119", "TravelTourism/TransportationExcursions/ParkingServices": "13410", "TravelTourism/TouristAttractionsDestinations/ThemeParks": "13496", "TravelTourism/TouristAttractionsDestinations/Museums": "13497", "TravelTourism/TouristAttractionsDestinations/BeachesIslands": "13582", "TravelTourism/Accommodations/HouseSwapsHomeExchanges": "13806", "TravelTourism/TransportationExcursions/CarpoolingRidesharing": "13831", "TravelTourism/SpecialtyTravel/Agritourism": "13847", "TravelTourism/TouristAttractionsDestinations/HistoricalSitesBuildings": "13857", "TravelTourism/TouristAttractionsDestinations/LakesRivers": "13858", "TravelTourism/TouristAttractionsDestinations/Libraries": "13862", "LawGovernment/Legal/LegalFormsKits": "10161", "LawGovernment/LawEnforcementProtectiveServices/PrivateInvestigation": "10165", "LawGovernment/Politics/ConservativePolitics": "10805", "LawGovernment/Politics/LobbyingServices": "10806", "LawGovernment/Politics/PoliticalActivism": "10807", "LawGovernment/Politics/CampaignsElections": "10808", "LawGovernment/Politics/LiberalPolitics": "10809", "LawGovernment/EmergencyServices/EmergencyAlertServices": "10810", "LawGovernment/EmergencyServices/RoadsideAssistanceEmergencyRoadServices": "10811", "LawGovernment/EmergencyServices/EmergencyMedicalServices": "10813", "LawGovernment/EmergencyServices/SearchRescueServices": "10814", "LawGovernment/EmergencyServices/FireFightingServices": "10815", "LawGovernment/EmergencyServices/EmergencyTrainingServices": "10816", "LawGovernment/EmergencyServices/PoisonControl": "10817", "LawGovernment/Military/Army": "10818", "LawGovernment/Military/CoastGuard": "10819", "LawGovernment/Military/Navy": "10820", "LawGovernment/Military/AirForce": "10821", "LawGovernment/Military/Marines": "10822", "LawGovernment/Military/NationalGuard": "10823", "LawGovernment/PublicServices/AnimalControl": "10825", "LawGovernment/PublicServices/PublicBroadcasting": "10826", "LawGovernment/PublicServices/PublicHousing": "10827", "LawGovernment/PublicServices/PublicNotaries": "10828", "LawGovernment/PublicServices/PublicUtilities": "10829", "LawGovernment/LawEnforcementProtectiveServices/AirportSecurity": "10849", "LawGovernment/LawEnforcementProtectiveServices/TrafficSafety": "10850", "LawGovernment/LawEnforcementProtectiveServices/Police": "10851", "LawGovernment/Legal/ClassActionLaw": "10852", "LawGovernment/Legal/RealEstateLaw": "10853", "LawGovernment/Legal/MalpracticeSuitsLaw": "10855", "LawGovernment/Legal/LaborEmploymentLaw": "10856", "LawGovernment/Legal/ImmigrationLaw": "10857", "LawGovernment/Legal/CourtReporting": "10858", "LawGovernment/Legal/LegalAid": "10859", "LawGovernment/Legal/FinanceLaw": "10860", "LawGovernment/Legal/BusinessCorporateLaw": "10862", "LawGovernment/Legal/MilitaryLegalServices": "10863", "LawGovernment/Legal/BailBonds": "10864", "LawGovernment/Legal/LegalInsurance": "10865", "LawGovernment/Legal/AccidentPersonalInjuryLaw": "10866", "LawGovernment/Legal/LawsuitFunding": "10867", "LawGovernment/Legal/FamilyLaw": "10868", "LawGovernment/Legal/BankruptcyLaw": "10869", "LawGovernment/Legal/IntellectualProperty": "10871", "LawGovernment/Military/MilitaryEducationTraining": "13345", "LawGovernment/Military/MilitarySupportServices": "13350", "LawGovernment/PublicServices/PublicHealth": "13403", "LawGovernment/Government/Royalty": "13444", "LawGovernment/Government/GovernmentMinistries": "13508", "LawGovernment/Legal/LemonLaws": "13644", "LawGovernment/Legal/VehicleCodesDrivingLaws": "13646", "LawGovernment/Legal/AttorneysLawFirms": "13686", "LawGovernment/Legal/CriminalLaw": "13736", "LawGovernment/Government/EmbassiesConsulates": "13875", "LawGovernment/Government/StateLocalGovernment": "13876", "ComputersConsumerElectronics/ConsumerElectronics/ConsumerElectronicAccessories": "10872", "ComputersConsumerElectronics/ConsumerElectronics/HomeAudioVideo": "10873", "ComputersConsumerElectronics/ConsumerElectronics/WirelessDevices": "10876", "ComputersConsumerElectronics/ConsumerElectronics/PortableMediaDevices": "10877", "ComputersConsumerElectronics/ConsumerElectronics/GPSNavigation": "10880", "ComputersConsumerElectronics/ConsumerElectronics/CarAudioVideo": "10881", "ComputersConsumerElectronics/Computers/TechnologyNewsPublications": "10882", "ComputersConsumerElectronics/Computers/ComputerHardware": "10883", "ComputersConsumerElectronics/Computers/Software": "10885", "ComputersConsumerElectronics/Computers/ComputerAccessories": "10886", "ComputersConsumerElectronics/Computers/ComputerManufacturing": "11057", "ComputersConsumerElectronics/ConsumerElectronics/RadarDetectors": "11318", "ComputersConsumerElectronics/ConsumerElectronics/ConsumerElectronicWarrantyPlans": "12148", "ComputersConsumerElectronics/ConsumerElectronics/ConsumerElectronicDonation": "12149", "ComputersConsumerElectronics/ConsumerElectronics/ConsumerElectronicServiceRepair": "12150", "ComputersConsumerElectronics/ConsumerElectronics/ElectronicWasteDisposal": "12153", "ComputersConsumerElectronics/Computers/HostedDataStorage": "12156", "ComputersConsumerElectronics/Computers/ComputerRentals": "12196", "ComputersConsumerElectronics/Computers/TechnologySpecsReviewsComparisons": "12199", "ComputersConsumerElectronics/Computers/ComputerRepair": "12201", "ComputersConsumerElectronics/Computers/ComputerTechSupport": "12202", "ComputersConsumerElectronics/Computers/ComputerConsulting": "12203", "DiningNightlife/Restaurants/RestaurantTakeoutDelivery": "10887", "DiningNightlife/Restaurants/RestaurantReservationsBooking": "10888", "DiningNightlife/Restaurants/DineInRestaurants": "10890", "DiningNightlife/Restaurants/FastFoodRestaurants": "10891", "DiningNightlife/Restaurants/PizzeriasPizzaDelivery": "12228", "DiningNightlife/DiningNightlifeReviewsGuidesListings/BarClubNightlifeReviewsGuidesListings": "12974", "DiningNightlife/DiningNightlifeReviewsGuidesListings/RestaurantReviewsGuidesListings": "12975", "DiningNightlife/Restaurants/Bakeries": "13693", "Apparel/Footwear/FootwearAccessories": "10172", "Apparel/Footwear/ShoeRepairFootwearServices": "10173", "Apparel/Footwear/AthleticShoes": "10619", "Apparel/Jewelry/AntiqueJewelry": "10721", "Apparel/Jewelry/Rings": "10892", "Apparel/Jewelry/PreciousSemiPreciousGemsGemstoneJewelry": "10893", "Apparel/Jewelry/Bracelets": "10894", "Apparel/Jewelry/PreciousMetalJewelry": "10895", "Apparel/Jewelry/Cufflinks": "10896", "Apparel/Jewelry/PearlsPearlJewelry": "10897", "Apparel/Jewelry/WatchesWatchAccessories": "10899", "Apparel/Jewelry/Earrings": "10901", "Apparel/Jewelry/Anklets": "10902", "Apparel/Jewelry/Necklaces": "10903", "Apparel/Jewelry/BroochesPins": "10904", "Apparel/Clothing/CustomClothing": "10906", "Apparel/Jewelry/JewelryCleaningRepair": "10910", "Apparel/Clothing/PetiteClothing": "10912", "Apparel/Clothing/Underwear": "10913", "Apparel/Clothing/SuitsBusinessAttire": "10914", "Apparel/Clothing/Dancewear": "10916", "Apparel/Clothing/Outerwear": "10917", "Apparel/Clothing/UtilityClothing": "10918", "Apparel/Clothing/CulturallySpecificClothing": "10919", "Apparel/Clothing/FormalWear": "10920", "Apparel/Clothing/SocksHosiery": "10921", "Apparel/Clothing/PlusSizeClothing": "10923", "Apparel/Clothing/CostumesCostumeRental": "10924", "Apparel/Clothing/Sleepwear": "10925", "Apparel/Clothing/Uniforms": "10926", "Apparel/Footwear/Sandals": "10927", "Apparel/Footwear/Slippers": "10928", "Apparel/Footwear/RollerShoes": "10929", "Apparel/Footwear/CasualShoes": "10930", "Apparel/Footwear/Boots": "10931", "Apparel/Footwear/DressShoes": "10932", "Apparel/Footwear/Moccasins": "10933", "Apparel/Footwear/SpecialWidthOrthopedicShoes": "10934", "Apparel/ApparelAccessories/BeltsSuspenders": "10935", "Apparel/ApparelAccessories/Eyewear": "10936", "Apparel/ApparelAccessories/BagsPacks": "10937", "Apparel/ApparelAccessories/BillfoldsWallets": "10938", "Apparel/ApparelAccessories/GlovesMittens": "10939", "Apparel/ApparelAccessories/Ties": "10940", "Apparel/ApparelAccessories/Headwear": "10941", "Apparel/ApparelAccessories/HairAccessories": "10942", "Apparel/ApparelAccessories/ScarvesShawls": "10943", "Apparel/Clothing/Swimwear": "11917", "Apparel/Clothing/PulloverSweatersCardigans": "12237", "Apparel/Clothing/SweatshirtsHoodies": "12238", "Apparel/Clothing/Vests": "12239", "Apparel/Clothing/ShirtsTopsBlouses": "12240", "Apparel/Clothing/PantsJeansTrousers": "12241", "Apparel/Clothing/Shorts": "12242", "Apparel/Clothing/SportCoatsJackets": "12243", "Apparel/RainGear/RainUmbrellas": "13354", "Apparel/ApparelAccessories/ParasolsPersonalSunUmbrellas": "13409", "Apparel/ApparelAccessories/KeyChainsKeyRings": "13701", "Apparel/Clothing/MensClothing": "13778", "Apparel/Clothing/WomensClothing": "13779", "Apparel/Clothing/ChildrensClothing": "13780", "NewsMediaPublications/OnlineMedia/Podcasts": "10568", "NewsMediaPublications/OnlineMedia/Webcasts": "10569", "NewsMediaPublications/BooksLiterature/BookReviews": "10608", "NewsMediaPublications/BooksLiterature/BookRetailers": "10609", "NewsMediaPublications/BooksLiterature/EBooks": "10611", "NewsMediaPublications/MensInterestsMediaPublications/MensInterestsMediaPublicationsMature": "11868", "NewsMediaPublications/ApparelMediaPublications/FashionStylePublications": "11875", "NewsMediaPublications/ApparelMediaPublications/ApparelTradePublications": "11876", "NewsMediaPublications/Publishing/EBookPublishing": "12972", "NewsMediaPublications/Publishing/SelfPublishing": "12973", "NewsMediaPublications/ReferenceMaterialsResources/Dictionaries": "13429", "NewsMediaPublications/ReferenceMaterialsResources/Encyclopedias": "13430", "NewsMediaPublications/ReferenceMaterialsResources/Translation": "13432", "NewsMediaPublications/BooksLiterature/ChildrensBooks": "13445", "NewsMediaPublications/ReferenceMaterialsResources/GeographicReference": "13600", "NewsMediaPublications/BooksLiterature/Poetry": "13660", "NewsMediaPublications/OnlineMedia/OnlineImageGalleries": "13692", "NewsMediaPublications/ReferenceMaterialsResources/PublicRecords": "13716", "NewsMediaPublications/OnlineMedia/FlashBasedEntertainment": "13785", "NewsMediaPublications/BooksLiterature/AudioBooks": "13801", "NewsMediaPublications/ReferenceMaterialsResources/Quotations": "13861", "RetailersGeneralMerchandise/InformalSellingExchanging/GarageEstateYardSales": "13811"}}]