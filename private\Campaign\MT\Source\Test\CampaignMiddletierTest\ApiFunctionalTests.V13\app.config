﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <!-- CampaignAPI config -->
  <appSettings>
    <!--<add key="AdCenterEnvironment" value="LocalApps" />-->
    <add key="AdCenterEnvironment" value="##TestEnvironment##"/>
    <!--<add key="AdCenterEnvironment" value="SI-" />-->
    <!--<add key="AdCenterEnvironment" value="pace3_LDC2" />-->
    <!--<add key="AdCenterEnvironment" value="Wallace" />-->
    <!--<add key="AdCenterEnvironment" value="appsTest1Box" />-->
    <!--<add key="AdCenterEnvironment" value="apps-devdev" />-->
    <!-- Needed to determine the right campaign partition from the metadatadb -->
    <add key="DataCenterId" value="1"/>
    <!-- IT SHOULD BE SET TO FALSE FOR LOAD TESTS -->
    <add key="FindLogsForFailedTests" value="true"/>
    <add key="PrintLogsInConsole" value="true"/>
    <!-- Custom customerInfo setting-->
    <add key="UseCustomUserCredential" value="false"/>
    <add key="DefaultCustomerPartition" value=""/>
    <add key="UseShardGroupHistogramForCampaignDb" value="true"/>
    <add key="ReplaceBulkDownloadURL" value="true"/>
    <!-- Logger Configuration -->
    <!-- LogLevel Values: Info, Debug, Warn, Error, Fatal -->
    <add key="LogLevel" value="Info"/>
    <add key="LogToFile" value="false"/>
    <add key="LogFilePath" value="\\machine\share\log.txt"/>
    <!--0 – UserName (default),  1 – OAuth, 2 – SmallToken-->
    <add key="Authentication" value="0"/>
    <!--defaut value 1 sec-->
    <add key="ForceRefreshAccessTokenTimeLimit" value="1"/>
    <!--Update to valid share for BulkDownload BI Tests. Localpath when debugging locally and \\by2adck585\DownloadFile for pace and si -->
    <add key="ReportingShare" value=""/>
    <add key="MMC_API_Server" value="mmcapi.ads-int.microsoft.com" />
    <add key="WaitForCCMTCacheUpdateSec" value="15"/>
    <add key="FileDownloadRetryCount" value="10"/>
    <add key="FileDownloadRetryWaitInMilliSeconds" value="1000"/>
    <add key="CustomerCacheFolderName" value="Default"/>
    <add key="Clientcenter.UserSecurityClient.IsTestEnvironment" value="true"/>
    <add key="CampaignSecretsKeyVaultName-TEST" value="CampaignSecretsKVSI"/>
    <add key="CampaignSecretsKeyVaultName-TEST-CORP" value="CampaignSecretsKVCI"/>
    <add key="DefaultCustomerPilot" value=""/>
  </appSettings>
</configuration>